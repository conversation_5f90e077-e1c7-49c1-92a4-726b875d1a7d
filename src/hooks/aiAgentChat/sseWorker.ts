/*
 * @Author: 016645
 * @Description: SSE流式数据处理Worker
 * @Date: 2025-05-21 17:28:12
 */

// 添加空导出，使文件被视为模块
export {};

/**
 * 消息类型定义
 */
type WorkerMessage = {
  finalUrl: string;
  method: string;
  headers: Record<string, string>;
  isGetMethod: boolean;
  data: any;
  messageInterval?: number;
};

type PostMessageData = {
  type: 'onMessage' | 'onMessageBatch' | 'onEnd' | 'onError';
  data?: string;
  dataBatch?: string[];
  error?: string;
};

/**
 * 发送消息到主线程
 */
function sendMessage(data: PostMessageData) {
  self.postMessage(data);
}

/**
 * 发送错误消息
 */
function sendErrorMessage(error: unknown) {
  const errorMessage = error instanceof Error ? error.message : '流式读取错误';
  sendMessage({ 
    type: 'onError',
    error: errorMessage
  });
}

/**
 * 创建SSE连接
 */
async function createSseConnection(
  url: string, 
  method: string, 
  headers: Record<string, string>, 
  isGetMethod: boolean, 
  data: any
) {
  // eslint-disable-next-line compat/compat
  const response = await fetch(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      ...headers,
    },
    // 仅在非GET请求时设置body
    ...(!isGetMethod && { body: JSON.stringify(data) })
  });

  // 错误处理
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return response;
}

/**
 * 处理事件流
 */
async function processEventStream(response: Response, messageInterval: number) {
  // 获取读取器和解码器
  const reader = response?.body?.getReader();
  // eslint-disable-next-line compat/compat
  const decoder = new TextDecoder("utf-8");

  // 添加空值检查
  if (!reader) {
    throw new Error('无法获取响应流读取器');
  }

  // 初始化消息队列和控制变量
  let buffer = '';
  const messageQueue: string[] = [];
  // 创建对象以便能够通过引用共享状态
  const state = {
    isFlushingQueue: false 
  };
  
  // 常量定义
  const EVENT_SEPARATOR = '\n\n';
  const MESSAGE_INTERVAL = messageInterval; // 消息间隔(毫秒)，调整为100ms
  const MAX_BUFFER_SIZE = 100000; // 最大缓冲区大小(约100KB)
  
  // 定义消息处理函数（闭包，可访问外部变量）
  const processMessages = () => {
    if (state.isFlushingQueue || messageQueue.length === 0) return;
    
    state.isFlushingQueue = true;
    
    const processNextMessage = () => {
      if (messageQueue.length === 0) {
        state.isFlushingQueue = false;
        return;
      }
      
      const message = messageQueue.shift();
      if (message) {
        sendMessage({ 
          type: 'onMessage',
          data: message
        });
      }
      
      // 安排下一条消息的处理
      setTimeout(processNextMessage, MESSAGE_INTERVAL);
    };
    
    // 开始处理第一条消息
    processNextMessage();
  };
  
  // 等待队列为空的函数
  const waitForQueueEmpty = (): Promise<void> => {
    return new Promise(resolve => {
      const checkQueue = () => {
        if (messageQueue.length === 0 && !state.isFlushingQueue) {
          resolve();
        } else {
          setTimeout(checkQueue, 100);
        }
      };
      
      checkQueue();
    });
  };
  
  // 开始处理流数据
  try {
    let done = false;
    
    while (!done) {
      // 读取下一块数据
      const readResult = await reader.read();
      done = readResult.done;
      
      // 流结束处理
      if (done) {
        // 处理缓冲区中剩余的数据
        if (buffer.length > 0) {
          messageQueue.push(buffer);
          buffer = '';
        }
        
        // 确保所有消息都被处理
        processMessages();
        
        // 等待所有消息发送完毕
        await waitForQueueEmpty();
        
        // 发送流结束通知
        sendMessage({ type: 'onEnd' });
        break;
      }
      
      // 如果值为 undefined，则跳过
      if (!readResult.value) continue;
      
      // 解码二进制数据为文本
      const chunk = decoder.decode(readResult.value, { stream: true });
      buffer += chunk;
      
      // 查找完整的事件块（以\n\n分隔）
      let eventEndIndex;
      while ((eventEndIndex = buffer.indexOf(EVENT_SEPARATOR)) !== -1) {
        // 提取完整的事件块
        const eventData = buffer.substring(0, eventEndIndex + EVENT_SEPARATOR.length);
        // 更新缓冲区，移除已处理的事件
        buffer = buffer.substring(eventEndIndex + EVENT_SEPARATOR.length);
        
        // 将事件添加到消息队列
        messageQueue.push(eventData);
      }
      
      // 如果缓冲区过大，作为单独消息处理
      if (buffer.length > MAX_BUFFER_SIZE) {
        messageQueue.push(buffer);
        buffer = '';
      }
      
      // 确保消息队列处理开始（只调用一次）
      processMessages();
    }
  } finally {
    // 确保释放读取器锁
    reader.releaseLock();
  }
}

// Worker初始化
self.onmessage = async (event) => {
  // 解构请求参数
  const { finalUrl, method, headers, isGetMethod, data, messageInterval = 100 }: WorkerMessage = event.data;
  
  try {
    // 建立SSE连接
    const response = await createSseConnection(finalUrl, method, headers, isGetMethod, data);
    
    // 处理SSE数据流
    await processEventStream(response, messageInterval);
  } catch (err) {
    // 发送错误消息
    sendErrorMessage(err);
  }
};