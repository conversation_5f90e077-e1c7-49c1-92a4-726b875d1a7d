/* eslint-disable @typescript-eslint/naming-convention */
/*
 * @Author: 016645
 * @Description: 对话问答方法
 * @Date: 2025-03-25 09:28:00
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { getUrl, fetchData, fetchSSE, parseSSEDataCustom } from '../utils';
import { SendMessage } from '../types';

/**
 * 消息发送返回数据类型
 */
type Data = {
  code: string;
  msg: string;
  resultData: {
    conversationId?: string;
    messageId?: string;
    list: Array<{ type: string, content: object, taskId?: string }>;
    endFlag?: boolean;
    relatedQuestionList?: any;
    createTime?: any;
  };
}

/**
 * 发送消息并处理响应的自定义Hook
 * @param options - 配置选项
 * @returns 包含发送消息方法和状态的对象
 */
const useSendMessage = (initOptions: SendMessage) => {
  // 解构配置参数
  const { requestConfig, requestParams } = initOptions;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    url = '',          // 请求地址
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,           // 请求头
  } = requestConfig;
  
  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // 记录原始数据
  const rawDataRef = useRef<string>('');
  const cleanupRef = useRef<(() => void) | null>(null);
  
  // 获取完整url
  const fullUrl = getUrl(baseUrl, url);

  /**
   * 发送消息方法
   */
  const run = useCallback(async (customParams?: any) => {

    // 清理之前的请求
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }

    // 开始加载
    setLoading(true);
    setError(null);
    rawDataRef.current = '';

    // 解构请求参数
    const { 
      appId, 
      userId, 
      conversationId, 
      question, 
      questionExtends, 
      stream, 
      model, 
      enableInternetSearch,
      messageInterval = 30,
      channel,
      selectedAgents,
    } = Object.assign({}, requestParams, customParams);
    
    try {
      /**
       * todo
       * 如果不传conversationId,是先创建会话还是服务端内置?
       */
      // 初始化请求数据
      const initRequestData = {
        appId,
        userId,
        conversationId,
        headers,
        question,
        questionExtends,
        model,
        enableInternetSearch,
        messageInterval,
        channel,
        selectedAgents,
        // enable_internet_search: enableInternetSearch, // 用来兼容ip地址接口联调，实际上线不需要
        // conversation_id: conversationId, // 用来兼容ip地址接口联调，实际上线不需要
        // selected_agents: selectedAgents, // 用来兼容ip地址接口联调，实际上线不需要
      };
      
      // 应用请求转换器
      const requestData = requestTransfer 
        ? requestTransfer(initRequestData) 
        : initRequestData;
      
      // 根据stream参数处理流式/非流式请求
      if (stream) {
        // 流式请求处理
        const cleanup = fetchSSE({
          url: fullUrl,
          data: requestData,
          headers,
        }, {
          onMessage: (resultData) => {
            try {
              // 拼接sse接口字符串
              // rawDataRef.current += resultData;
              rawDataRef.current = resultData;
              const responseData = parseSSEDataCustom(rawDataRef.current, responseTransfer);
              
              if (!responseData) {
                setError(new Error('无法解析响应数据'));
                return;
              }
              
              setData({
                code: '0',
                msg: 'success',
                resultData: responseData,
              });
            } catch (err) {
              console.error('解析SSE数据时出错:', err);
              setError(err instanceof Error 
                ? err 
                : new Error('解析SSE数据时出错')
              );
            }
          },
          onEnd: () => {
            // 最后一次尝试解析，确保所有数据都被处理
            try {
              const finalResponseData = parseSSEDataCustom(rawDataRef.current, responseTransfer);
              if (finalResponseData) {
                setData({
                  code: '0',
                  msg: 'success',
                  resultData: {
                    ...finalResponseData,
                    endFlag: true, // 确保设置结束标志
                  },
                });
              }
            } catch (err) {
              console.error('最终解析SSE数据时出错:', err);
            }
            
            // 所有数据接收完毕后再设置loading为false
            setLoading(false);
            cleanupRef.current = null;
          },
          onError: (err) => {
            setError(err);
            setLoading(false);
            cleanupRef.current = null;
          }
        });

        // 保存清理函数
        cleanupRef.current = cleanup;
      } else {
        // 非流式请求处理
        const responseData = await fetchData({
          url: fullUrl,
          data: requestData,
          headers,
        });
        
        // 应用响应转换器并更新状态
        const result = responseTransfer 
          ? responseTransfer(responseData) 
          : responseData;
        
        setData(result);
        setLoading(false);
      }
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('发送消息失败')
      );
      setLoading(false);
      cleanupRef.current = null;
    }
  }, [
    fullUrl, 
    requestTransfer, 
    responseTransfer, 
    headers,
    requestParams
  ]);

  // 在组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
    };
  }, []);
  
  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useSendMessage;