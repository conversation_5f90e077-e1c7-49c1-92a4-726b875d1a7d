/*
 * @Author: 016645
 * @Description: 创建会话功能
 * @Date: 2025-03-20 10:05:43
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchData, getUrl, fetchMS } from '../utils';
import { CreateConversation } from '../types';

/**
 * 创建会话返回数据类型
 */
type Data = {
  code: string;
  msg: string;
  resultData: {
    conversationId: string;
  };
}

/**
 * 创建会话的自定义Hook
 * @param options - 配置选项
 * @returns 包含创建会话方法和状态的对象
 */
const useCreateConversation = (options: CreateConversation) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  const {
    baseUrl = '',      // 基础地址
    url = '',          // 请求地址
    type = 'http',     // 请求类型：http或tcp
    action = '',       // TCP请求action
    method = 'post',   // 请求方法
    paramsKey = '',    // 参数包裹的key
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,           // 请求头
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取完整url
  const fullUrl = getUrl(baseUrl, url);

  // 使用useRef存储一个稳定的run函数引用
  const stableRunRef = useRef<(params: any) => Promise<void>>();
  
  /**
   * 创建会话方法
   */
  const run = useCallback(async (customParams?: any) => {
    setLoading(true);
    setError(null);

    // 解构请求参数
    const { appId, userId } = Object.assign({}, requestParams, customParams);
    
    try {
      // 初始化请求数据
      const initRequestData = {
        appId,
        userId,
      };
      
      // 使用请求转换器（如果提供）
      const requestData = requestTransfer 
        ? requestTransfer(initRequestData) 
        : initRequestData;
      
      // 根据请求类型发送请求
      let responseData: any = null;
      
      if (type.toLowerCase() === 'tcp') {
        // TCP请求
        responseData = await fetchMS({
          action, 
          url: fullUrl, 
          method, 
          paramsKey, 
          data: requestData, 
          headers
        });
      } else {
        // HTTP请求
        responseData = await fetchData({
          url: fullUrl, 
          data: requestData, 
          headers
        });
      }
      
      // 应用响应转换器（如果提供）
      const result = responseTransfer 
        ? responseTransfer(responseData) 
        : responseData;
      
      // 更新数据状态
      setData(result);
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('创建会话失败')
      );
    } finally {
      // 完成后设置loading状态
      setLoading(false);
    }
  }, [
    fullUrl, 
    requestTransfer, 
    responseTransfer, 
    headers, 
    type, 
    method, 
    paramsKey, 
    action,
    requestParams
  ]);

  // 更新stableRunRef
  stableRunRef.current = run;

  // 非手动模式下自动执行(使用stableRunRef)
  useEffect(() => {
    if (!manual && stableRunRef.current) {
      stableRunRef.current(requestParams);
    }
  }, [manual, requestParams]); // 只依赖manual

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useCreateConversation;
