/*
 * @Author: 016645
 * @Description: hiAgent创建会话功能
 * @Date: 2025-03-20 10:05:43
 */
import { useState, useEffect, useCallback } from 'react';
import { fetchData } from '../utils';
import { CreateConversation } from '../types';

/**
 * 创建会话返回数据类型
 */
export type Data = {
  code: string;
  msg: string;
  resultData: {
    conversationId: string;
  };
}

/**
 * 创建会话的自定义Hook
 * @param options - 配置选项
 * @returns 包含创建会话方法和状态的对象
 */
const useCreateConversation = (options: CreateConversation) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取完整url
  const fullUrl = (`${baseUrl}/create_conversation`).replace('//', '/');

  /**
   * 创建会话方法
   */
  const run = useCallback(async (customParams?: any) => {
    setLoading(true);
    setError(null);

    // 解构请求参数
    const { 
      userId, 
      inputs,
      appKey,
    } = Object.assign({}, requestParams, customParams);
    
    try {
      // 初始化请求数据
      const initRequestData = {
        UserID: userId,
        Inputs: inputs,
        AppKey: appKey,
      };
      
      // 使用请求转换器（如果提供）
      const requestData = requestTransfer 
        ? requestTransfer(initRequestData) 
        : initRequestData;
      
      // 发送请求
      const responseData = await fetchData({
        url: fullUrl, 
        data: requestData,
        headers: { ...headers, Apikey: appKey },
      });
      
      // 标准格式转换
      const standardData = {
        code: '0',
        msg: '新会话创建成功',
        resultData: {
          conversationId: responseData?.Conversation?.AppConversationID,
        },
      };
      
      // 应用响应转换器（如果提供）
      const result = responseTransfer 
        ? responseTransfer(standardData) 
        : standardData;
      
      // 更新数据状态
      setData(result);
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('创建会话失败')
      );
    } finally {
      // 完成后设置loading状态
      setLoading(false);
    }
  }, [
    fullUrl, 
    requestTransfer, 
    responseTransfer, 
    requestParams,
    headers,
  ]);

  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useCreateConversation;
