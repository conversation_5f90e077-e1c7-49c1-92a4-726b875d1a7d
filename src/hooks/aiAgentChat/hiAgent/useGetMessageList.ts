/*
 * @Author: 016645
 * @Description: 获取消息列表功能
 * @Date: 2025-04-03 18:10:55
 */
import { useState, useEffect, useCallback } from 'react';
import { fetchData, traverseObject } from '../utils';
import { GetMessageList } from '../types';

/**
 * 消息列表返回数据类型
 */
export interface Data {
  code: string;
  msg: string;
  resultData: {
    noMore: boolean;
    list: Array<any> | [];
  };
}

/**
 * 获取消息列表的自定义Hook
 * @param options - 配置选项
 * @returns 包含获取消息列表方法和状态的对象
 */
const useGetMessageList = (options: GetMessageList) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,           // 请求头
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取完整url
  const fullUrl = (`${baseUrl}/get_conversation_messages`).replace('//', '/');

  /**
   * 获取消息列表方法
   */
  const run = useCallback(async (customParams?: any) => {
    setLoading(true);
    setError(null);

    // 解构请求参数
    const { 
      userId, 
      conversationId, 
      pageNum = 1, 
      pageSize = 10,
      appKey,
    } = Object.assign({}, requestParams, customParams);
    
    try {
      const limit = pageNum * pageSize;
      // 初始化请求数据
      const initRequestData = {
        UserID: userId,
        AppConversationID: conversationId,
        Limit: limit,
        AppKey: appKey,
      };
      
      // 使用请求转换器（如果提供）
      const requestData = requestTransfer 
        ? requestTransfer(initRequestData) 
        : initRequestData;
      
      // 发送请求
      let responseData: any = null;
      
      // HTTP请求
      responseData = await fetchData({
        url: fullUrl, 
        data: requestData, 
        headers: { ...headers, Apikey: appKey },
      });

      // 数据标准化处理
      const messages = responseData?.Messages || [];
      const messagesLength = messages?.length;
      const messageData: any = {
        noMore: true,
        list: [],
      }
      if(messagesLength > 0) {
        messageData.noMore = messagesLength < limit;
        const messageList: any[] = [];
        // 按照CreateTime升序排序
        messages.sort((a: any, b: any) => {
          const timeA = a?.AnswerInfo?.CreatedTime || 0;
          const timeB = b?.AnswerInfo?.CreatedTime || 0;
          return timeA - timeB;
        });
        messages.forEach((item: any) => {
          const answerInfo = item?.AnswerInfo || {};
          // 添加问题
          messageList.push({
            conversationId: item?.ConversationID,
            messageId: item?.QueryID,
            list: [
              {
                type: 'text',
                content: {
                  text: item?.Query,
                }
              }
            ],
            createTime: answerInfo?.CreatedTime * 1000,
            queryExtends: traverseObject(item?.QueryExtends || {}),
            role: 'user',
            needFeedback: false,
            feedbackResult: null,
          })
          
          // 添加回答
          const answerObj: any = {
            conversationId: item?.ConversationID,
            messageId: answerInfo?.MessageID,
            list: [
              {
                type: 'text',
                content: {
                  text: answerInfo?.Answer,
                }
              }
            ],
            createTime: answerInfo?.CreatedTime * 1000,
            role: 'assistant',
            needFeedback: true,
            totalTime: answerInfo?.Latency,
            totalTokens: answerInfo?.TotalTokens,
          };
          if(answerInfo?.Like === 1 || answerInfo?.Like === -1) {
            answerObj.feedbackResult = answerInfo?.Like === 1 ? 'good' : 'bad';
          }
          messageList.push(answerObj);
        });
        messageData.list = messageList;
      }
      
      const resultData = {
        code: '0',
        msg: '获取消息列表成功',
        resultData: messageData,
      }
      
      // 应用响应转换器（如果提供）
      const result = responseTransfer 
        ? responseTransfer(resultData) 
        : resultData;
      
      // 更新数据状态
      setData(result);
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('获取消息列表失败')
      );
    } finally {
      // 完成后设置loading状态
      setLoading(false);
    }
  }, [
    fullUrl, 
    requestTransfer, 
    responseTransfer, 
    requestParams,
    headers,
  ]);

  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useGetMessageList;