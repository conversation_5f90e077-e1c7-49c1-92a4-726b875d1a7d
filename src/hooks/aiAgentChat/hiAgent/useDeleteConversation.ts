import { useState, useEffect, useCallback } from 'react';
import { fetchData } from '../utils';
import { DeleteConversation } from '../types';

export interface Data {
  code: string;
  msg: string;
  resultData: {};
}

/**
 * 删除会话的自定义Hook
 * @param options - 配置选项
 * @returns 包含删除会话方法和状态的对象
 */
const useDeleteConversation = (options: DeleteConversation) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    headers,           // 请求头
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取完整url
  const fullUrl = (`${baseUrl}/delete_conversation`).replace('//', '/');

  /**
   * 删除会话方法
   */
  const run = useCallback(async (customParams?: any) => {
    setLoading(true);
    setError(null);

    // 解构请求参数
    const { userId, conversationId, appKey } = Object.assign({}, requestParams, customParams);
    
    try {
      // 初始化请求数据
      const initRequestData = {
        UserID: userId,
        AppConversationID: conversationId,
        AppKey: appKey,
      };
      
      // 使用请求转换器（如果提供）
      const requestData = requestTransfer 
        ? requestTransfer(initRequestData) 
        : initRequestData;
      
      // 根据请求类型发送请求
      let responseData: any = null;
      
      // HTTP请求
      responseData = await fetchData({
        url: fullUrl, 
        data: requestData, 
        headers: { ...headers, Apikey: appKey },
      });

      const resultData = {
        code: '0',
        msg: '删除会话成功',
        resultData: responseData,
      };
      
      // 应用响应转换器（如果提供）
      const result = responseTransfer 
        ? responseTransfer(resultData) 
        : resultData;

      // 更新数据状态
      setData(result);
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('删除会话失败')
      );
    } finally {
      // 完成后设置loading状态
      setLoading(false);
    }
  }, [
    fullUrl, 
    requestTransfer, 
    responseTransfer, 
    requestParams,
    headers,
  ]);

  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
};

export default useDeleteConversation;