/*
 * @Author: 016645
 * @Description: hiAgent重新生成回复
 * @Date: 2025-03-25 09:28:00
 */
import { useState, useEffect, useCallback } from 'react';
import { fetchSSE, parseSSEData } from '../utils';
import { SendMessageAgain } from '../types';

/**
 * 重新生成回复返回数据类型
 */
type Data = {
  code: string;
  msg: string;
  resultData: {
    conversationId?: string;
    messageId?: string;
    list: Array<{type: string, content: object, taskId?: string}>;
  };
}

/**
 * 重新生成回复并处理响应的自定义Hook
 * @param options - 配置选项
 * @returns 包含重新生成回复方法和状态的对象
 */
const useSendMessageAgain = (options: SendMessageAgain) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // 获取完整url
  const fullUrl = (`${baseUrl}/query_again`).replace('//', '/');

  /**
   * 发送消息方法
   */
  const run = useCallback(async (customParams?: any) => {
    setLoading(true);
    setError(null);
    let rawData = '';

    // 解构请求参数
    const { 
      userId, 
      conversationId, 
      messageId,
      appKey,
    } = Object.assign({}, requestParams, customParams);
    
    try {
    
      // 初始化请求数据
      const initRequestData = {
        UserID: userId,
        AppConversationID: conversationId,
        MessageID: messageId,
        AppKey: appKey,
      };
      
      // 使用请求转换器（如果提供）
      const requestData = requestTransfer 
        ? requestTransfer(initRequestData) 
        : initRequestData;

      // 流式请求处理
      await fetchSSE({
        url: fullUrl,
        data: requestData,
        headers: { ...headers, Apikey: appKey },
      }, {
        onMessage: (resultData) => {
          // 拼接sse接口字符串
          rawData += resultData;
          const responseData = parseSSEData(rawData, conversationId, responseTransfer);
          
          if (!responseData) return;
          
          setData({
            code: '0',
            msg: 'success',
            resultData: responseData,
          });
        },
        onEnd: () => {
          // 所有数据接收完毕后再设置loading为false
          setLoading(false);
        }
      });
      
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('发送消息失败')
      );
      
      // 发生错误时设置loading为false
      setLoading(false);
    }
  }, [
    fullUrl, 
    requestTransfer, 
    responseTransfer, 
    requestParams,
    headers,
  ]);

  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
}

export default useSendMessageAgain;