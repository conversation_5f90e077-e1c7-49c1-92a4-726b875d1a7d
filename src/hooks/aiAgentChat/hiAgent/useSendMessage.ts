/*
 * @Author: 016645
 * @Description: hiAgent对话问答方法
 * @Date: 2025-03-25 09:28:00
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchData, fetchSSE, parseSSEData } from '../utils';
import { SendMessage } from '../types';

/**
 * 消息发送返回数据类型
 */
type Data = {
  code: string;
  msg: string;
  resultData: {
    conversationId?: string;
    messageId?: string;
    list: Array<{ type: string, content: object, taskId?: string }>;
    endFlag?: boolean;
    relatedQuestionList?: any;
    createTime?: any;
  };
}

/**
 * 发送消息并处理响应的自定义Hook
 * @param options - 配置选项
 * @returns 包含发送消息方法和状态的对象
 */
const useSendMessage = (options: SendMessage) => {
  // 解构配置参数
  const { requestConfig, requestParams } = options;
  
  // 解构请求配置
  const {
    baseUrl = '',      // 基础地址
    requestTransfer,   // 请求转换器
    responseTransfer,  // 响应转换器
    manual = false,    // 是否手动触发
    headers,
    // enableInternetSearch = false,
  } = requestConfig;

  // 状态管理
  const [data, setData] = useState<Data | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // 记录原始数据
  const rawDataRef = useRef<string>('');
  const cleanupRef = useRef<(() => void) | null>(null);
  
  // 获取完整url
  const createConversationUrl = (`${baseUrl}/create_conversation`).replace('//', '/');
  const fullUrl = (`${baseUrl}/chat_query`).replace('//', '/');
  const fullUpdateUrl = (`${baseUrl}/update_conversation`).replace('//', '/');

  /**
   * 发送消息方法
   */
  const run = useCallback(async (customParams?: any) => {
    // 清理之前的请求
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
    
    setLoading(true);
    setError(null);
    rawDataRef.current = '';

    // 解构请求参数
    const { 
      userId, 
      conversationId: initConversationId, 
      question, 
      questionExtends,
      isFirstChat = false,
      stream,
      messageInterval = 30,
      inputs,
      appKey,
    } = Object.assign({}, requestParams, customParams);
    
    try {
      let newConversationId = initConversationId;
      
      // 如果没传会话id，则先创建会话
      if (!newConversationId) {
        const createConversationData = await fetchData({
          url: createConversationUrl,
          data: {
            AppKey: appKey,
            UserID: userId,
            Inputs: inputs,
          },
          headers: { ...headers, Apikey: appKey },
        });
        newConversationId = createConversationData?.Conversation?.AppConversationID;
      }

      // 如果isFirstChat为true，则更新会话
      if (isFirstChat) {
        await fetchData({
          url: fullUpdateUrl,
          data: {
            UserID: userId,
            AppConversationID: newConversationId,
            AppKey: appKey,
            Inputs: inputs,
            ConversationName: question,
          },
          headers: { ...headers, Apikey: appKey },
        });
      }

      // 如果文件存在,处理文件数据
      if ((questionExtends as any)?.files && (questionExtends as any)?.files?.length > 0) {
        (questionExtends as any).Files = (questionExtends as any).files.map((item: any) => {
          return {
            Path: item?.path,
            Name: item?.name,
            Size: item?.size,
            Url: item?.url,
          };
        });
        delete questionExtends.files;
      }

      // 初始化请求数据
      const initRequestData: any = {
        AppKey: appKey,
        UserID: userId,
        AppConversationID: newConversationId,
        Query: question,
        QueryExtends: questionExtends,
        ResponseMode: stream ? 'streaming' : 'blocking',
        messageInterval, // 严格控制消息间隔
      };

      // 使用请求转换器（如果提供）
      const requestData = requestTransfer 
        ? requestTransfer(initRequestData) 
        : initRequestData;
      
      // 根据stream参数处理流式/非流式请求
      if (stream) {
        // 流式请求处理
        const cleanup = fetchSSE({
          url: fullUrl,
          data: requestData,
          headers: { ...headers, Apikey: appKey },
        }, {
          onMessage: (resultData) => {
            try {
              // 拼接sse接口字符串并记录
              rawDataRef.current += resultData;
              
              // 解析SSE数据
              const responseData = parseSSEData(rawDataRef.current, newConversationId, responseTransfer);
              
              if (!responseData) {
                setError(new Error('无法解析响应数据'));
                return;
              }
              
              // 更新状态
              setData({
                code: '0',
                msg: 'success',
                resultData: responseData,
              });
            } catch (err) {
              console.error('解析SSE数据时出错:', err);
              setError(err instanceof Error 
                ? err 
                : new Error('解析SSE数据时出错')
              );
            }
          },
          onEnd: () => {
            // 最后一次尝试解析，确保所有数据都被处理
            try {
              const finalResponseData = parseSSEData(rawDataRef.current, newConversationId, responseTransfer);
              if (finalResponseData) {
                setData({
                  code: '0',
                  msg: 'success',
                  resultData: {
                    ...finalResponseData,
                    endFlag: true, // 确保设置结束标志
                  },
                });
              }
            } catch (err) {
              console.error('最终解析SSE数据时出错:', err);
            }
            
            // 所有数据接收完毕后再设置loading为false
            setLoading(false);
            cleanupRef.current = null;
          },
          onError: (err) => {
            setError(err);
            setLoading(false);
            cleanupRef.current = null;
          }
        });
        
        // 保存清理函数
        cleanupRef.current = cleanup;
      } else {
        // 非流式请求处理
        const sendMessageData = await fetchData({
          url: fullUrl,
          data: requestData,
          headers: { ...headers, Apikey: appKey },
          responseType: 'text',
        });
        
        // 标准格式转换
        const responseData = sendMessageData?.data;
        const resultList = typeof responseData?.parsedAnswer === 'string' 
          ? [{ 
              type: 'markdown', 
              content: {
                text: responseData?.parsedAnswer,
              }, 
              taskId: responseData?.task_id 
            }] 
          : responseData?.parsedAnswer;
        
        const standardData = {
          code: '0',
          msg: 'success',
          resultData: {
            conversationId: newConversationId,
            messageId: responseData?.id,
            list: resultList,
          },
        };
        
        // 应用响应转换器（如果提供）
        const result = responseTransfer 
          ? responseTransfer(standardData) 
          : standardData;
        
        // 更新数据状态
        setData(result);
        
        // 非流式请求这里直接设置loading为false
        setLoading(false);
      }
    } catch (err) {
      // 错误处理
      setError(err instanceof Error 
        ? err 
        : new Error('发送消息失败')
      );
      
      // 发生错误时设置loading为false
      setLoading(false);
      cleanupRef.current = null;
    }
  }, [
    createConversationUrl, 
    fullUrl, 
    fullUpdateUrl,
    requestTransfer, 
    responseTransfer,
    requestParams,
    headers,
  ]);

  // 在组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
    };
  }, []);
  
  // 非手动模式下自动执行
  useEffect(() => {
    if (!manual) {
      run(requestParams);
    }
  }, [manual, run, requestParams]);

  // 返回状态和方法
  return {
    data,
    loading,
    error,
    run,
  };
}

export default useSendMessage;