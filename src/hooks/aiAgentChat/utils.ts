/*
 * @Author: 016645
 * @Description: API请求和数据处理工具函数
 * @Date: 2025-03-20 10:48:56
 */
import { request as h5request } from '@ht/h5-utils';


const { request } = h5request;

/**
 * 构建完整URL
 * @param baseUrl - 基础URL
 * @param url - 路径或完整URL
 * @returns 格式化后的完整URL
 */
export function getUrl(baseUrl: string, url: string): string {
  // 检查 url 是否已经以 http:// 或 https:// 开头
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return baseUrl.replace(/\/+$/, '') + '/' + url.replace(/^\/+/, '');
}

/**
 * 将对象转换为URL查询字符串
 * @param query - 要转换的对象
 * @returns 格式化的查询字符串
 */
export function queryToString(query: Record<string, unknown>): string {
  return Object.keys(query)
    .map(
      (key) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(query[key] as any)}`
    )
    .join('&');
}

/**
 * 数据处理函数
 */

/**
 * 检查字符串是否为有效的JSON
 * @param str - 要检查的字符串
 * @returns 是否为有效JSON
 */
export function isValidJSON(str: string): boolean {
  if (typeof str !== 'string') return false;
  
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 解析SSE数据（hiAgent格式）
 * @param rawData - 原始SSE数据字符串
 * @param responseTransfer - 可选的响应转换器函数
 * @returns 解析后的数据对象
 * @throws 解析失败时抛出错误
 */
export const parseSSEData = (rawData: string, conversationId?: string, responseTransfer?: any) => {
  try {
    // 分离 event 和 data
    let result: any = {};
    const chunks = rawData.split('\n\n');
    
    chunks.forEach((chunk) => {
      const lines = chunk.split('\n');
      
      lines.forEach((line: string) => {
        if (line.startsWith('data:')) {
          try {
            // 只解析 data 部分的 JSON 字符串
            const jsonStr = line.slice(10).trim();
            
            if (jsonStr !== '') {
              if (isValidJSON(jsonStr)) {
                const resultData = JSON.parse(jsonStr);
                const finalResultData = responseTransfer 
                  ? responseTransfer(resultData) 
                  : resultData;
                
                const {
                  event,
                  conversation_id: initConversationId, 
                  task_id: taskId, 
                  id, 
                  answer 
                } = finalResultData;
                
                if (event === 'message' && answer) {
                  const answerStr = answer
                    //.replace(/\n/g, '')
                    // 给属性名添加双引号
                    .replace(/([{,]\s*)(\w+):/g, '$1"$2":')
                    // 处理可能的单引号
                    .replace(/'/g, '"')
                    // 移除对象或数组最后一个元素后的逗号
                    .replace(/,\s*([}\]])/g, '$1')
                    // 处理可能的多余空格
                    // .trim();
                  
                  result = {
                    conversationId: conversationId || initConversationId,
                    taskId,
                    messageId: id,
                    endFlag: false,
                    list: [...(result.list || []), {
                      taskId,
                      type: 'markdown',
                      content: {
                        text: answerStr,
                      },
                    }],
                  };
                } else if (event === 'message_cost') {
                  const {
                    input_tokens: inputTokens,
                    output_tokens: outputTokens,
                    latency,
                  } = finalResultData;
                  result = {
                    ...result,
                    totalTime: latency,
                    totalTokens: inputTokens + outputTokens,
                  }
                } else if (event === 'suggestion') {
                  const { suggested_questions: suggestedQuestions } = finalResultData;
                  const relatedQuestionList = suggestedQuestions.map((item: string) => ({
                    title: item,
                    content: item,
                    url: null
                  }))
                  result = {
                    ...result,
                    relatedQuestionList,
                  }
                } else if (event === 'message_end') {
                  result = {
                    ...result,
                    endFlag: true,
                  }
                }
              }
            }
          } catch (e) {
            console.error('解析 data 失败:', e);
          }
        }
      });
    });
    
    return result;
  } catch (err) {
    throw new Error(err instanceof Error ? err.message : 'SSE 数据解析错误');
  }
};

/**
 * 解析SSE数据（自定义格式）
 * @param rawData - 原始SSE数据字符串
 * @param responseTransfer - 响应转换器函数
 * @returns 解析后的数据对象
 * @throws 解析失败时抛出错误
 */
export const parseSSEDataCustom = (rawData: string, responseTransfer: any) => {
  try {
    // 分离 event 和 data
    let result: any = {};
    const chunks = rawData.split('\n\n');
    
    chunks.forEach((chunk) => {
      const lines = chunk.split('\n');
      
      lines.forEach((line: string) => {
        if (line.startsWith('data:')) {
          try {
            // 只解析 data 部分的 JSON 字符串
            const jsonStr = line.slice(5).trim();
            
            if (jsonStr !== '') {
              if (isValidJSON(jsonStr)) {
                const resultData = JSON.parse(jsonStr);
                const finalResultData = responseTransfer 
                  ? responseTransfer(resultData) 
                  : resultData;
                
                const { list, ...rest } = finalResultData;
                
                if (list) {
                  const currentList = list.map((item: any) => (
                    { ...item, taskId: finalResultData?.taskId }
                  ));
                  
                  result = {
                    ...rest,
                    list: [...(result.list || []), ...currentList],
                  };
                } else {
                  result = {
                    ...result,
                    ...rest,
                  };
                }
              }
            }
          } catch (e) {
            console.error('解析 data 失败:', e);
          }
        }
      });
    });
    
    return result;
  } catch (err) {
    throw new Error(err instanceof Error ? err.message : 'SSE 数据解析错误');
  }
};

/**
 * 请求函数
 */

/**
 * 网络请求接口选项
 */
interface FetchOptions {
  url: string;
  method?: string;
  data?: any;
  headers?: object | null | undefined;
}

/**
 * 发送标准HTTP请求
 * @param options - 请求选项
 * @returns 响应数据
 * @throws 请求失败时抛出错误
 */
export async function fetchData(options: FetchOptions & { responseType?: string }) {
  const { 
    url, 
    method = 'post', 
    data, 
    headers, 
    responseType = 'json' 
  } = options;
  
  // 处理GET请求，将参数转换为查询字符串并附加到URL
  const isGetMethod = method.toLowerCase() === 'get';
  const finalUrl = isGetMethod && data ? `${url}${url.includes('?') ? '&' : '?'}${queryToString(data)}` : url;
  
  // eslint-disable-next-line compat/compat
  const response = await fetch(finalUrl, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    // 仅在非GET请求时设置body
    ...(!isGetMethod && { body: JSON.stringify(data) })
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  switch (responseType) {
    case 'json':
      return response.json();
      
    case 'text':
      const sseText = await response.text();
      try {
        return parseSSEData(sseText);
      } catch (err) {
        console.error('SSE解析错误:', err);
        return { data: { text: sseText } };
      } 
      
    case 'blob':
      return response.blob();
      
    case 'formData':
      return response.formData();
      
    case 'arrayBuffer':
      return response.arrayBuffer();
      
    default:
      return response.json();
  }
}

/**
 * SSE回调选项接口
 */
interface SSECallbackOptions {
  onMessage?: (data: any) => void;
  onMessageBatch?: (dataBatch: any[]) => void;
  onEnd?: () => void;
  onError?: (error: Error) => void;
  onRetry?: (attempt: number, error: Error, delay: number) => void;
}

/**
 * 重试配置接口
 */
interface RetryConfig {
  maxRetries?: number;     // 最大重试次数
  initialDelay?: number;   // 初始延迟(毫秒)
  maxDelay?: number;       // 最大延迟(毫秒)
  backoffFactor?: number;  // 退避因子
  shouldRetry?: (error: Error) => boolean; // 判断是否应该重试的函数
}

/**
 * 获取绝对URL
 * @param url - 输入URL
 * @returns 绝对URL
 */
function getAbsoluteUrl(url: string): string {
  // 已经是绝对URL则直接返回
  if (url.match(/^https?:\/\//)) {
    return url;
  }
  
  // 获取当前页面的origin
  const origin = typeof window !== 'undefined' ? 
    window.location.origin : 
    'http://localhost'; // 默认值，实际应根据环境确定
  
  // 构建绝对URL
  return url.startsWith('/') ? 
    origin + url : 
    origin + '/' + url;
}

/**
 * 创建处理SSE数据的Worker
 * @returns Worker实例
 */
function createSSEWorker(): Worker {
  // 创建Worker内联代码
  const workerCode = String.raw`
    // SSE数据流处理Worker
    
    // 常量定义
    const EVENT_SEPARATOR = '\n\n'; // 事件分隔符
    const DATA_PREFIX = 'data:'; // 数据前缀
    const MAX_BUFFER_SIZE = 100000; // 最大缓冲区大小(100KB)
    
    // 状态跟踪
    let totalBytesReceived = 0;
    let totalMessagesExtracted = 0;
    let totalMessagesSent = 0;
    
    /**
     * 发送消息到主线程
     */
    function postToMain(type, payload = {}) {
      self.postMessage({ type, ...payload });
    }
    
    /**
     * 记录日志（调试用）
     */
    function logDebug(message, data) {
      if (data) {
        console.debug('[SSE Worker] ' + message, data);
      } else {
        console.debug('[SSE Worker] ' + message);
      }
    }
    
    /**
     * 高精度定时器 - 比setTimeout更精确
     * @param callback - 回调函数
     * @param interval - 时间间隔(ms)
     */
    function preciseTimeout(callback, interval) {
      const start = Date.now();
      const tick = () => {
        const elapsed = Date.now() - start;
        if (elapsed >= interval) {
          callback();
          return;
        }
        
        // 针对剩余时间调整检查频率
        const remaining = interval - elapsed;
        const nextCheck = remaining > 25 ? 25 : 1;
        setTimeout(tick, nextCheck);
      };
      
      setTimeout(tick, 0);
    }
    
    /**
     * 处理SSE数据流
     */
    async function processEventStream(reader, messageInterval) {
      const decoder = new TextDecoder("utf-8");
      
      // 初始化状态和缓冲区
      let buffer = '';
      const messageQueue = [];
      let isProcessing = false;
      let isStreamDone = false; // 跟踪流是否结束
      let endPromiseResolve = null;
      
      const endPromise = new Promise(resolve => {
        endPromiseResolve = resolve;
      });
      
      // 处理单条消息 - 严格按照messageInterval发送
      const processNextMessage = () => {
        if (messageQueue.length === 0) {
          isProcessing = false;
          
          // 如果队列为空且流已结束，发送流结束通知
          if (isStreamDone) {
            logDebug('所有消息处理完毕，发送结束通知。总接收: ' + totalBytesReceived + '字节, 提取: ' + totalMessagesExtracted + '条, 发送: ' + totalMessagesSent + '条');
            postToMain('onEnd');
            endPromiseResolve();
          }
          return;
        }
        
        isProcessing = true;
        const message = messageQueue.shift();
        
        // 发送消息到主线程
        postToMain('onMessage', { data: message });
        totalMessagesSent++;
        
        // 严格按照固定的时间间隔发送下一条消息
        setTimeout(processNextMessage, messageInterval);
      };
      
      // 启动消息处理
      const startProcessing = () => {
        if (isProcessing) return;
        
        if (messageQueue.length > 0) {
          processNextMessage();
        }
      };
      
      // 提取并添加单个事件到队列
      const extractAndAddEvent = (eventData) => {
        if (eventData && eventData.includes(DATA_PREFIX)) {
          messageQueue.push(eventData);
          totalMessagesExtracted++;
          startProcessing();
        }
      };
      
      // 处理数据流
      try {
        let done = false;
        
        while (!done) {
          const readResult = await reader.read();
          done = readResult.done;
          
          // 流结束处理
          if (done) {
            logDebug('数据流已结束，处理剩余数据');
            
            // 处理剩余数据
            if (buffer.length > 0) {
              // 作为最后一个消息处理
              extractAndAddEvent(buffer);
              buffer = '';
            }
            
            // 标记流已结束
            isStreamDone = true;
            
            // 确保所有消息被处理
            startProcessing();
            
            // 如果没有消息需要处理，直接发送结束通知
            if (!isProcessing && messageQueue.length === 0) {
              logDebug('没有剩余消息，直接发送结束通知');
              postToMain('onEnd');
              endPromiseResolve();
            } else {
              logDebug('等待队列中的 ' + messageQueue.length + ' 条消息处理完毕');
              // 否则等待结束Promise
              await endPromise;
            }
            
            // 退出循环
            break;
          }
          
          // 跳过空值
          if (!readResult.value) continue;
          
          // 处理新数据块
          const chunk = decoder.decode(readResult.value, { stream: true });
          buffer += chunk;
          totalBytesReceived += chunk.length;
          
          // 逐个提取事件并添加到队列 - 一次只提取一个事件
          let eventEndIndex;
          while ((eventEndIndex = buffer.indexOf(EVENT_SEPARATOR)) !== -1) {
            // 提取一个完整事件
            const eventData = buffer.substring(0, eventEndIndex + EVENT_SEPARATOR.length);
            buffer = buffer.substring(eventEndIndex + EVENT_SEPARATOR.length);
            
            // 逐个添加到队列
            extractAndAddEvent(eventData);
          }
          
          // 处理过大的缓冲区
          if (buffer.length > MAX_BUFFER_SIZE) {
            logDebug('缓冲区过大 (' + buffer.length + ' 字节)，作为单独消息处理');
            extractAndAddEvent(buffer);
            buffer = '';
          }
        }
      } finally {
        reader.releaseLock();
      }
    }
    
    /**
     * 创建并处理SSE连接
     */
    async function createAndProcessSSE(url, method, headers, isGetMethod, data, messageInterval) {
      try {
        // 发起请求
        logDebug('开始SSE请求: ' + url);
        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            ...headers,
          },
          ...(isGetMethod ? {} : { body: JSON.stringify(data) })
        });
        
        // 错误处理
        if (!response.ok) {
          throw new Error('HTTP error! status: ' + response.status);
        }
        
        const reader = response?.body?.getReader();
        if (!reader) {
          throw new Error('无法获取响应流读取器');
        }
        
        logDebug('开始处理事件流');
        // 开始处理事件流
        await processEventStream(reader, messageInterval);
        logDebug('事件流处理完成');
      } catch (error) {
        // 发送错误消息
        logDebug('处理SSE流时发生错误', error);
        postToMain('onError', { 
          error: error instanceof Error ? error.message : '流式读取错误' 
        });
      }
    }
    
    // Worker入口点
    self.onmessage = (event) => {
      const { finalUrl, method, headers, isGetMethod, data, messageInterval = 100 } = event.data;
      createAndProcessSSE(finalUrl, method, headers, isGetMethod, data, messageInterval);
    };
  `;
  
  // 创建Worker
  const workerBlob = new Blob([workerCode], { type: 'application/javascript' });
  // eslint-disable-next-line compat/compat
  return new Worker(URL.createObjectURL(workerBlob));
}

/**
 * 发送SSE流式请求
 * 使用Worker处理高负载数据，避免主线程阻塞
 * @param options - 请求选项
 * @param callbackOptions - 回调函数选项
 * @returns 清理函数，用于终止请求和释放资源
 */
export function fetchSSE(options: FetchOptions, callbackOptions: SSECallbackOptions): (() => void) {
  const { url, method = 'post', data, headers } = options;
  const { messageInterval = 100 } = data || {}; // 默认消息间隔为100ms
  
  // 是否启用调试日志
  const enableDebugLogs = false;
  
  // 调试日志函数
  const debugLog = (...args: any[]) => {
    if (enableDebugLogs) {
      console.log(`[SSE Debug ${new Date().toISOString()}]`, ...args);
    }
  };
  
  // 处理GET请求，将参数转换为查询字符串并附加到URL
  const isGetMethod = method.toLowerCase() === 'get';
  const finalUrl = isGetMethod && data ? `${url}${url.includes('?') ? '&' : '?'}${queryToString(data)}` : url;
  
  try {
    // 确保URL是绝对路径
    const absoluteUrl = getAbsoluteUrl(finalUrl);
    debugLog('开始SSE请求:', absoluteUrl);

    // 创建Worker处理SSE数据流
    const worker = createSSEWorker();
    
    // 跟踪Worker活动状态
    let isActive = true;
    let totalMessagesReceived = 0;
    let totalMessagesProcessed = 0;
    
    // 声明提前需要的函数引用
    let terminateWorker: () => void;
    let finalizeSseRequest: () => void;
    
    // 终止Worker并清理资源的函数
    terminateWorker = () => {
      if (isActive) {
        debugLog('终止SSE请求，清理资源');
        isActive = false;
        worker.terminate();
      }
    };
    
    // 完成SSE请求的函数 - 确保所有回调都执行后再清理资源
    finalizeSseRequest = () => {
      if (!isActive) return; // 防止重复调用
      
      debugLog(`请求完成。总接收: ${totalMessagesReceived}, 总处理: ${totalMessagesProcessed}`);
      
      // 调用onEnd回调
      try {
        callbackOptions.onEnd?.();
      } catch (err) {
        console.error('执行onEnd回调时出错:', err);
      }
      
      // 清理资源
      terminateWorker();
    };

    // 监听Worker返回的消息
    worker.onmessage = (event) => {
      // 如果请求已被终止，不再处理消息
      if (!isActive) return;
      
      const { type, data: sseData, error } = event.data;
      
      switch (type) {
        case 'onMessage':
          // 增加待处理消息计数和总接收计数
          totalMessagesReceived++;
          debugLog(`收到消息 #${totalMessagesReceived}`);
          
          try {
            // 直接处理消息，不使用队列或控制器
            // 这样可以确保消息处理与Worker中的发送频率保持一致
            callbackOptions.onMessage?.(sseData);
            totalMessagesProcessed++;
            debugLog(`消息处理完成 ${totalMessagesProcessed}/${totalMessagesReceived}`);
          } catch (err) {
            console.error('处理SSE消息时出错:', err);
          }
          break;
          
        case 'onEnd':
          debugLog('收到流结束事件');
          
          // 延迟一小段时间后完成请求，确保最后的UI更新
          setTimeout(() => {
            finalizeSseRequest();
          }, 50);
          break;
          
        case 'onError':
          if (error) {
            debugLog('收到错误:', error);
            const err = new Error(error);
            callbackOptions.onError?.(err);
          }
          break;
      }
    };
    
    // 设置错误处理
    worker.onerror = (err) => {
      console.error('Worker错误:', err);
      debugLog('Worker错误:', err.message);
      callbackOptions.onError?.(new Error(`Worker错误: ${err.message}`));
      terminateWorker();
    };
    
    // 发送初始化消息到Worker
    worker.postMessage({
      finalUrl: absoluteUrl,
      method,
      headers,
      isGetMethod,
      data,
      messageInterval,
    });
    
    debugLog('SSE请求已初始化');
    
    // 返回终止函数，允许用户手动终止请求
    return terminateWorker;
  } catch (err) {
    // 捕获fetch或流处理过程中的错误并向上抛出
    console.error('SSE请求初始化失败:', err);
    if (err instanceof Error) {
      throw err;
    } else {
      throw new Error('未知的SSE请求错误');
    }
  }
}

/**
 * 发送SSE流式请求（带重连重试功能）
 * @param options - 请求选项
 * @param callbackOptions - 回调函数选项
 * @param retryConfig - 重试配置
 * @returns 清理函数，用于终止请求和释放资源
 */
export function fetchSSEWithRetry(
  options: FetchOptions, 
  callbackOptions: SSECallbackOptions,
  retryConfig?: RetryConfig
): (() => void) {
  // 默认重试配置
  const {
    maxRetries = 3,
    initialDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    shouldRetry = () => true,
  } = retryConfig || {};

  let attempt = 0;
  let currentDelay = initialDelay;
  let cleanup: (() => void) | null = null;
  let isActive = true;

  // 包装回调函数，添加重试逻辑
  const wrappedCallbacks: SSECallbackOptions = {
    ...callbackOptions,
    onError: (err) => {
      // 先调用原始错误处理
      callbackOptions.onError?.(err);
      
      // 如果已经停止或达到最大重试次数，不再重试
      if (!isActive || attempt >= maxRetries || !shouldRetry(err)) {
        return;
      }
      
      // 计算重试延迟
      attempt++;
      currentDelay = Math.min(currentDelay * backoffFactor, maxDelay);
      
      // 通知即将重试
      callbackOptions.onRetry?.(attempt, err, currentDelay);
      
      // 延迟后重试
      setTimeout(() => {
        if (isActive) {
          // 清理旧的连接
          if (cleanup) {
            cleanup();
            cleanup = null;
          }
          
          // 重新连接
          cleanup = fetchSSE(options, wrappedCallbacks);
        }
      }, currentDelay);
    }
  };
  
  // 首次尝试连接
  try {
    cleanup = fetchSSE(options, wrappedCallbacks);
  } catch (error) {
    // 处理初始化错误
    const err = error instanceof Error ? error : new Error(String(error));
    wrappedCallbacks.onError?.(err);
  }
  
  // 返回清理函数
  return () => {
    isActive = false;
    if (cleanup) {
      cleanup();
    }
  };
}

/**
 * 站内请求接口选项
 */
interface MSFetchOptions {
  action: string;
  url: string;
  method?: string;
  paramsKey?: string;
  data?: any;
  headers?: any;
}

/**
 * 发送站内请求
 * @param options - 请求选项
 * @returns 响应数据
 * @throws 请求失败时抛出错误
 */
export async function fetchMS(options: MSFetchOptions) {
  const { action, url, method = 'post', paramsKey = 'params', data, headers } = options;
  
  try {
    const finalUrl = method === 'GET' ? `${url}?${queryToString(data)}` : url;
    const requestConfig: any = {
      action,
      path: finalUrl,
      ...headers,
    };
    
    // 参数包裹的key动态传入
    if (paramsKey) {
      requestConfig[paramsKey] = JSON.stringify(data);
    }
    
    const response = await request(requestConfig);
    return response;
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : `站内请求失败, ${url}`);
  }
}

/**
 * 对象属性名首字母转小写,支持多层级
 * @param obj - 对象
 * @returns 新对象
 */
export function traverseObject(obj: any) {
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const newKey = key.charAt(0).toLowerCase() + key.slice(1);
      
      if (Array.isArray(value) && value !== null) {
        obj[newKey] = value.map(item => {
          if (typeof item === 'object' && item !== null) {
            return traverseObject(item);
          }
          return item;
        });
      } else if (typeof value === 'object') {
        obj[newKey] = traverseObject(value);
      }else {
        obj[newKey] = value;
      }
      delete obj[key];
    }
  }
  return obj;
}