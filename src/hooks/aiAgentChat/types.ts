/*
 * @Author: 016645
 * @Description: 文件描述
 * @Date: 2025-03-20 17:28:12
 */

// 基础参数
export interface ChatOptions {
  requestConfig: {
    platform?: 'hiAgent'| string;
    baseUrl?: string;
    url?: string;
    type?: string | 'http';
    action?: string;
    method?: 'POST' | 'GET' | string;
    paramsKey?: string;
    requestTransfer?: (data: any) => any;
    responseTransfer?: (response: any) => any;
    manual?: boolean;
    headers?: Object;
  },
}

// 创建会话扩展参数
export interface CreateConversation extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    inputs?: Object;
  }
}

// 发送信息扩展参数
export interface SendMessage extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    inputs?: Object;
    conversationId?: string | undefined;
    question: string;
    questionExtends?: Object | Array<Object>;
    isFirstChat?: boolean;
    stream?: boolean; // 是否流式返回
    model?: string; // 模型
    enableInternetSearch?: boolean; // 是否启用互联网搜索, 暂不支持
    messageInterval?: number; // 返回给前端的数据帧间隔，单位毫秒
  }
}

// 重新生成回复
export interface SendMessageAgain extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    conversationId?: string | undefined;
    messageId?: string | undefined;
    stream?: boolean; // 是否流式返回
  }
}

// 删除会话扩展参数
export interface DeleteConversation extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    conversationId: string;
  }
}

// 获取会话列表扩展参数
export interface GetConversationList extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    scenario?: string;
  }
}

// 获取消息列表扩展参数
export interface GetMessageList extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    conversationId: string;
    pageNum: number;
    pageSize: number;
  }
}

// 停止消息扩展参数
export interface StopMessage extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    conversationId: string;
    messageId: string;
    taskId: string;
  }
}

// 反馈扩展参数
export interface Feedback extends ChatOptions {
  requestParams: {
    appKey?: string;
    appId?: string;
    userId: string;
    conversationId: string; 
    messageId: string;
    score: 'good' | 'bad';
    type: 'submit' | 'reset';
  }
}

// 敏感词校验扩展参数
export interface Sensitive extends ChatOptions {
  requestParams: {
    appKey?: string;
    question: string;
  }
}

export interface SSEResult {
  id?: string;
  event?: string;
  data?: string | {
    answer?: string;
    parsedAnswer?: string | object;
    task_id?: string;
  };
}