/**
 * 全量埋点配置
 */
import { isMobile } from './utils/canUse';

export interface ILogParams {
  id: string;
  page_id: string;
  page_title?: any;
  btn_title?: {
    [key: string]: any;
    useSendBeacon?: boolean;
  };
  btn_id?: string;
}

export const LogPointConfigMap = new Map([
  // HOME对话页
  [
    'handleStopAnswer',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_STOP_ANSWER',
      btn_title: {
        btn_label: '智能助手-停止生成',
      },
    },
  ],
  [
    'handleNewConversation',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_NEW_CONVERSATION',
      btn_title: {
        btn_label: '智能助手-开启新对话',
      },
    },
  ],
  [
    'handleFeedBackGood',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_UP',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点赞内容`,
      },
    },
  ],
  [
    'handleFeedBackBad',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_DOWN',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点踩内容`,
      },
    },
  ],
  [
    'handleFeedBackGoodCancel',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_UP',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-取消点赞内容`,
      },
    },
  ],
  [
    'handleFeedBackBadCancel',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_THUMBS_DOWN',
      btn_title: {
       btn_label: `${isMobile ? 'App': 'pc' }端Ai-取消点踩内容`,
      },
    },
  ],
  [
    'handleAnswerCopy',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_ANSWER_COPY',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-复制内容`,
        id: '', // 内容id
      },
    },
  ],
  [
    'handleRiskOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_RISK_OPEN',
      btn_title: {
        btn_label: '智能助手-查看风险提示',
      }
    },
  ],
  [
    'handleRiskClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_RISK_CLOSE',
      btn_title: {
        btn_label: '智能助手-关闭风险提示',
      },
    },
  ],
  [
    'handleRiskAgree',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_RISK_AGREE',
      btn_title: {
        btn_label: '智能助手-同意风险提示',
      },
    },
  ],
  [
    'toggleEnableInternetSearchOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_INTERNE_SEARCH_OPEN',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-开启联网搜索`,
      },
    },
  ],
  [
    'toggleEnableInternetSearchClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_PAGE_INTERNE_SEARCH_CLOSE',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-关闭联网搜索`,
      },
    },
  ],
  // 历史会话
  [
    'toggleHistoryOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_HISTORY_LIST_PAGE',
      page_title: '智能助手历史会话列表页',
      btn_id: 'AICHAT_HISTORY_LIST_PAGE_OPEN',
      btn_title: {
        btn_label: '智能助手-打开历史会话列表',
      },
    },
  ],
  [
    'toggleHistoryClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_HISTORY_LIST_PAGE',
      page_title: '智能助手历史会话列表页',
      btn_id: 'AICHAT_HISTORY_LIST_PAGE_OPEN',
      btn_title: {
        btn_label: '智能助手-关闭历史会话列表',
      },
    },
  ],
  [
    'selectHistoryConversation',
    {
      id: 'button_click',
      page_id: 'AICHAT_HISTORY_LIST_PAGE',
      page_title: '智能助手历史会话列表页',
      btn_id: 'AICHAT_HISTORY_LIST_PAGE_ITEM_OPEN',
      btn_title: {
        btn_label: '智能助手-查看历史会话',
        id: '', // 会话id
      },
    },
  ],
  [
    'toggleThinkCollapseOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_TOGGLE_THINK_COLLAPSE',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点击查看深度思考过程`,
        id: '', // 会话id
      },
    },
  ],
  [
    'toggleThinkCollapseClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_TOGGLE_THINK_COLLAPSE',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点击收起深度思考过程`,
        id: '', // 会话id
      },
    },
  ],
  [
    'toggleReferencesCollapseOpen',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_TOGGLE_REFERENCES_COLLAPSE',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点击查看参考来源`,
        id: '', // 会话id
      },
    },
  ],
  [
    'toggleReferencesCollapseClose',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_TOGGLE_REFERENCES_COLLAPSE',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点击收起参考来源`,
        id: '', // 会话id
      },
    },
  ],
  [
    'clickReference',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_CLICK_REFERENCE',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点击查看参考内容`,
        id: '', // 会话id
      },
    },
  ],
  [
    'companyDataClick',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_CLICK_COMPANYDATA',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-`,
        id: '', // 会话id
      },
    },
  ],
  [
    'companyDataInnerClick',
    {
      id: 'button_click',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_CLICK_COMPANYDATA_INNER',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-点击查看内部数据`,
        id: '', // 会话id
      },
    },
  ],
  [
    'referencePageView',
    {
      id: 'pageview',
      page_id: 'AICHAT_CONVERSATION_PAGE',
      page_title: '智能助手对话页',
      btn_id: 'AICHAT_CONVERSATION_CLICK_REFERENCE',
      btn_title: {
        btn_label: `${isMobile ? 'App': 'pc' }端Ai-查看参考内容`,
        id: '', // 会话id
        loadtype: 'appear', // 页面进入
      },
    },
  ],
]);