import React, { useState } from 'react';
import { LoadingSpinner } from '../LoadingSpinner';
import { LowcodeProps } from './interface';
import { getLowcodeUrl } from '../../utils/lowcode';
import CloseIcon from '../Navbar/images/close.svg';

const LCRender = window.LCRender || null;
export const Lowcode = (props: LowcodeProps) => {
  const {
    content,
    isDev,
    lowCodeConfig,
    userId,
  } = props;

  // 点击链接出来的
  const [showModal, setShowModal] = useState(false);
  // 魔方详情页面的链接
  const [prdtLowcodeUrl, setPrdtLowcodeUrl] = useState('');
  const [prdtLowcodeContent, setPrdtLowcodeContent] = useState<Record<string, any>>({});

  return (
    <>
      {LCRender && (<LCRender
        name="魔方卡片" // 魔方平台页面名称
        target={content?.url} // 魔方平台页面edit地址
        isDev={isDev}
        cardData={{ // TEC卡片的业务数据
          ...(content?.data.view ?? {}),
          _context: content?.data.context,
          _lowCodeDetailMeta: content?.data.lowCodeDetailMeta,
        }}
        px2remRootValue={lowCodeConfig?.rootValue} // rem布局 root节点值，用于转换魔方代码中的px单位，用于app适配
        mfRequestPrefixEnum={lowCodeConfig?.requestMobile} // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
        requestHandlerType={lowCodeConfig?.requestHandlerType} // 移动端需要重传该字段表示魔方卡片走站内tcp请求，不同app传入不同枚举值
        devicePixelRatio={lowCodeConfig?.devicePixelRatio} // 不同dpi设备适配用
        host={lowCodeConfig?.host} // 宿主，（会以此为判断用来隐藏详情页面再下钻）
        onDetailClick={() => { // 点击卡片上的详情下钻的回调函数，用于页面跳转
          if (content?.data?.lowCodeDetailMeta?.appId) {
            let jumpUrl = getLowcodeUrl(content?.data?.lowCodeDetailMeta?.appId, {
              sysId: "000045",// 鉴权使用
              userId,// 鉴权使用
              ...content?.data?.context
            });
            setShowModal(true);
            setPrdtLowcodeUrl(jumpUrl);
            setPrdtLowcodeContent(content ?? {})
          }
        }
        }
        renderLoading={() => { // 组件加载之前的loading状态
          return (
            <div className="LowcodeLoading">
              <LoadingSpinner />
              卡片加载中...
            </div>
          )
        }
        }
      />)
      }
      {showModal && (
        <div className="ModalWrap">
          <div
            className="ModalCloseWrap"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setShowModal(false);
            }} >
            <img src={CloseIcon} className="ModalCloseWrap-close" />
          </div>
          {LCRender && (<LCRender
            name="魔方卡片" // 魔方平台页面名称
            target={prdtLowcodeUrl} // 魔方平台页面edit地址
            isDev={isDev}
            px2remRootValue={lowCodeConfig?.rootValue} // rem布局 root节点值，用于转换魔方代码中的px单位，用于app适配
            mfRequestPrefixEnum={lowCodeConfig?.requestMobile} // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
            requestHandlerType={lowCodeConfig?.requestHandlerType} // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
            devicePixelRatio={lowCodeConfig?.devicePixelRatio} // 不同dpi设备适配用
            host={lowCodeConfig?.host} // 宿主，（会以此为判断用来隐藏详情页面再下钻）
            cardData={{ // TEC卡片的业务数据
              ...prdtLowcodeContent?.data?.context,
              ...prdtLowcodeContent?.data?.lowCodeDetailMeta,
              sysId: "000045", // 鉴权使用
              userId, // 鉴权使用
            }}
            // sysCode="AORTA"
            // sysId="000045"
            // sysName="聊TA平台"
            onDetailClick={(evt: Record<string, any>) => { // 点击卡片上的详情下钻的回调函数，用于页面跳转
              // 单纯setPrdtLowcodeUrl无法触发Modal重新渲染加载新卡片。所以先关一下modal再开
              setShowModal(false);
              const detailData = evt ?? prdtLowcodeContent?.data;
              if (detailData.lowCodeDetailMeta.appId) {
                let jumpUrl = getLowcodeUrl(detailData.lowCodeDetailMeta.appId, { sysId: "000045", userId, ...detailData.context });
                setPrdtLowcodeUrl(jumpUrl);
                setPrdtLowcodeContent({ data: detailData });
                setTimeout(() => {
                  setShowModal(true);
                }, 0);
              }
            }
            }
          />)}
        </div>
      )}
    </>
  );
};