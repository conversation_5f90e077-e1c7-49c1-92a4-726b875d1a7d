import React from 'react';
import { Bubble } from '../Bubble';
// import { LoadingSpinner } from '../LoadingSpinner';

export function Typing({ text = '思考中' }: { text?: string }) {
  return (
    <Bubble type="typing">
      <div className="TypingWrap">
        {/* <LoadingSpinner /> */}
        <div className="TypingText">
          {text}
          <span className="TypingEllipsis">
            <span className="TypingDot" />
            <span className="TypingDot" />
            <span className="TypingDot" />
          </span>
        </div>
      </div>
    </Bubble>
  );
}

