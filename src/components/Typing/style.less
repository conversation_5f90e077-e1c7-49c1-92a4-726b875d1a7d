.TypingWrap {
  display: inline-flex;

  .TypingText {
    margin-left: 10px;
    line-height: 24px;
  }

  .TypingEllipsis {
    display: inline-flex;
    align-items: center;
  }

  .TypingDot {
    width: 2px;
    height: 2px;
    border-radius: 50%;
    background-color: black;
    margin: 0 2px;
    animation: dotFade 3s infinite ease-in-out;
  }

  .TypingDot:nth-child(1) {
    animation-delay: 0s;
  }

  .TypingDot:nth-child(2) {
    animation-delay: 0.5s;
  }

  .TypingDot:nth-child(3) {
    animation-delay: 1s;
  }

  @keyframes dotFade {

    0%,
    16.67% {
      opacity: 1;
    }

    33.33%,
    50% {
      opacity: 0;
    }

    66.67%,
    100% {
      opacity: 1;
    }
  }
}
