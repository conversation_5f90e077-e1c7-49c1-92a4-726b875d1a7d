import {ReactNode} from 'react';

export interface TooltipTheme {
    background?: string;
    color?: string;
    arrowColor?: string;
    borderRadius?: string;
    boxShadow?: string;
}

export type TooltipProps = {
    content: ReactNode;
    placement?: Placement;
    children: ReactNode;
    mouseEnterDelay?: number;
    mouseLeaveDelay?: number;
    visible?: boolean;
    onVisibleChange?: (visible: boolean) => void;
    theme?: TooltipTheme;
    autoAdjust?: boolean;
}
