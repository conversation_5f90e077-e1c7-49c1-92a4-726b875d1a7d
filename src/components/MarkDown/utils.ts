
/***
 * 公式转义的预处理：对于复杂转义情况，预处理方案通常最可靠。
 * 统一使用语法，在内容源头就使用$$...$$和$...$
 * 注意：匹配多重转义时，一定要倒序，先匹配复杂的情况。
 */
export const preprocessContent = (content: string) => {
  if(!content) return '';
  return content
    // 块级
    .replace(/\\\\\[/g, '$$')
    .replace(/\\\\\]/g, '$$')
    .replace(/\\\[/g, '$$')  
    .replace(/\\\]/g, '$$')
   
    // 行内
    .replace(/\\\\\(/g, '$')
    .replace(/\\\\\)/g, '$')
    .replace(/\\\(/g, '$')
    .replace(/\\\)/g, '$')

    .replace(/<think>|<\/think>/g, '') // 将<think>和</think>替换成空字符串

    .replace(/\n{3,}/g, '\n\n'); // 压缩多个空行
    
  };