import React, { useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
// import remarkGemoji from "remark-gemoji";
import rehypeRaw from "rehype-raw";
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import MarkdownLink from '../MarkdownLink';
// import rehypeExternalLinks from 'rehype-external-links'; // 关键插件
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { coldarkCold } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { preprocessContent } from './utils';
// eslint-disable-next-line import/no-extraneous-dependencies
import 'katex/dist/katex.min.css';
// import 'github-markdown-css/github-markdown.css'; // 此css已经拷贝到style.less


export interface MarkDownProps {
  content: string;
  // 如果传了，打开链接使用传入的打开页面方法
  openPage?: (url: string) => void;
  // 是否解析外部链接（思考部分不解析）
  disableLink?: boolean;
}
export const MarkDown: React.FC<MarkDownProps> = (props) => {
  const { content, openPage, disableLink } = props;

  const handleCode = useCallback(({ children = [], className, ...restProps }) => {
    const match = /language-(\w+)/.exec(className || '')
    
    // 如果是数学公式块，直接返回原始内容
    if (className?.includes('math')) {
      return <code {...restProps}>{children}</code>
    }
    
    return (<SyntaxHighlighter
      language={match?.[1]}
      showLineNumbers={true}
      style={coldarkCold as any}
      PreTag='div'
      className='syntax-hight-wrapper'
      {...restProps}
    >
      {String(children).replace(/\n$/, '')}
    </SyntaxHighlighter>)
  }, []);

  // 处理链接解析
  const handleLink = useCallback(({ href, className, children }) => {
    // 如果不解析链接（思考内容部分不解析），解码为span
    if (disableLink) {
      return <span>{href && decodeURIComponent(href)}</span>
    }
    // 如果不是以http开头的链接，不解析为外部链接
    if (!href?.startsWith('http')) {
      // 优先展示方括号里面的网站名，没有的情况下解码链接
      return <span>{children ? children : (href && decodeURIComponent(href))}</span>
    }

    return (
      <MarkdownLink href={href} className={className} openPage={openPage}>
        {children}
      </MarkdownLink>
    )
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [disableLink, openPage]);

  return (
    <ReactMarkdown
      className='markdown-body'
      children={preprocessContent(content)}
      // remarkPlugins={[remarkGfm]}
      remarkPlugins={[[remarkGfm, { singleTilde: false }], remarkMath]}
      // remarkPlugins={[remarkGfm,remarkGemoji]}
      rehypePlugins={[
        rehypeRaw,
        // rehypeKatex,
        [rehypeKatex, { strict: false }], // 禁用严格模式（忽略非致命警告，减少控制台输出）
        // [rehypeExternalLinks, {
        //   target: '_blank', // 新开页面
        //   rel: ['noopener', 'noreferrer'], // 安全增强
        //   // 可选：仅处理外链
        //   // test: (node) => 
        //   //   node.properties?.href?.startsWith('http')
        // }],
      ]}
      components={{
        code: handleCode,
        a: handleLink,
      }}
    />
  );
};
