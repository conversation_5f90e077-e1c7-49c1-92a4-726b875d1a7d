import React, { useState, useEffect } from 'react';
import { MarkDown } from '../MarkDown';
import { LoadingSpinner } from '../LoadingSpinner';
import { ThinkContentProps } from './interface';
import { LogPointConfigMap } from '../../LogPointConfigMap';

import upSrc from './images/up.svg';
import downSrc from './images/down.svg';
import doneSrc from './images/done.svg';
export const ThinkContent = (props: ThinkContentProps) => {
  const {
    isThinking,
    stopped = false,
    thinkLinks = [],
    onReportLog,
    reMeasureSize,
  } = props;

  const [isThinkCollapsed, setIsThinkCollapsed] = useState(false);

  const toggleThinkCollapse = () => {
    // 埋点上报
    const params = LogPointConfigMap.get(isThinkCollapsed ? 'toggleThinkCollapseOpen' : 'toggleThinkCollapseClose');
    onReportLog?.(params);
    setIsThinkCollapsed(!isThinkCollapsed);
    // 通知虚拟列表重新测算尺寸
    if (reMeasureSize) {
      reMeasureSize();
    }
  }

  // 由思考中变成已思考完成，自动折叠
  useEffect(() => {
    if (isThinking === false && thinkLinks?.length > 0) {
      setIsThinkCollapsed(true);
      // 通知虚拟列表重新测算尺寸
      if (reMeasureSize) {
        reMeasureSize();
      }
    }
  }, [isThinking, reMeasureSize, thinkLinks?.length])

  if (thinkLinks?.length === 0) { return null; }

  return (
    <div className="ThinkContentWrap">
      {
        isThinking
          ? (
            <div className="Thinking">
              <div className="ThinkingText">思考中...</div>
            </div>
          )
          : (
            <div className="ThinkingCompleted" onClick={toggleThinkCollapse}>
              {stopped ? '思考已停止' : '已深度思考'}
              {isThinkCollapsed
                ? <img src={downSrc} className="CollapsedIcon" />
                : <img src={upSrc} className="CollapsedIcon" />
              }
            </div>
          )
      }
      {
        !isThinkCollapsed && thinkLinks?.map((item) => {
          if (!item) return null;
          return (
            <div className="ThinkContent" key={item?.title}>
              <div className="ThinkTitle">
                {item?.isThinking ? <LoadingSpinner /> : <img src={doneSrc} className="ThinkTitle-icon" />}
                <div className="ThinkTitle-text">{item?.title}</div>
              </div>
              <div className="ThinkMarkdown">
                <MarkDown content={item?.thinkContent || (isThinking ? '正在思考…' : '')} disableLink={true} />
              </div>
            </div>
          )
        })
      }
      {/* 非折叠的情况，思考完成展示答案生成节点 */}
      {
        !isThinkCollapsed && !isThinking && (
          <div className="ThinkContent" key="答案生成">
            <div className="ThinkTitle">
              <img src={doneSrc} className="ThinkTitle-icon" />
              <div className="ThinkTitle-text">答案生成</div>
            </div>
          </div>
        )
      }
    </div>
  );
};