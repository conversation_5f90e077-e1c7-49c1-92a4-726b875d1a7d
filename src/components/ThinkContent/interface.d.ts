// 思维链
export interface ThinkContentProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 思考中（整体是否在思考中，需每个agent思考结束）
   */
  isThinking?: boolean;
  /**
   * 各agent的思维链
   */
  thinkLinks: ThinkLinkProps[];
  /**
   * 是否停止生成
   */
  stopped?: boolean;
  /**
    * log上报回调
    */
  onReportLog?: (params: ILogParams | undefined) => void;
  // 通知虚拟列表重新测算尺寸
  reMeasureSize?: () => void;
}

// 思维链
export interface ThinkLinkProps {
  /**
  * 思考中(每一个agent是否在思考中)
  */
  isThinking?: boolean;
  /**
   * 思考内容
   */
  thinkContent: string;
  /**
  * 标题
  */
  title?: string;
}
