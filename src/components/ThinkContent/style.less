.ThinkContentWrap {
  font-size: @bubble-think-text-font-size;
  color: #888;
  line-height: 20px;
  margin-bottom: 12px;

  .Thinking {
    display: flex;
    align-items: center;

    .ThinkingText {
      font-size: 14px;
      color: #888;
      line-height: 20px;
      margin-right: 6px;
    }
  }

  .ThinkingCompleted {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    font-weight: bold;
    display: flex;
    align-items: center;

    .CollapsedIcon {
      margin-left: 4px;
      height: 14px;
      width: 14px;
    }
  }

  .ThinkContent {
    position: relative;
    background: @bubble-think-bg;

    .ThinkTitle {
      display: flex;
      align-items: center;
      padding: 2px 0;

      .LoadingSpinner {
        width: 16px;
        height: 16px;

        div {
          transform-origin: 8px 8px;

          &::after {
            top: 1px;
            left: 7px;
            height: 3px;
            background: #888;
          }
        }
      }

      &-icon {
        width: 16px;
        height: 16px;
      }

      &-text {
        font-size: 14px;
        color: #333;
        line-height: 20px;
        margin-left: 10px;
        font-weight: bold;
      }
    }

    .ThinkMarkdown {
      padding-top: 2px;
      padding-bottom: 10px;
      padding-left: 17px;
      margin-left: 8px;
      border-left: 1px solid rgba(0, 0, 0, 0.2);
    }

    .markdown-body {
      color: @bubble-think-color;
      font-size: @bubble-think-text-font-size;
    }

    // markdown无序列表
    .markdown-body ul {
      // list-style: disc !important;
      margin-left: 4px;

      &>li {
        list-style: disc !important;

      }
    }

    .markdown-body ol {
      margin-bottom: 12px;
    }
  }
}
