/**
 * 这是用react-file-viewer中的mammoth实现的word预览
 */
import React from 'react';

const Loading = () => {
  return (
    <div>
      <p>加载中...</p>
    </div>
  );
};
export default Loading;


// import React, { useEffect, useRef } from 'react';
// // @ts-ignore
// // eslint-disable-next-line import/no-extraneous-dependencies
// import mammoth from 'mammoth';
// import CloseIcon from '../Navbar/images/close.svg';

// interface DocxViewerProps {
//     fileUrl: string;
//     handleClose: () => void;
// }

// const DocxViewer: React.FC<DocxViewerProps> = ({ fileUrl,handleClose }) => {

//     const previewContainerRef = useRef<HTMLDivElement | null>(null);

//     useEffect(() => {
//         const jsonFile = new XMLHttpRequest();
//         jsonFile.open('GET', fileUrl, true);
//         jsonFile.send();
//         jsonFile.responseType = 'arraybuffer';
//         jsonFile.onreadystatechange = () => {
//             if (jsonFile.readyState === 4 && jsonFile.status === 200) {
//                 mammoth.convertToHtml(
//                     { arrayBuffer: jsonFile.response },
//                     { includeDefaultStyleMap: true },
//                 )
//                     .then((result: any) => {
//                         const docEl = document.createElement('div');
//                         docEl.className = 'document-container';
//                         docEl.innerHTML = result.value;
//                         if (previewContainerRef.current) {
//                             previewContainerRef.current.innerHTML = docEl.outerHTML;
//                         } else {
//                             console.error('previewContainerRef.current is null');
//                         }
//                     })
//                     .catch((a: any) => {
//                         console.log('alexei: something went wrong', a);
//                     })
//                     .done();
//             }
//         };
//     }, [fileUrl])


//     return (
//         <div className="WordViewWrap">
//             <div className='closeWrap'>
//                 <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
//             </div>
//             <div ref={previewContainerRef} className="WordContent">
//                 <div className="loading-container">
//                     <span className="loading" />
//                 </div>
//             </div>
//         </div>
//     );
// }

// export default DocxViewer;

