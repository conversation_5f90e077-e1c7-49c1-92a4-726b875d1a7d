/* eslint-disable compat/compat */
/* eslint-disable react-hooks/exhaustive-deps */
// PdfViewer.tsx
// import React, { useEffect, useRef, useState } from "react";
// import * as pdfjsLib from "pdfjs-dist";
// import "pdfjs-dist/build/pdf.worker.entry";
// import CloseIcon from './images/close.svg';

// export interface PdfViewerProps {
//   fileUrl: Blob | string; // 可以是 URL 或 Blob
//   scale?: number;
//   handleClose: () => void;
//   onLoad?: () => void;
//   onError?: (error: any) => void;
//   showPageIndicator?: boolean;
// }

// const PdfViewer: React.FC<PdfViewerProps> = ({
//   fileUrl,
//   scale = 2,
//   handleClose,
//   onLoad,
//   onError,
//   showPageIndicator = true,
// }) => {

//   const usePDFData = (options: { src: Blob | string, scale?: number }) => {
//     const previewUrls = useRef<string[]>([])
//     const urls = useRef<string[]>([])
//     const [loading, setLoading] = useState(true)

//     useEffect(() => {
//       urls.current = []
//       setLoading(true);
//       (async () => {
//         // 这里千万别解构，会导致 this 指向错误
//         try {
//           const data =
//             typeof fileUrl === "string"
//               ? await fetch(fileUrl).then(res => res.arrayBuffer())
//               : await fileUrl.arrayBuffer();

//           const pdfDocument = await pdfjsLib.getDocument({ data }).promise;
//           // const pdfDocument = await pdfjsLib.getDocument(options.src).promise
//           const task = new Array(pdfDocument.numPages).fill(null)
//           await Promise.all(task.map(async (_, i) => {
//             const page = await pdfDocument.getPage(i + 1)
//             const viewport = page.getViewport({ scale: options.scale || 2 })
//             const canvas = document.createElement('canvas')

//             canvas.width = viewport.width
//             canvas.height = viewport.height
//             const ctx = canvas.getContext("2d") as CanvasRenderingContext2D
//             const renderTask = page.render({
//               canvasContext: ctx,
//               viewport,
//             });
//             await renderTask.promise;
//             // 分别获取不同尺寸的图片，一个用来预览一个用来展示
//             urls.current[i] = canvas.toDataURL('image/jpeg', 2)
//             previewUrls.current[i] = canvas.toDataURL('image/jpeg', 0.5)
//           }))
//           setLoading(false);
//           onLoad?.();
//         } catch (error) {
//           setLoading(false);
//           onError?.(error);
//           console.error("加载 PDF 文件失败:", error);
//         }

//       })()
//       // eslint-disable-next-line react-hooks/exhaustive-deps
//     }, [options.src])

//     return {
//       loading,
//       urls: urls.current,
//       previewUrls: previewUrls.current,
//     }
//   }

//   const { loading, urls } = usePDFData({
//     src: fileUrl,
//     scale,
//   })

//   if (loading) {
//     return <div className="WordViewWrap">
//       <div className='closeWrap'>
//         <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
//       </div>
//       <div >loading...</div>
//     </div>
//   }
//   return (
//     <div className="WordViewWrap">
//       <div className='closeWrap'>
//         <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
//       </div>
//       <div className="PdfjsDistViewerContent" >
//         {urls?.map((item, i) => (
//           <React.Fragment key={item}>
//             <img
//               src={item}
//               className="PdfjsDistViewerImg"
//             />
//             {showPageIndicator &&
//               <span
//                 className="PdfjsDistViewerPage">第 {i + 1} 页 / 共 {urls?.length} 页</span>
//             }

//           </React.Fragment>
//         ))}
//       </div>
//     </div>
//   )

// };

import React from 'react';
import CloseIcon from './images/close.svg';

export interface PdfViewerProps {
  fileUrl: Blob | string; // 可以是 URL 或 Blob
  scale?: number;
  handleClose: () => void;
  onLoad?: () => void;
  onError?: (error: any) => void;
  showPageIndicator?: boolean;
}

const PdfViewer: React.FC<PdfViewerProps> = ({
  handleClose,
}) => {
  return (
    <div className="WordViewWrap">
      <div className='closeWrap'>
        <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
      </div>
      <div className="PdfjsDistViewerContent" >
        预览部分
      </div>
    </div>
  )
}

export default PdfViewer;
