/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable compat/compat */
/**
 * 这是用docx-preview实现的word预览
 * docx-preview不支持word文档页眉/页脚的渲染
 */

import React, { useEffect, useRef, useState } from "react";
// import { renderAsync } from "docx-preview";
import CloseIcon from './images/close.svg';
import { isMobile } from '../../utils/canUse';

type DocxPreviewerProps = {
    fileUrl: string;
    handleClose: () => void;
};

const WordPreviewByUrl: React.FC<DocxPreviewerProps> = ({ fileUrl, handleClose }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const scaleFontSize = (container: HTMLElement, scale = 2) => {
        const elements = container.querySelectorAll<HTMLElement>('[style*="font-size"]');
        elements.forEach(el => {
            const fontSizeStr = el.style.fontSize;
            if (!fontSizeStr) return;

            const match = fontSizeStr.match(/^([\d.]+)(pt|px)$/);
            if (match) {
                const value = parseFloat(match[1]);
                const unit = match[2];

                // 将 pt 或 px 放大
                const newValue = value * scale;
                el.style.fontSize = `${newValue}${unit}`;
            }
        });
    }

    const convertPtToPxStyle = (container: HTMLElement) => {
        const elements = container.querySelectorAll<HTMLElement>('[style*="width"], [style*="height"]');
        elements.forEach(el => {
            const style = el.style;
            const w = style.width?.match(/^([\d.]+)pt$/);
            if (w) {
                const pt = parseFloat(w[1]);
                style.width = `${(pt * 1.333 * 2).toFixed(2)}px`;
            }

            const h = style.height?.match(/^([\d.]+)pt$/);
            if (h) {
                const pt = parseFloat(h[1]);
                style.height = `${(pt * 1.333 * 2).toFixed(2)}px`;
            }

        })
    }

    useEffect(() => {
        let isCurrent = true;

        /* eslint-disable  */
        const loadDocx = async () => {
            setLoading(true);
            setError(null);

            try {
                const res = await fetch(fileUrl);
                if (!res.ok) throw new Error(`文档下载失败：HTTP ${res.status}`);
                const blob = await res.blob();

                const header = await blob.slice(0, 4).arrayBuffer();
                const bytes = new Uint8Array(header);

                // docx文件本质是一个ZIP压缩包
                // 看文件的前两个字节是不是ZIP格式的魔数
                // 0x50 = ASCII 'P', 0x4B = ASCII 'K'
                const isZip = bytes[0] === 0x50 && bytes[1] === 0x4B;

                if (!isZip) throw new Error("文件格式错误：不是有效的 docx 文件");

                if (isCurrent && containerRef.current) {
                    containerRef.current.innerHTML = "";
                    // await renderAsync(blob, containerRef.current);

                    if (isMobile) {
                        scaleFontSize(containerRef.current, 2);
                        convertPtToPxStyle(containerRef.current);
                    }

                    // // 调整文档内容宽度
                    // const adjustDocWidth = () => {
                    //     const container = containerRef.current as HTMLElement;
                    //     const content = container?.querySelector('.docx') as HTMLElement;
                    //     if (content) {
                    //         const containerWidth = container.offsetWidth;
                    //         const contentWidth = content.scrollWidth;
                    //         if (contentWidth > containerWidth) {
                    //             content.style.transform = `scale(1)`;
                    //             content.style.transformOrigin = 'left top';
                    //             content.style.width = '100%';
                    //             content.style.padding = '10px';
                    //         } else {
                    //             content.style.transform = 'scale(1)';
                    //             content.style.transformOrigin = 'left top';
                    //             content.style.width = '100%';
                    //         }
                    //     }
                    // }
                    // adjustDocWidth()
                    // window.addEventListener('resize', adjustDocWidth);

                    // // 强制调整图片的宽度
                    // const adjustImages = () => {
                    //     if (!containerRef.current) return;
                    //     const images = containerRef.current?.querySelectorAll("img");
                    //     images.forEach((img) => {
                    //         const parentDiv = img.parentNode as HTMLElement;
                    //         parentDiv.style.width = '100%';
                    //         img.style.maxWidth = "100%"; // 限制图片最大宽度
                    //         img.style.height = "auto"; // 保持图片比例
                    //         img.style.objectFit = "contain"; // 保持图片比例
                    //     });
                    // };

                    // setTimeout(adjustImages, 500); // 等待渲染完成后调整图片
                    // const images = containerRef.current?.querySelectorAll("img");
                    // images.forEach((img) => {
                    //     img.onload = adjustImages;
                    // })
                    // // 监听动态变化
                    // const observer = new MutationObserver(adjustImages);
                    // observer.observe(containerRef.current, {
                    //     childList: true,
                    //     subtree: true,
                    // });
                    // return () => { window.removeEventListener('resize', adjustDocWidth); observer.disconnect(); }
                }

            } catch (err: any) {
                setError(err.message || "文档加载失败");
                return;
            } finally {
                setLoading(false);
                return;
            }
        };

        loadDocx();
        return () => {
            isCurrent = false;
        }
    }, [fileUrl]);

    return (

        <div className="WordViewWrap">
            <div className='closeWrap'>
                <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
            </div>
            <div className="WordContent" >
                {loading && <div className="docx-previewer-loading" >loading...</div>}
                {error && <div className="docx-previewer-error"> {error}</div>}
                <div
                    ref={containerRef}
                    style={{
                        transform: 'scale(1)',
                        transformOrigin: '0 0'
                    }}
                >
                    {/* Word 文件内容会渲染到这里 */}
                </div>
            </div>

        </div>
    );
};

export default WordPreviewByUrl;
