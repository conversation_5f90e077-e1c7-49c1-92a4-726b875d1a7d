/* eslint-disable import/no-extraneous-dependencies */
import React, { useRef, useEffect, useState } from 'react';
// @ts-ignore：
// import FileViewer from 'react-file-viewer';
// import fileExpample from '../../assets/1922768.pdf';
import CloseIcon from '../Navbar/images/close.svg';

interface PdfViewerProps {
  fileUrl: string;
  handleClose: () => void;
  fileType: string;
}

const PdfByFileViewer: React.FC<PdfViewerProps> = ({ fileUrl, handleClose }) => {
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const [checkFileValid, setCheckFileValid] = useState<boolean | null>();

  // useEffect(() => {
  //   const adjustCanvasResolution = () => {
  //     if (previewContainerRef.current) {
  //       const canvases = previewContainerRef.current.querySelectorAll('.PDFContentWrap canvas');
  //       console.log(1111, canvases)
  //       canvases.forEach((canvas) => {
  //         const ctx = canvas.getContext('2d');
  //         const { width, height } = canvas;

  //         // 增加像素密度（提高分辨率）
  //         const scale = 2; // 或根据 devicePixelRatio 调整
  //         canvas.width = width * scale;
  //         canvas.height = height * scale;
  //         canvas.style.width = `${width}px`;
  //         canvas.style.height = `${height}px`;

  //         // 重绘内容
  //         ctx.scale(scale, scale);
  //         ctx.drawImage(canvas, 0, 0);
  //       });
  //     }
  //   };
  //   // 延迟执行，确保 Canvas 元素加载完成
  //   setTimeout(adjustCanvasResolution, 1000);
  // }, []);

  const fetchFileWithXHR = (url: string | URL) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';

    xhr.onload = function () {
      if (xhr.status === 200) {
        console.log('file loaded successfully:', xhr.response);
        setCheckFileValid(true);
      } else {
        console.error('Failed to load file, status:', xhr.status);
        setCheckFileValid(false);
      }
    };

    xhr.onerror = function () {
      console.error('Network or CORS error');
      setCheckFileValid(false);
      // alert('无法加载 PDF 文件，请检查文件链接或服务器配置。');
    };

    xhr.send();
  };

  useEffect(() => {
    // <FileViewer>的onError好像无法捕获CORS错误，所以用XHR的方式先尝试获取文件
    if (fileUrl) {
      fetchFileWithXHR(fileUrl);
    }
  }, [fileUrl])




  // const handleError = () => {
  //   console.log("出现错误", fileUrl);
  //   return (
  //     <div>
  //       <p>文件加载失败</p>
  //     </div>
  //   )
  // }

  // const ErrorComponent = () => (
  //   <div>
  //     <p>文件无法查看</p>
  //   </div>
  // );
  // const UnsupportedComponent = () => (
  //   <div>
  //     <p>文件不支持查看</p>
  //   </div>
  // );

  // if (!checkFileValid) {
  //   return (
  //     <div className="PDFWrap">
  //       <div className='closeWrap'>
  //         <img src={CloseIcon} className="PDFWrap-close" onClick={() => handleClose()} />
  //       </div>
  //       <div className='PDFContentWrap' ref={previewContainerRef}>
  //         <div>
  //           <p>文件加载失败</p>
  //         </div>

  //       </div>

  //     </div>
  //   )
  // } else {
  //   return (
  //     <div className="PDFWrap">
  //       <div className='closeWrap'>
  //         <img src={CloseIcon} className="PDFWrap-close" onClick={() => handleClose()} />
  //       </div>
  //       <div className='PDFContentWrap' ref={previewContainerRef}>
  //         <FileViewer
  //           className="FileContent"
  //           fileType={fileType}
  //           filePath={fileUrl}
  //           onError={handleError}
  //           errorComponent={ErrorComponent}
  //           unsupportedComponent={UnsupportedComponent}
  //         />
  //       </div>
  //     </div>

  //   );
  // }

  return (
    <div className="PDFWrap">
      <div className='closeWrap'>
        <img src={CloseIcon} className="PDFWrap-close" onClick={() => handleClose()} />
      </div>
      <div className='PDFContentWrap' ref={previewContainerRef}>
        {!checkFileValid ?
          <div>
            <p>文件加载失败</p>
          </div> :
          // <FileViewer
          //   className="FileContent"
          //   fileType={fileType}
          //   filePath={fileUrl}
          //   onError={handleError}
          //   errorComponent={ErrorComponent}
          //   unsupportedComponent={UnsupportedComponent}
          // />
          <div>文件预览窗口测试</div>
          }

      </div>

    </div>

  );
};

export default PdfByFileViewer;
