import React from 'react';
import CloseIcon from './images/close.svg';

interface NoSupportViewerProps {
    noSupportContent?: string;
    handleClose: () => void;
}

export const NoSupportViewer = ({ noSupportContent, handleClose }: NoSupportViewerProps) => {
    return (
        <div className="WordViewWrap">
            <div className='closeWrap'>
                <img src={CloseIcon} className="WordViewWrap-close" onClick={() => handleClose()} />
            </div>
            <div>{noSupportContent || '暂不支持预览该类型的文件'}</div>
        </div>
    );
};
