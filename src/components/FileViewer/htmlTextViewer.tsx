import React, { useRef } from 'react';
import CloseIcon from './images/close.svg';

interface HtmlTextViewerProps {
    textContent: string;
    title?: string;
    handleClose: () => void;
}
export const HtmlTextViewer = ({ textContent, title, handleClose }: HtmlTextViewerProps) => {
    const iframeRef = useRef<HTMLIFrameElement>(null);

    const isShowNoData = !textContent;
    const renderNoData = () => {
        return (
            <div className='noDatabox'>
                暂无数据
            </div>
        );
    }

    return (
        <div className="HtmlWrap">
            <div className='closeWrap'>
                <img src={CloseIcon} className="HtmlWrap-close" onClick={() => handleClose()} />
            </div>

            {!isShowNoData && (
                <iframe
                    ref={iframeRef}
                    // srcDoc={textContent}
                    src={textContent}
                    title={title || "HTML 预览"}
                    sandbox="allow-same-origin allow-scripts"
                    className='iframe'
                />
                // <div className='iframe' dangerouslySetInnerHTML={{ __html: textContent }} />
            )}
            {isShowNoData && renderNoData()}
        </div>
    )

}
