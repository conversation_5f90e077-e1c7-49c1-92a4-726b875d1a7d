@avatar-gap: 4px;

.Message {
  position: relative;

  &+& {
    margin-top: 30px;
  }

  &.left {
    .Message-main {
      &>.Avatar {
        margin-right: @avatar-gap;
      }
    }

    .Bubble {
      // background: #f7f7f7;
      background: @bubble-left-bg;
      padding-left: @bubble-left-padding-left;
      padding-right: @bubble-left-padding-right;
      padding-top: @bubble-left-padding-top;
      padding-bottom: @bubble-left-padding-bottom;

      &.card {
        margin-right: 0;
        overflow-x: auto;
        width: 100%;
        // background: #fff;
        background: @bubble-left-bg;

        .Feedback {
          padding: 15px;
        }

        .HallucinationMarker {
          padding-left: 15px;
        }
      }

      .markdown-body {
        color: @bubble-left-color;
      }
    }
  }

  &.right {

    .Message-main,
    .Message-content {
      flex-direction: row-reverse;
    }

    .Message-main {
      &>.Avatar {
        margin-left: @avatar-gap;
      }
    }

    .Message-author {
      text-align: right;
    }

    .Bubble {
      margin-left: 55px;
      border-radius: 12px;
      background: @bubble-right-bg;
      color: #333;
      padding-left: @bubble-right-padding-left;
      padding-right: @bubble-right-padding-right;
      padding-top: @bubble-right-padding-top;
      padding-bottom: @bubble-right-padding-bottom;

      .markdown-body {
        color: @bubble-right-color;
      }
    }

    .Unsend {
      background: linear-gradient(270deg, rgba(84, 182, 255, 0.5) 0%, rgba(0, 128, 255, 0.5) 46%, rgba(205, 142, 237, 0.5) 100%);
    }

  }

  &.pop {
    display: none;
  }
}

.Message-meta {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
  text-align: center;
}

.Message-main,
.Message-content {
  display: flex;
  align-items: flex-start;
}

.Message-main {
  padding: 0 15px 30px;
}

.Message-inner {
  flex: 1;
  min-width: 0;
}

.Message-stopped {
  font-size: 12px;
  color: #999;
  line-height: 16px;
  margin-top: 10px;
  padding-left: 15px;
}

.Message-sendError {
  font-size: 14px;
  color: #666;
  line-height: 19px;
  margin-top: 12px;
  display: flex;
  align-items: center;

  img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }
}

.Message-author {
  margin-bottom: 6px;
  color: @gray-2;
  font-size: @font-size-xs;
  line-height: 1.1;
}

// SystemMessage
.SystemMessage {
  padding: 0 15px;
  font-size: @font-size-xs;
  color: @gray-2;
  text-align: center;

  a {
    margin-left: 5px;
  }
}

.SystemMessage-inner {
  display: inline-block;
  padding: 6px 9px;
  border-radius: 6px;
  background: @gray-8;
  text-align: left;
}

.HrLine {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  height: 1px;
  padding: 0;
  margin: 20px 0;
  background-color: #e1e1e1;
  border: 0;

  &::before {
    display: table;
    content: "";
  }

  &::after {
    display: table;
    clear: both;
    content: "";
  }
}

.AgentNote {
  font-size: 12px;
  color: #ccc;
  line-height: 18px;
  margin: 12px 0;
}

.SubProblemTitle {
  margin: 15px 0 12px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  line-height: 24px;
}

.Reporter {
  font-size: 16px;
  margin: 15px 0;
  font-weight: bold;
}

.Message-traceId {
  margin-bottom: 20px;
}

.Message-traceIdUrl {
  font-size: 14px;
  font-weight: 400;
  color: #006BFF;
  line-height: 22px;
  cursor: pointer;
}

.Message-badIconBg {
  position: absolute;
  top: 0;
  right: 0;
  width: 63px;
  height: 61px;
}

.Message-goodIconBg {
  position: absolute;
  top: 0;
  right: 0;
  width: 56px;
  height: 54px;
}

.Message-badBubble {
  position: relative;
  margin-top: 10px;
  padding: 16px;
  background: linear-gradient(90deg, #FFFFFF 0%, #FAEDDE 100%);
  box-shadow: 0px 4px 8px 0px rgba(34, 55, 97, 0.1);
  border: 1px solid #D5B692;
  border-radius: 8px;
}

.Message-badBubble::before {
  content: '';
  position: absolute;
  top: -10px;
  /* 箭头高度(8px) + div边框(1px) × 2 */
  left: 70px;
  transform: translateX(-50%);
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-bottom: 9px solid #D5B692;
}

/* 白色箭头填充 */
.Message-badBubble::after {
  content: '';
  position: absolute;
  top: -8px;
  /* 箭头高度8px */
  left: 70px;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

.Message-goodBubble {
  position: relative;
  margin-top: 10px;
  padding: 16px;
  background: linear-gradient(90deg, #FFFFFF 0%, #DCE9FF 100%);
  box-shadow: 0px 4px 8px 0px rgba(34, 55, 97, 0.1);
  border: 1px solid #B1C2E4;
  border-radius: 8px;
}

.Message-goodBubble::before {
  content: '';
  position: absolute;
  top: -10px;
  /* 箭头高度(8px) + div边框(1px) × 2 */
  left: 40px;
  transform: translateX(-50%);
  border-left: 9px solid transparent;
  border-right: 9px solid transparent;
  border-bottom: 9px solid #B1C2E4;
}

/* 白色箭头填充 */
.Message-goodBubble::after {
  content: '';
  position: absolute;
  top: -8px;
  /* 箭头高度8px */
  left: 40px;
  transform: translateX(-50%);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
}

.Message-badResult {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #3F434B;
  line-height: 22px;
}

.Feedback-icon {
  width: 14px;
  height: 14px;
  margin-right: 14px;
}

.Message-badReason {
  display: flex;
  margin-left: 28px;
  margin-top: 10px;
}

.Message-badReasonName {
  font-size: 14px;
  font-weight: 400;
  color: #6C6F76;
  line-height: 22px;
}

.Message-badReasonText {
  max-width: 83%;
  text-wrap: wrap;
  font-size: 14px;
  font-weight: 400;
  color: #3F434B;
  line-height: 22px;
  word-break: break-all;
}

.AppReferencePopup {
  display: flex;
  position: absolute;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100vw;

  .popupMask {
    width: 100%;
    height: 100%;
    z-index: 999;
    background: #000000;
    opacity: 0.5;
  }

  .popupContainer {
    position: absolute;
    z-index: 1001;
    bottom: 0;
    width: 100%;
    min-height: 107px;
    height: auto;
    max-height: 67vh;
    background: #FFFFFF;
    border-radius: 12px 12px 0px 0px;
    padding-bottom: 20px;

    .containerHeader {
      box-sizing: border-box;
      width: 100%;
      // height: 75px;
      padding: 16px 15px;
      background: linear-gradient(180deg, #CFE1F4 0%, #FFFFFF 100%);
      border-radius: inherit;
      // border: 1px solid;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;

      .headerTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;

        font-size: 16px;
        font-weight: 600;
        color: #333333;
        line-height: 22px;
      }

      .closeIcon {
        width: 14px;
        height: 14px;
      }

      .headerSubTitle,
      .headerSubTitleDisable {
        display: flex;
        align-items: center;
        margin-top: 8px;
        font-size: 14px;
        font-weight: 400;

        line-height: 24px;
        position: relative;

        .titleText {
          // max-width: calc(100% - 14px);
          // overflow: hidden;
          // text-overflow: ellipsis;
          // white-space: nowrap;

          word-break: break-word;
          line-height: 24px;
          padding-right: 14px;
          /* 预留 corner 图标空间 */
        }

        .titleText.clamp {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          max-height: 48px;
        }
      }

      .headerSubTitle {
        cursor: pointer;
        color: #3E74F7;
      }

      .headerSubTitleDisable {
        color: #888;
      }

      .blueArrow {
        width: 14px;
        height: 14px;
        position: absolute;
        right: 0;
        bottom: 0;
      }

      .inlineblueArrow {
        width: 14px;
        height: 14px;
        vertical-align: text-bottom;
        margin-bottom: 2px;
      }

      .cornerblueArrow {
        position: absolute;
        right: 7px;
        bottom: 4px;
        width: 14px;
        height: 14px;
      }
    }

    .headerInstru {
      display: flex;
      align-items: center;
      height: 26px;
      margin-left: 15px;
      margin-right: 15px;
      margin-bottom: 17px;
      padding: 0 13px;
      background: linear-gradient(270deg, rgba(232, 244, 255, 0.5) 0%, #E8F4FF 100%);
      border-radius: 4px;

      font-size: 12px;
      font-weight: 400;
      color: #4B65A5;
      line-height: 17px;
    }

    .headerInstruIcon {
      width: 11px;
      height: 11px;
      margin-right: 8px;
    }

    .containerBody,
    .containerBodyToCust {
      box-sizing: border-box;
      height: auto;
      min-height: 0;

      padding: 10px 14px 20px 15px;
      overflow-y: auto;

      font-size: 14px;
      font-weight: 400;
      color: #4A4A4A;
      line-height: 24px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
        /* 轨道背景色 */
        // border-radius: 6px;
        /* 轨道圆角 */
      }

      &::-webkit-scrollbar-thumb {
        border: 3px solid #B9D1E9;
        width: 6px;
        height: 45px;
        background: #B9D1E9;
        border-radius: 3px;
      }

      .markdown-body {
        font-size: 14px;
        font-weight: 400;
        color: #4A4A4A;
        line-height: 24px;
      }
    }

    .containerBody {
      max-height: calc(67vh - 110px);
    }

    .containerBodyToCust {
      max-height: calc(67vh - 153px);
    }
  }

}

.PcReferencePopup,
.PcReferencePopupvisible {
  position: fixed;
  z-index: 9999;
  width: 375px;
  min-height: 54px;
  max-height: 50vh;
  background: #FFFFFF;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.3);
  border-radius: 12px;

  .containerHeader {
    box-sizing: border-box;
    width: 100%;
    // height: 94px;
    padding: 16px 15px;
    background: linear-gradient(180deg, #CFE1F4 0%, #FFFFFF 100%);
    border-radius: inherit;
    // border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;

    .headerTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;

      font-size: 16px;
      font-weight: 600;
      color: #333333;
      line-height: 22px;
    }

    .closeIcon {
      width: 14px;
      height: 14px;
    }

    .headerSubTitle,
    .headerSubTitleDisable {
      display: flex;
      align-items: center;
      margin-top: 8px;
      font-size: 14px;
      font-weight: 400;

      line-height: 24px;
      position: relative;

      .titleText {
        // max-width: calc(100% - 14px);
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;

        word-break: break-word;
        line-height: 24px;
        padding-right: 14px;
        /* 预留 corner 图标空间 */
      }

      .titleText.clamp {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: 48px;
      }
    }

    .headerSubTitle {
      cursor: pointer;
      color: #3E74F7;
    }

    .headerSubTitleDisable {
      color: #888;
    }

    .blueArrow {
      width: 14px;
      height: 14px;
      position: absolute;
      right: 0;
      bottom: 0;
    }

    .inlineblueArrow {
      width: 14px;
      height: 14px;
      vertical-align: text-bottom;
      margin-bottom: 2px;
    }

    .cornerblueArrow {
      position: absolute;
      right: 7px;
      bottom: 4px;
      width: 14px;
      height: 14px;
    }

  }

  .headerInstru {
    display: flex;
    align-items: center;
    height: 26px;
    margin-left: 15px;
    margin-right: 15px;
    margin-bottom: 17px;
    padding: 0 13px;
    background: linear-gradient(270deg, rgba(232, 244, 255, 0.5) 0%, #E8F4FF 100%);
    border-radius: 4px;

    font-size: 12px;
    font-weight: 400;
    color: #4B65A5;
    line-height: 17px;
  }

  .headerInstruIcon {
    width: 11px;
    height: 11px;
    margin-right: 8px;
  }

  .containerBody,
  .containerBodyToCust {
    box-sizing: border-box;
    height: auto;
    min-height: 0;
    padding: 0px 14px 10px 15px;
    overflow-y: auto;
    font-size: 14px;
    font-weight: 400;
    color: #4A4A4A;
    line-height: 24px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      /* 轨道背景色 */
      // border-radius: 6px;
      /* 轨道圆角 */
    }

    &::-webkit-scrollbar-thumb {
      border: 3px solid #B9D1E9;
      width: 6px;
      height: 45px;
      background: #B9D1E9;
      border-radius: 3px;
    }

    .markdown-body {
      font-size: 14px;
      font-weight: 400;
      color: #4A4A4A;
      line-height: 24px;
    }
  }

  .containerBody {
    max-height: calc(50vh - 110px);
  }

  .containerBodyToCust {
    max-height: calc(50vh - 153px);
  }

  .containerEmptyBlock {
    height: 10px;
  }

  .tooltip-arrow-top::before {
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }

  .tooltip-arrow-top {
    background-color: #fff;
    bottom: -4px;
    // left: 50%;
    left: 45px;
    transform: translateX(-50%) rotate(-135deg);
  }

  .tooltip-arrow-bottom {
    background-color: #CFE1F4;
    top: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }

  .tooltip-arrow-left {
    background-color: #fff;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) rotate(135deg);
  }

  .tooltip-arrow-right {
    background-color: #fff;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) rotate(-45deg);
  }


}
