import React, { useState, useEffect, useRef, useImperativeHandle } from 'react';
import ReactDOM from "react-dom";
import { isMobile } from '../../utils/canUse';
import CloseIcon from './images/closeIcon.png';
import BlueArrow from './images/arrowRight.png';
import InfoIcon from './images/infoIcon.svg';
import { MarkDown } from '../MarkDown';
import { ReferenceProps } from '../References/interface';
import { LogPointConfigMap } from '../../LogPointConfigMap';
import { WaterMark } from '../WaterMark';


export interface ReferencePopupProps {
    parentTarget: any;
    popup: any;
    visiable: boolean;
    placement: string[];
    onMouseEnter: () => void;
    onMouseLeave: () => void;
    handleVisiable: (e: boolean) => void;

    openPage?: (url: string) => void;
    openRawUrl?: (url: string) => void;
    openCallbackUrl?: (url: string) => void;
    downloadFileAndView?: (url: Blob | string, type: string) => void;
    /**
      * log上报回调
      */
    onReportLog?: (params: any) => void;
    waterMark?: object;
}

const ReferencePopupC = React.forwardRef((props: ReferencePopupProps, ref) => {
    const {
        parentTarget,
        popup,
        placement,
        visiable,
        handleVisiable,
        onMouseEnter,
        onMouseLeave,
        openPage,
        openRawUrl,
        openCallbackUrl,
        downloadFileAndView,
        onReportLog,
        waterMark,
    } = props;

    const tooltipRef = useRef<HTMLDivElement>(null);
    const [arrowLeft, setArrowLeft] = useState(0);

    const textRef = useRef<HTMLDivElement>(null);
    const [isOverflowing, setIsOverflowing] = useState(false);

    useEffect(() => {
        // 箭头定位都是相对于弹窗
        if (parentTarget && popup) {
            if (placement?.[0] === 'middle') {
                if (placement?.[1] === 'left') {
                    // 4是箭头的宽度，8是tooltip和触发元素的间距
                    setArrowLeft(parentTarget.getBoundingClientRect().left - popup?.left - 8 - 4);
                } else {
                    setArrowLeft(- 4);
                }
            } else {

                setArrowLeft(parentTarget.getBoundingClientRect().left - popup?.left + 8);
            }
        }
    }, [parentTarget, popup, placement]);

    // 检测文本是否溢出
    useEffect(() => {
        const el = textRef.current;
        if (el) {
            const lineHeight = parseFloat(getComputedStyle(el).lineHeight || '24');
            const maxHeight = lineHeight * 2;

            if (el.scrollHeight > maxHeight + 1) {
                setIsOverflowing(true);
            } else {
                setIsOverflowing(false);
            }
        }
    }, [popup?.reference?.title, popup?.reference?.docName]);


    const getPopHeight = () => {
        return tooltipRef?.current?.getBoundingClientRect();
    }

    useImperativeHandle(ref, () => ({
        getPopHeight,
    }));

    if (!visiable) {
        return null;
    }
    const handleReferenceClick = (item: ReferenceProps) => {
        // 埋点上报
        const params = LogPointConfigMap.get('clickReference');
        if (params) {
            onReportLog?.({
                ...params,
                btn_title: {
                    ...(typeof params.btn_title === 'object' ? params.btn_title : {}),
                    value: item.title || item?.docName,
                }
            });
        }
        if (isMobile) { // app端有rawUrl（云文档链接）则用云文档预览方法打开，没有rwaUrl则看有downloadUrl（内部引用）取downloadUrl进行下载预览,没有则取url(外部引用)进行打开
            if (item?.callbackUrl) {
                if (openCallbackUrl) {
                    openCallbackUrl(item?.callbackUrl);
                } else {
                    console.error('openCallbackUrl function is not defined');
                }
            } else if (item?.rawUrl) {
                if (openRawUrl) {
                    openRawUrl(item?.rawUrl);
                } else {
                    console.error('openRawUrl function is not defined');
                }
            } else if (item?.downloadUrl && item?.fileType) {
                const logParams = LogPointConfigMap.get('referencePageView');
                if (logParams) {
                    onReportLog?.({
                        ...logParams,
                        btn_title: {
                            ...(typeof logParams.btn_title === 'object' ? logParams.btn_title : {}),
                        }
                    });
                }
                if (downloadFileAndView) {
                    downloadFileAndView(item?.downloadUrl, item?.fileType);
                } else {
                    console.error('暂无预览方法，无法预览文件');
                }
            } else if (item?.url) {
                if (openPage) {
                    openPage(item?.url)
                } else {
                    window.open(item?.url)
                }
            } else {
                console.error('no callbackUrl, downloadUrl or url');
            }

        } else { // pc端有url(云文档 或 外部引用)取url进行预览，没有则取downloadUrl进行下载预览
            if (item?.callbackUrl) {
                if (openCallbackUrl) {
                    openCallbackUrl(item?.callbackUrl); // 返回响应数据
                } else {
                    console.error('openCallbackUrl function is not defined');
                }
            } else if (item?.url) {
                const logParams = LogPointConfigMap.get('referencePageView');
                if (logParams) {
                    onReportLog?.({
                        ...logParams,
                        btn_title: {
                            ...(typeof logParams.btn_title === 'object' ? logParams.btn_title : {}),
                        }
                    });
                }
                if (openPage) {
                    openPage(item?.url)
                } else {
                    window.open(item?.url)
                }
            } else {
                // 只返回downloadUrl的先获取二进制文件流再进行后续预览操作
                if (item?.downloadUrl && item?.fileType) {
                    if (downloadFileAndView) {
                        const logParams = LogPointConfigMap.get('referencePageView');
                        if (logParams) {
                            onReportLog?.({
                                ...logParams,
                                btn_title: {
                                    ...(typeof logParams.btn_title === 'object' ? logParams.btn_title : {}),
                                }
                            });
                        }
                        downloadFileAndView(item?.downloadUrl, item?.fileType);
                    } else {
                        console.error('暂无预览方法，无法预览文件');
                    }
                } else {
                    console.error('no callbackUrl, url or downloadUrl');
                }
            }
        }
    }

    return (
        <>
            {isMobile ?
                <>
                    {
                        ReactDOM.createPortal(
                            <div className='AppReferencePopup'>
                                <WaterMark {...waterMark} zIndex={1001}>
                                    <div className='popupMask' onClick={() => handleVisiable(false)}></div>
                                    <div className='popupContainer'>

                                        <div className='containerHeader'>
                                            <div className='headerTitle'>
                                                <div>来源：{popup?.reference?.source !== 'inner' ? '联网搜索' : popup?.reference?.subSourceType}</div>
                                                <img src={CloseIcon} alt='' className='closeIcon' onClick={() => handleVisiable(false)} />
                                            </div>
                                            {(popup?.reference?.title || popup?.reference?.docName) &&
                                                <>
                                                    {
                                                        popup?.reference?.callbackUrl || popup?.reference?.rawUrl || popup?.reference?.url || popup?.reference?.downloadUrl ?
                                                            <div className='headerSubTitle'>
                                                                <div ref={textRef} className={`titleText ${isOverflowing ? 'clamp' : ''}`} onClick={() => handleReferenceClick(popup?.reference)} >
                                                                    {popup?.reference?.title || popup?.reference?.docName}
                                                                    {!isOverflowing && (
                                                                        <img src={BlueArrow} alt='' className='inlineblueArrow' />
                                                                    )}
                                                                </div>
                                                                {isOverflowing && (
                                                                    <img src={BlueArrow} alt='' className='cornerblueArrow' onClick={() => handleReferenceClick(popup?.reference)} />
                                                                )}
                                                            </div> :
                                                            <div className='headerSubTitleDisable'>
                                                                <div ref={textRef} className={`titleText ${isOverflowing ? 'clamp' : ''}`} >
                                                                    {popup?.reference?.title || popup?.reference?.docName}
                                                                </div>
                                                            </div>
                                                    }
                                                </>

                                            }
                                        </div>
                                        {(popup?.reference?.toCust === false || popup?.reference?.source !== 'inner') &&
                                            <div className='headerInstru'>
                                                <img src={InfoIcon} alt='' className='headerInstruIcon' />
                                                {popup?.reference?.toCust === false && '内部资料、不可直接对客转发'}
                                                {popup?.reference?.source !== 'inner' && '可能包含自媒体创作内容，请谨慎识别'}
                                            </div>
                                        }
                                        {(popup?.reference?.summary || popup?.reference?.chunkText) && <div className={popup?.reference?.toCust === false || popup?.reference?.source !== 'inner' ? 'containerBodyToCust' : 'containerBody'}>
                                            <MarkDown content={popup?.reference?.summary || popup?.reference?.chunkText} />
                                        </div>
                                        }
                                    </div>
                                </WaterMark>
                            </div>,
                            document.body
                        )
                    }
                </>
                :
                <>
                    {ReactDOM.createPortal(

                        <div
                            ref={tooltipRef}
                            className="PcReferencePopup"
                            style={{
                                top: popup?.top,
                                left: popup?.left,
                            }}
                            onMouseEnter={onMouseEnter}
                            onMouseLeave={onMouseLeave}
                        >
                            <div style={{ position: 'relative', height: '100%', borderRadius: 'inherit' }}>
                                <WaterMark {...waterMark}>
                                    <div className='containerHeader'>
                                        <div className='headerTitle'>
                                            <div>来源：{popup?.reference?.source !== 'inner' ? '联网搜索' : popup?.reference?.subSourceType}</div>
                                        </div>
                                        {(popup?.reference?.title || popup?.reference?.docName) &&
                                            <>
                                                {
                                                    popup?.reference?.callbackUrl || popup?.reference?.rawUrl || popup?.reference?.url || popup?.reference?.downloadUrl ?
                                                        <div className='headerSubTitle' onClick={() => handleReferenceClick(popup?.reference)}>
                                                            <div ref={textRef} className={`titleText ${isOverflowing ? 'clamp' : ''}`} >
                                                                {popup?.reference?.title || popup?.reference?.docName}
                                                                {!isOverflowing && (
                                                                    <img src={BlueArrow} alt='' className='inlineblueArrow' />
                                                                )}
                                                            </div>
                                                            {isOverflowing && (
                                                                <img src={BlueArrow} alt='' className='cornerblueArrow' />
                                                            )}
                                                        </div> :
                                                        <div className='headerSubTitleDisable'>
                                                            <div ref={textRef} className={`titleText ${isOverflowing ? 'clamp' : ''}`} >
                                                                {popup?.reference?.title || popup?.reference?.docName}
                                                            </div>
                                                        </div>
                                                }
                                            </>

                                        }
                                    </div>
                                    {(popup?.reference?.toCust === false || popup?.reference?.source !== 'inner') &&
                                        <div className='headerInstru'>
                                            <img src={InfoIcon} alt='' className='headerInstruIcon' />
                                            {popup?.reference?.toCust === false && '内部资料、不可直接对客转发'}
                                            {popup?.reference?.source !== 'inner' && '可能包含自媒体创作内容，请谨慎识别'}
                                        </div>
                                    }
                                    {(popup?.reference?.summary || popup?.reference?.chunkText) && <>
                                        <div className={popup?.reference?.toCust === false || popup?.reference?.source !== 'inner' ? 'containerBodyToCust' : 'containerBody'}>
                                            <MarkDown content={popup?.reference?.summary || popup?.reference?.chunkText} />
                                        </div>
                                        {/* <div className='containerEmptyBlock'></div> */}
                                    </>
                                    }
                                </WaterMark>
                                <div
                                    // className={placement?.[0] === 'top' ? 'tooltip-arrow-top' : 'tooltip-arrow-bottom'}
                                    className={placement?.[0] === 'middle' ? `tooltip-arrow-${placement?.[1]}` : `tooltip-arrow-${placement?.[0]}`}
                                    style={{
                                        position: 'absolute',
                                        width: '8px',
                                        height: '8px',
                                        // 等腰直角三角形，直角在右下角（初始状态）
                                        clipPath: 'polygon(0 0, 100% 0, 0 100%)',
                                        left: arrowLeft,
                                    }}
                                />

                            </div>
                        </div>,
                        document.body
                    )}
                </>
            }
        </>
    )
});

export const ReferencePopup = React.memo(ReferencePopupC);
