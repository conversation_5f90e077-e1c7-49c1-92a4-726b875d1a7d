/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import clsx from 'clsx';
import { isMobile } from '../../utils/canUse';
import { SystemMessage } from './SystemMessage';
import { HallucinationMarker } from '../HallucinationMarker';
import { Feedback } from '../Feedback';
import { Avatar } from '../Avatar';
import { Time } from '../Time';
import { Typing } from '../Typing';
import WarningIcon from '../Bubble/images/warning.svg'
import { ILogParams } from '../../LogPointConfigMap';
import { Bubble } from '../Bubble';
import { ThinkContent } from '../ThinkContent';
import { Lowcode } from '../Lowcode';
import { AiImage } from '../Image';
import { GuessAskMore } from '../GuessAskMore';
import { Card } from '../Card';
import { MarkDown } from '../MarkDown';
import { FileCard } from '../FileCard';
import { Video } from '../Video';
import { References } from '../References';
// import { getThinkContent } from '../../utils/aiChat';
import { ThinkLinkProps } from '../ThinkContent/interface';
import { ReferenceProps } from '../References/interface';
import { FeedbackLabelProps } from '../FeedbackTipPc/interface';
import { getDateFormat } from '../../utils/aiChat';
import DefaultDown from '../Feedback/images/default-down.svg';
import DefaultUp from '../Feedback/images/default-up.svg';
import BadIconBg from './images/badIconBg.png';
import GoodIconBg from './images/goodIconBg.png';
import { ReferencePopup } from './ReferencePopup';
import BlueArrow from './images/arrowRight.png';
import InfoIcon from './images/infoIcon.svg';

export interface User {
  avatar?: string;
  name?: string;
  url?: string;
  [k: string]: any;
}

export type MessageId = string | number;

export type CardProps = {
  type: string;
  content: {
    mediaType: string;
    summary: string;
    fileTitle: string;
    fileUrl: string;
    id: string;
  }
}
export interface FeedbackProps {
  feedbackTime: number | string;
  feedbackResult: string;
  feedbackChannel: string;
  feedbackReason: {
    selectedReason: string[];
    inputReason: string;
  }
}

export interface MessageProps {
  /**
   * 唯一ID
   */
  _id: MessageId;
  /**
   * 用来回传给后端的messageId
   */
  messageId?: MessageId;
  /**
   * 消息类型
   */
  type?: string;
  /**
   * 消息内容
   */
  content?: any;
  /**
   * 正文内容，一个数组
   */
  contents?: any[];
  /**
   * 消息创建时间
   */
  createdAt?: number;
  /**
   * 消息发送者信息
   */
  user?: User;
  /**
   * 消息位置
   */
  position?: 'left' | 'right' | 'center' | 'pop';
  /**
   * 是否显示时间
   */
  hasTime?: boolean;
  /**
   * 消息内容渲染函数
   */
  renderMessageContent?: (message: MessageProps) => React.ReactNode;
  /**
   * 消息内容为需要嵌套的卡片渲染函数
   */
  renderCardContent?: (cards: CardProps[]) => React.ReactNode;
  /**
   * 是否需要点赞点踩
   */
  needFeedback?: boolean;
  /**
  * 点赞点踩结果,good-点赞，bad-点踩
  */
  feedbackResult?: 'good' | 'bad';
  onFeedBack?: (score: MessageProps['feedbackResult'], message: MessageProps) => void;
  /**
  * log上报回调
  */
  onReportLog?: (params: ILogParams | undefined) => void;
  /**
  * 复制文本回调
  */
  copyText?: (text: string) => void;
  /**
  * 是否已停止生成
  */
  stopped?: boolean;
  /**
   * 未发送成功
   */
  unsend?: boolean;
  /**
  * 发送了以后获取答案异常（比如接口超时等）
  */
  sendError?: boolean;
  /**
   * 是否展示幻觉标识 aiMsgExtInfoJson缺失了dataSource二级属性 或者 为 "LLM"
   */
  showHallucination?: boolean;
  /**
  * 是否正在思考中
  */
  isThinking?: boolean;
  /**
   * 思考中的内容
   */
  thinkLinks?: ThinkLinkProps[];

  // 模方卡片用到的
  lowCodeConfig?: object;
  isDev?: boolean;
  userId?: string;
  waterMark?: object;
  // GuessAskMore卡片可能需要的发送
  onSend?: (type: string, content: string, params?: object) => Promise<boolean>;
  // GuessAskMore卡片可能需要的打开链接/markdown卡片上链接的打开
  openPage?: (url: string) => void;
  // 文件预览方法
  openFileViews?: () => {};
  // 参考资料
  references?: ReferenceProps[];
  // 是否展示反馈弹窗
  showFeedbackModal?: boolean;
  // 反馈标签
  feedbackLabels?: FeedbackLabelProps[];
  /**
    * 点踩反馈弹窗配置
    */
  feedbackModalConfig?: {
    title?: string,
    inputPlaceholder?: string,
    showLabels?: boolean,
  };
  // 是否展示token相关信息
  showToken?: boolean,
  totalTokens?: number,
  totalTime?: number,
  // app端pdf预览方法
  downloadFileAndView?: () => {};
  // app端云文档预览方法
  openRawUrl?: () => {};
  openCallbackUrl?: () => {};
  // 渲染特殊卡片
  cards?: CardProps[];
  // 是否预览模式
  isPreview?: boolean,
  traceId?: string,
  feedback?: FeedbackProps,
  traceIdUrl?: string,
  // 通知虚拟列表重新测算尺寸
  reMeasureSize?: () => void;
  // 各子问题的参考资料合起来以后去重的数组
  uniqReferences?: ReferenceProps[];
}

export type MessageWithoutId = Omit<MessageProps, '_id'> & {
  _id?: MessageId;
};

const Message = (props: MessageProps) => {
  const { renderMessageContent, renderCardContent, onFeedBack, onReportLog, copyText, lowCodeConfig, onSend, openPage, openFileViews, isDev, userId, waterMark, feedbackLabels, showFeedbackModal, feedbackModalConfig, downloadFileAndView, openRawUrl, openCallbackUrl, isPreview = false, reMeasureSize, ...msg } = props;
  const { type, content, user = {}, _id: id, position = 'left', hasTime = true, createdAt, isThinking, thinkLinks = [], contents = [], uniqReferences = [] } = msg;
  const { name, avatar } = user;

  // 标志位，是否已经触发引用元素onMouseEnter事件
  const isHoverRef = useRef<boolean>(false);
  // 标志位，是否已经触发弹窗onMouseEnter事件
  const isPopHoverRef = useRef<boolean>(false);
  const [popupVisible, setPopupVisible] = useState(false);
  const [popup, setPopup] = useState<{ id?: string; reference?: any; top: number; left: number } | null>(null);
  const [popReferences, setPopReferences] = useState<any>({});
  const [popupPlacement, setPopupPlacement] = useState(['top', 'right']);
  const wrapperRef = useRef<HTMLDivElement>(null); //  绑定当前这条消息根容器

  const [parentTarget, setParentTarget] = useState<HTMLElement | null>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const hideTimer = useRef<any>(null);

  const textRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);


  const calculateDetail = useCallback((el: any) => {
    const rect = el.getBoundingClientRect();
    let top;
    let left;
    let placementY;
    let placementX;
    const leftMargin = 30;
    const margin = 8;
    // const chatWrapWidth = document.querySelector('.ChatWrap')?.getBoundingClientRect()?.width;
    const tooltipHeight = popupRef.current?.getBoundingClientRect()?.height || 476;
    const tooltipWidth = popupRef.current?.getBoundingClientRect()?.width || 375;
    const showRight = window.innerWidth - rect.left > tooltipWidth + 20;
    const showLeft = rect.left > tooltipWidth + 20;

    const showTop = rect.top >= tooltipHeight + margin;
    const showBottom = window.innerHeight - rect.top - rect.height >= tooltipHeight + margin;
    console.log('上下左右', showTop, showBottom, showLeft, showRight);
    if (!showTop && !showBottom) {
      // 元素上方下方都放不下弹窗时
      placementY = 'middle';
      top = rect.top - tooltipHeight / 2 + rect.height / 2;
      if (showRight) {
        placementX = 'right';
        left = rect.left + rect.width + margin;
      } else {
        placementX = 'left';
        left = rect.left - tooltipWidth - margin;
      }
    } else {
      if (showTop) {
        placementY = 'top';
        top = rect.top - tooltipHeight - margin;
      } else {
        placementY = 'bottom';
        top = rect.top + rect.height + margin;
      }
      if (showRight) {
        placementX = 'right';
        // 极限情况，假如触发元素左边间距不足偏移量30，则设为10（极限值）
        left = rect.left > leftMargin ? rect.left - leftMargin : rect.left - leftMargin / 3;
      } else if (showLeft) {
        placementX = 'left';
        // 极限情况，假如触发元素右边间距不足偏移量30，则设为10（极限值）
        left = window.innerWidth - rect.left - rect.width > leftMargin ?
          rect.left - tooltipWidth + leftMargin + rect.width
          : rect.left - tooltipWidth + leftMargin / 3 + rect.width;
      } else {
        placementX = 'middle';
        left = (window.innerWidth - tooltipWidth) / 2;
      }
    }
    return { top, left, placementX, placementY }
  }, [])

  // 计算弹窗位置
  const calculatePopupPosition = useCallback(() => {
    if (!wrapperRef.current || !popupRef.current) {
      return;
    }

    if (parentTarget) {

      const { top, left, placementX, placementY } = calculateDetail(parentTarget);
      setPopupPlacement([placementY, placementX]);
      setPopup({ ...popup, top, left });
    }
    // const tooltipHeight = popupRect.height;
    // const margin = 8;
    // const canShowAbove = rect.top >= tooltipHeight + margin;
    // const placementY = canShowAbove ? 'top' : 'bottom';
    // const top = canShowAbove
    //   ? rect.top - tooltipHeight - margin
    //   : rect.bottom + margin;

    // let left;
    // const showRight = window.innerWidth - rect.right > tooltipWidth;
    // const showLeft = rect.left > tooltipWidth;
    // const leftMargin = 30;

    // let placementX;
    // if (showRight) {
    //   placementX = 'right';
    //   left = rect.left - leftMargin;
    // } else if (showLeft) {
    //   placementX = 'left';
    //   left = rect.left - tooltipWidth + leftMargin + rect.width;
    // } else {
    //   placementX = 'middle';
    //   left = (window.innerWidth - tooltipWidth) / 2;
    // }
  }, [])
  const handleShow = () => {
    if (hideTimer.current) {
      clearTimeout(hideTimer.current);
      hideTimer.current = null;
    }
    setPopupVisible(true);
  }

  // 引用元素的onMouseEnter事件
  const handleMouseEnter = useCallback((el: any) => {
    if (isHoverRef.current) return;
    isHoverRef.current = true;

    if (hideTimer.current) {
      clearTimeout(hideTimer.current);
      hideTimer.current = null;
    }
    setParentTarget(el);
    const cId = el.dataset.id;
    // id格式为 `${msg?.messageId}+${msg?.agent}+${msg?.problem_id || msg?.problemId}+${citationNumber}`
    const idArr = cId?.split('+');

    const tContent = contents?.find((it: any) => it?.candidate_agent === idArr?.[1] && it?.problem_id === Number(idArr?.[2]));
    const tReferenceArr = tContent?.references;
    const tReference = tReferenceArr?.find((e: { citationIndex: number; }) => Number(e?.citationIndex) === Number(idArr?.[3]));
    setPopReferences(tReference);

    const titleTextEl = textRef.current;
    if (titleTextEl) {
      const lineHeight = parseFloat(getComputedStyle(titleTextEl).lineHeight || '24');
      const maxHeight = lineHeight * 2;

      if (titleTextEl.scrollHeight > maxHeight + 1) {
        setIsOverflowing(true);
      } else {
        setIsOverflowing(false);
      }
    }

    if (el) {
      const { top, left, placementX, placementY } = calculateDetail(el);

      setPopupPlacement([placementY, placementX]);
      setPopup({ id: cId, reference: tReference, top, left });
      handleShow();
    }

  }, [contents]);

  // 引用元素的onMouseLeave事件
  const handleMouseLeave = () => {
    if (!isHoverRef.current) return;
    isHoverRef.current = false;

    hideTimer.current = setTimeout(() => {
      setPopReferences({});
      setPopupVisible(false);
    }, 200)
  }

  //  弹窗的onMouseEnter事件
  const handleTooltipEnter = () => {
    if (isPopHoverRef.current) return;
    // 鼠标从触发元素移入弹窗时，如果handleTooltipEnter，就不要执行handleMouseLeave了
    isHoverRef.current = false;
    isPopHoverRef.current = true;
    if (hideTimer.current) {
      clearTimeout(hideTimer.current);
      hideTimer.current = null;
    }
  }

  //   弹窗的onMouseLeave事件
  const handleTooltipLeave = () => {
    if (!isPopHoverRef.current) return;
    isPopHoverRef.current = false;
    hideTimer.current = setTimeout(() => {
      setPopReferences({});
      setPopupVisible(false);
    }, 200)
  }

  useEffect(() => {
    const wrapper = wrapperRef.current;
    if (!wrapper || contents.length === 0) return;
    if (isMobile) {
      // 事件委托的方式
      const handleEventClick = (e: Event) => {
        const target = (e.target as HTMLElement)?.closest(".ReferenceCitation") as HTMLElement;
        if (target) {
          setParentTarget(target);
          const cId = target.dataset.id;
          // id格式为 `${msg?.messageId}+${msg?.agent}+${msg?.problem_id || msg?.problemId}+${citationNumber}`
          const idArr = cId?.split('+');

          const tContent = contents?.find((it: any) => it?.candidate_agent === idArr?.[1] && it?.problem_id === Number(idArr?.[2]));
          const tReferenceArr = tContent?.references;
          const tReference = tReferenceArr?.find((it: { citationIndex: number; }) => Number(it?.citationIndex) === Number(idArr?.[3]));

          handleShow();
          setPopup({ id: cId, reference: tReference, top: 0, left: 0 });
        }
      };
      wrapper.addEventListener('click', handleEventClick);

      return () => {
        wrapper.removeEventListener('click', handleEventClick);
      };
    } else {
      const citationEls = wrapper.querySelectorAll<HTMLElement>('.ReferenceCitation');
      citationEls.forEach((el) => {
        const citationEl = el as HTMLElement;
        setParentTarget(el);

        // citationEl.addEventListener('click', () => handleMouseEnter(el));
        citationEl.addEventListener('mouseenter', () => handleMouseEnter(el));
        citationEl.addEventListener('mouseleave', handleMouseLeave);

        // 清理函数
        (citationEl as any)._cleanupHandlers = { handleMouseEnter, handleMouseLeave };
      });
      return () => {
        citationEls.forEach((el) => {
          const citationEl = el as HTMLElement;
          const handlers = (citationEl as any)._cleanupHandlers;
          if (handlers) {
            // citationEl.removeEventListener('click', handlers.handleMouseEnter);
            citationEl.removeEventListener('mouseenter', handlers.handleMouseEnter);
            citationEl.removeEventListener('mouseleave', handlers.handleMouseLeave);
          }
        });
      };
    }
  }, [contents]);

  // 当弹窗可见性变化时计算位置
  useEffect(() => {
    if (!popupVisible) {
      return;
    }

    if (popupVisible && parentTarget) {
      // 使用requestAnimationFrame确保DOM已更新
      requestAnimationFrame(() => {
        calculatePopupPosition();
      });
    }
  }, [popupVisible, popReferences, parentTarget]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (popupVisible && parentTarget) {
        calculatePopupPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (!isMobile && popupVisible) {
      setPopupVisible(false);
    }
  }, [parentTarget?.getBoundingClientRect()?.y])

  // 实现多消息卡片类型模板
  const innerRenderMessageContent = useCallback(() => {
    // 根据消息类型来渲染
    switch (type) {
      case 'text':
      case 'richtext':
      case 'thinking':
      case 'markdown': //参考：https://blog.csdn.net/Sakuraaaa_/article/details/128400497
        // const formatContent = getThinkContent(content?.text);
        // 滤掉假值
        const effctiiveContents = contents?.filter(item => item);
        return (
          <Bubble>
            <ThinkContent
              isThinking={isThinking && !msg.stopped && !msg.sendError}
              thinkLinks={thinkLinks}
              stopped={msg.stopped}
              onReportLog={onReportLog}
              reMeasureSize={reMeasureSize}
            />
            {
              content?.text && (
                <MarkDown content={content?.text} openPage={openPage} />
              )
            }
            {
              effctiiveContents && effctiiveContents?.length > 0 && effctiiveContents?.map((item: any, index: number) => (
                <div key={index}>
                  <MarkDown content={`### **${item?.sub_problem_title ?? ''}** \n ${item?.content ?? ''}`} openPage={openPage} />
                  {/* 每个子问题如果有相应的卡片需要渲染 */}
                  {
                    item?.cards?.length > 0 && item?.isProblemEnd && renderCardContent && (
                      renderCardContent(item?.cards ?? [])
                    )
                  }
                  {/* 每个子问题结束以后根据agent加注，业问和睿评默认是内部，如果参考资料里面出现了外部的，则展示外部的话术 */}
                  <div className='AgentNote'>
                    {
                      item?.isProblemEnd
                      && (item?.candidate_agent === 'yewen' || item?.candidate_agent === 'ruiping')
                      && item?.references?.length > 0
                      && (
                        item?.references?.findIndex((reference: ReferenceProps) => reference?.source === 'outer') > -1
                          ? '注：以上内容由AI结合互联网数据总结，请审慎使用 ！'
                          : '注：以上内容由AI基于公司内部知识总结，供参考'
                      )
                    }
                    {
                      item?.isProblemEnd && item?.candidate_agent === 'hiagent_search' && '注：以上分析结果来自于外部网络查询，请审慎使用 ！'
                    }
                  </div>
                  {/* 每个子问题结束以后追加参考资料 */}
                  {/* {
                    item?.references?.length > 0 && item?.isProblemEnd && (
                      <References references={item?.references} openPage={openPage} downloadFileAndView={downloadFileAndView} openRawUrl={openRawUrl} openCallbackUrl={openCallbackUrl} onReportLog={onReportLog} reMeasureSize={reMeasureSize} />
                    )
                  } */}
                  {/* 除了最后一个子问题，都用横线作为分隔符 */}
                  {
                    (index !== effctiiveContents?.length - 1) && <hr className='HrLine'></hr>
                  }
                </div>
              )
              )
            }
            {/* 每个子问题的参考资料去重后得到的数组 */}
            {
              uniqReferences?.length > 0 && (
                <References references={uniqReferences} openPage={openPage} downloadFileAndView={downloadFileAndView} openRawUrl={openRawUrl} openCallbackUrl={openCallbackUrl} onReportLog={onReportLog} reMeasureSize={reMeasureSize} />
              )
            }
          </Bubble>
        );
      case 'relatedQuestionList':
        return (
          <Card fluid>
            {/* eslint-disable-next-line @typescript-eslint/no-use-before-define */}
            <GuessAskMore questionList={content.relatedQuestionList} onSend={onSend} openPage={openPage} isPreview={isPreview} />
          </Card>
        );
      case 'file':
        return (
          <>
            <Bubble type="file">
              <FileCard file={content} openFileViews={openFileViews} />
            </Bubble>
          </>
        );
      case 'image':
        return (
          <>
            <Bubble type="image">
              <AiImage src={content.picUrl} />
            </Bubble>
          </>
        );
      case 'video':
        return (
          <Bubble type="video">
            <Video src={content?.url} />
          </Bubble>
        );
      case 'card':
        return (
          <Bubble type="card">
            <Lowcode
              content={content}
              isDev={isDev}
              userId={userId}
              lowCodeConfig={lowCodeConfig}
            />
          </Bubble>
        );
      default:
        return null;
    }
  }, [content, contents, downloadFileAndView, openRawUrl, openCallbackUrl, isDev, isThinking, lowCodeConfig, msg?.cards, msg.sendError, msg.stopped, onReportLog, onSend, openFileViews, openPage, renderCardContent, thinkLinks, type, userId]);

  const renderNotTyping = () => {
    if (type !== 'typing') {
      if (renderMessageContent && renderMessageContent(msg)) {
        return renderMessageContent(msg);
      }
      return innerRenderMessageContent();
    }
    return null;
  };

  const handleTraceIdClick = () => {
    if (!msg?.traceIdUrl) {
      console.warn('traceIdUrl is null');
      return;
    }
    if (openPage) {
      openPage(msg?.traceIdUrl)
    } else {
      window.open(msg?.traceIdUrl)
    }
  }

  if (type === 'system') {
    return <SystemMessage content={content.text} action={content.action} />;
  }

  const isRL = position === 'right' || position === 'left';

  return (
    <div className={clsx('Message', position)} data-id={id} data-type={type} ref={wrapperRef} >
      {hasTime && createdAt && (
        <div className="Message-meta">
          <Time date={createdAt} />
        </div>
      )}
      {/* 预览模式下展示traceId链接 */}
      {msg?.traceId && position === 'left' && isPreview &&
        <div className='Message-traceId'>
          traceID:<span className='Message-traceIdUrl' onClick={handleTraceIdClick}>{msg?.traceId}</span>
        </div>
      }
      <div className="Message-main">
        {isRL && avatar && <Avatar className='Message-avatar-img' src={avatar} alt={name} url={user.url} />}
        <div className="Message-inner">
          {isRL && name && <div className="Message-author">{name}</div>}
          <div className="Message-content" role="alert" aria-live="assertive" aria-atomic="false">
            {
              type === 'typing' && (<Typing />)
            }
            {renderNotTyping()}
          </div>
          {msg?.stopped && <div className="Message-stopped">已停止生成</div>}
          {msg?.sendError && <div className="Message-sendError"> <img src={WarningIcon} />答案生成异常，已被终止</div>}
          <HallucinationMarker showHallucination={msg?.showHallucination} />
          <Feedback
            message={msg}
            onFeedBack={onFeedBack}
            onReportLog={onReportLog}
            copyText={copyText}
            feedbackLabels={feedbackLabels}
            showFeedbackModal={showFeedbackModal}
            // 反馈弹窗自定义配置
            feedbackModalConfig={feedbackModalConfig}
            isPreview={isPreview}
          />
          {/* 预览模式下展示反馈结果气泡 */}
          {msg?.feedback && isPreview &&
            <div className={msg?.feedback?.feedbackResult === 'bad' ? 'Message-badBubble' : 'Message-goodBubble'}>
              <img
                src={msg?.feedback?.feedbackResult === 'bad' ? BadIconBg : GoodIconBg}
                className={msg?.feedback?.feedbackResult === 'bad' ? 'Message-badIconBg' : 'Message-goodIconBg'}
              />
              <div className='Message-badResult'>
                <img src={msg?.feedback?.feedbackResult === 'bad' ? DefaultDown : DefaultUp} alt='' className='Feedback-icon' />
                {`${msg?.feedback?.feedbackTime && getDateFormat(msg?.feedback?.feedbackTime, 'YYYY-MM-DD HH:mm')} ${msg?.feedback?.feedbackChannel === 'app' ? '手机聊TA' : 'PC聊TA'}`} 反馈
              </div>
              {msg?.feedback?.feedbackResult === 'bad' &&
                <>
                  <div className='Message-badReason'>
                    <div className='Message-badReasonName'>反馈原因：</div>
                    <div className='Message-badReasonText'>{msg?.feedback?.feedbackReason?.selectedReason?.join('；') || '--'}</div>
                  </div>
                  <div className='Message-badReason'>
                    <div className='Message-badReasonName'>补充说明：</div>
                    <div className='Message-badReasonText'>{msg?.feedback?.feedbackReason?.inputReason}</div>
                  </div>
                </>
              }

            </div>
          }
        </div>
      </div>
      {popupVisible && (
        <>
          <ReferencePopup
            // ref={popupRef}
            parentTarget={parentTarget}
            visiable={popupVisible}
            waterMark={waterMark}
            popup={popup}
            onMouseEnter={handleTooltipEnter}
            onMouseLeave={handleTooltipLeave}
            handleVisiable={(e) => setPopupVisible(e)}
            placement={popupPlacement}
            openPage={openPage}
            downloadFileAndView={downloadFileAndView}
            openRawUrl={openRawUrl}
            openCallbackUrl={openCallbackUrl}
            onReportLog={onReportLog}
          />
        </>
      )}
      {!isMobile && <div
        ref={popupRef}
        className="PcReferencePopup"
        style={{
          top: -9999,
          left: -9999,
        }}
      >
        <div style={{ position: 'relative', height: '100%' }}>
          <div className='containerHeader'>
            <div className='headerTitle'>
              <div>来源：{popReferences?.source !== 'inner' ? '联网搜索' : popReferences?.subSourceType}</div>
            </div>
            {(popReferences?.title || popReferences?.docName) && <div className='headerSubTitle'>
              {/* {popReferences?.title || popReferences?.docName}
              <img src={BlueArrow} alt='' className='blueArrow' /> */}
              <div ref={textRef} className={`titleText ${isOverflowing ? 'clamp' : ''}`} >
                {popReferences?.title || popReferences?.docName}
                {!isOverflowing && (
                  <img src={BlueArrow} alt='' className='inlineblueArrow' />
                )}
              </div>
              {isOverflowing && (
                <img src={BlueArrow} alt='' className='cornerblueArrow' />
              )}
            </div>
            }
          </div>
          {(popReferences?.toCust === false || popReferences?.source !== 'inner') &&
            <div className='headerInstru'>
              <img src={InfoIcon} alt='' className='headerInstruIcon' />
              {popReferences?.toCust === false && '内部资料、不可直接对客转发'}
              {popReferences?.source !== 'inner' && '可能包含自媒体创作内容，请谨慎识别'}
            </div>
          }
          {(popReferences?.summary || popReferences?.chunkText) && <>
            <div className={popReferences?.toCust === false || popReferences?.source !== 'inner' ? 'containerBodyToCust' : 'containerBody'}>
              <MarkDown content={popReferences?.summary || popReferences?.chunkText} />
            </div>
            {/* <div className='containerEmptyBlock'></div> */}
          </>
          }
        </div>
      </div>
      }
    </div>
  );
};

export default React.memo(Message);
