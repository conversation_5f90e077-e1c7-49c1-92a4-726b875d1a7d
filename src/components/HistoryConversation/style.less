.conversationWrap {
    display: flex;
    flex-direction: column;
    // height: 100vh;
    height: 100%;
    border-radius: 8px;
    background: @history-conversation-bg;

    .conversation {
        display: flex;
        flex-direction: column;
        .title {
            margin-top: 40px;
        }

        .item {
            margin-top: 20px;
        }
    }
}

.contentWrap {
    flex-grow: 1;
    overflow-y: scroll;
    padding: 0 6px 0px 20px;
    // margin-right: 6px;

    /* 整个滚动条 */
        &::-webkit-scrollbar {
            width: 6px;
            /* 垂直滚动条宽度 */
            height: 6px;
            /* 水平滚动条高度 */
        }

        /* 滚动条轨道 */
        &::-webkit-scrollbar-track {
            background: transparent;
            /* 轨道背景色 */
            // border-radius: 6px;
            /* 轨道圆角 */
        }

        /* 滚动条滑块 */
        &::-webkit-scrollbar-thumb {
            /* 滑块背景色 */
            border-radius: 3px;
            /* 滑块圆角 */
            border: 3px solid #B9D1E9;
            /* 滑块边框 */

            width: 6px;
            height: 45px;
            background: #B9D1E9;
            border-radius: 3px;
        }

        /* 滚动条滑块悬停效果 */
        &::-webkit-scrollbar-thumb:hover {
            background: #555;
            /* 悬停时滑块背景色 */
        }
}

// /* 整个滚动条 */
// ::-webkit-scrollbar {
//     width: 6px;
//     /* 垂直滚动条宽度 */
//     height: 6px;
//     /* 水平滚动条高度 */
// }

// /* 滚动条轨道 */
// ::-webkit-scrollbar-track {
//     background: transparent;
//     /* 轨道背景色 */
//     // border-radius: 6px;
//     /* 轨道圆角 */
// }

// /* 滚动条滑块 */
// ::-webkit-scrollbar-thumb {
//     background: #E5E7EE;
//     /* 滑块背景色 */
//     border-radius: 3px;
//     /* 滑块圆角 */
//     border: 3px solid #f1f1f1;
//     /* 滑块边框 */

//     width: 6px;
//     height: 45px;
//     background: #B9D1E9;
//     border-radius: 3px;
// }

// /* 滚动条滑块悬停效果 */
// ::-webkit-scrollbar-thumb:hover {
//     background: #555;
//     /* 悬停时滑块背景色 */
// }

.groupTitle {
    display: flex;
    align-items: center;

    .titleIcon {
        width: 2px;
        height: 10px;
        background: #CCCCCC;
        margin-right: 6px;
    }

    .titleText {
        font-size: 15px;
        font-weight: 600;
        color: #333333;
        line-height: 21px;
    }
}

.ConversationItem {
    display: flex;
    align-items: center;
    width: 82%;
    justify-content: space-between;

    .itemLabel {
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        font-size: 15px;
        font-weight: 400;
        color: #333333;
        line-height: 21px;
        border-radius: 4px;
        padding: 6px 10px;

        &:hover {
            width: max-content;
            // max-width: 100%;
            background: rgba(62, 116, 247, 0.06);
            border-radius: 4px;
        }
    }

    .itemActive {
        width: max-content;
        background: @history-conversation-active-bg;
        border-radius: 4px;
        color: @history-conversation-active-color;

        &:hover {
            width: max-content;
            max-width: 100%;
            background: @history-conversation-active-bg;
        }
    }

    .itemOperation {

        position: relative;

        .operationIcon {
            cursor: pointer;
        }

        .operationMenu {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.searchBarWrap {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}
