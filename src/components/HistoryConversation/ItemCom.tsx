/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import clsx from 'clsx';
import { ConversationItemProps } from './interface';

// interface ConversationsItemComponent {
//     conversationId: string;
//     question: string;
//     createTime: number;
//     activeConversationId?: string;
//     disabled?: boolean;
//     menuConfig?: any;
//     onConversationClick: () => void;
// }

// const stopPropagation: React.MouseEventHandler<HTMLSpanElement> = (e) => {
//     e.stopPropagation();
// };

const ConversationsItem: React.FC<ConversationItemProps> = (props) => {
    const {
        conversationId,
        question,
        // createTime,
        disabled,
        activeConversationId,
        // menuConfig,
    } = props;

    // =========================== Tooltip ============================
    // const [opened, setOpened] = useState(false);

    // ============================ Style =============================
    const mergedCls = clsx(
        { ['itemLabel']: true },
        { ['itemActive']: activeConversationId === conversationId },
        { ['itemDisabled']: disabled },
    );

    const [tooltipShow, setTooltipShow] = useState(false);
      useEffect(() => {
          const titleEle = document.getElementById(`${conversationId}`);
        if (titleEle) {
          if (titleEle.scrollWidth > titleEle.clientWidth) {
            setTooltipShow(true);
          } else {
            setTooltipShow(false);
          }
        }
      }, []);


    // const onOpenChange = (open: boolean) => {
    //     if (open) {
    //         setOpened(!open);
    //     }
    // };

    // ============================ Render ============================
    return (
        <div className='ConversationItem'>
            <div className={mergedCls} title={tooltipShow ? question : ''} id={conversationId}>
                {question}
            </div>
            {/* TODO-预留会话操作菜单 */}
            {/* {menuConfig?.items?.length ?
                <div className='itemOperation'>
                    <div className='operationIcon' onClick={() => setOpened(true)}>操作</div>
                    {opened && <div className='operationMenu'>菜单弹窗</div>}
                </div> : null
            } */}
        </div>
    );
};

export default ConversationsItem;
