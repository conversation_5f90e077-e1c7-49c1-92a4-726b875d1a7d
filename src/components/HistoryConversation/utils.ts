import { GroupedItem, ConversationItemProps } from './interface';

export const groupByTimeRange = (data: ConversationItemProps[]): GroupedItem[] => {
    const now = new Date();
    const today = new Date(now);
    today.setHours(0, 0, 0, 0); // 今天的开始时间

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1); // 昨天的开始时间

    const sixDaysAgo = new Date(today);
    sixDaysAgo.setDate(today.getDate() - 6); // 7 天前的开始时间

    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30); // 30 天前的开始时间

    // 初始化分组
    const groupedData: Record<string, ConversationItemProps[]> = {
      "今天": [],
      "七天内": [],
      "30天内": [],
      // "30天之前": []
    };

    // 遍历数据并分类
    data.forEach((item:  ConversationItemProps) => {
      const convDate = new Date(item.updateTime || item.createTime);
      // console.log('today', today, convDate);
      // debugger;
      if (convDate >= today) {
        groupedData["今天"].push(item); // 今天
      } else if (convDate >= sixDaysAgo) {
        groupedData["七天内"].push(item); // 七天内（T-6日~T-1日）
      } else if (convDate >= thirtyDaysAgo) {
        groupedData["30天内"].push(item); // 30天内（T-30日~T-7日）
      }
      // else {
      //     groupedData["30天之前"].push(item); // 30天之前
      // }
    });

    // 对每个分组内的 items 按 time 倒序排序
    Object.keys(groupedData).forEach((group) => {
      // groupedData[group].sort((a, b) => (b.updateTime - a.updateTime || b.createTime - a.createTime));
      groupedData[group].sort((a, b) => b.updateTime || b.createTime - a.updateTime  || a.createTime);
    });

    // 转换为目标格式，并过滤掉 items 为空的组
    const result: GroupedItem[] = Object.keys(groupedData)
      .filter((group) => groupedData[group].length > 0) // 过滤掉空组
      .map((group) => ({
        group,
        items: groupedData[group],
      }));

    return result;
};