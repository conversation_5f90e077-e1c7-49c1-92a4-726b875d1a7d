/* eslint-disable import/no-extraneous-dependencies */
import React, { useState, useEffect } from 'react';
import GroupTitle from './GroupTitle';
import SearchBar from '../SearchBar';
import ConversationsItemComponent from './ItemCom';
import { Navbar } from '../Navbar';
import { groupByTimeRange } from './utils';
import { HistoryConversationProps, HistoryConversationCustomerProps, ConversationItemProps, GroupedItem } from './interface';

export const HistoryConversation = (props: HistoryConversationProps & HistoryConversationCustomerProps) => {
  const {
    style,
    title="历史会话记录",
    logo='',
    navbar,
    renderTitle,
    renderBrand,
    showSearch=true,
    searchPlaceholder="请输入搜索关键字",
    list,
    activeConversationId,
    onConversationClick,
    renderFooter,
  } = props;

  const [groupedConversationItems, setGroupedConversationItems] = useState<GroupedItem[]>([]);

  // 列表初始化
  useEffect(() => {
    setGroupedConversationItems(groupByTimeRange(list));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(list)])

  const onSearch = (keyword: string) => {
    const newList = list?.filter((item: any) => item.question.includes(keyword));
    setGroupedConversationItems(groupByTimeRange(newList));
  }

  const onClear = () => {
    setGroupedConversationItems(groupByTimeRange(list));
  }

  return (
    <div className='conversationWrap' style={{...style}}>
      {/* 标题区域 */}
      {renderTitle ? renderTitle() : (
        <Navbar
          logoAndTitlePosition="left"
          {...navbar}
          showReturnButton={false}
          showLogo={true}
          logo={logo}
          title={title}
          showHistoryButton={false}

        />
      )}
      {/* 品牌区域 */}
      {renderBrand && renderBrand()}
      {/* 搜索区域 */}
      <div className='searchBarWrap'>
      {showSearch && (
        <SearchBar
          onInput={onSearch}
          onSearch={onSearch}
          onClear={onClear}
          searchPlaceholder={searchPlaceholder}
        />
      )}
      </div>
      {/* 列表区域 */}
      <div className='contentWrap'>
        {groupedConversationItems?.map((groupedItem: GroupedItem, index) => {
          return (
            <div key={index} className='conversation'>
              <div className='title'>
                <GroupTitle title={groupedItem.group} />
              </div>
              {groupedItem?.items?.map((conversation: ConversationItemProps) => {
                return (
                  <div className='item' onClick={() => onConversationClick(conversation)} key={conversation.conversationId}>
                    <ConversationsItemComponent
                      {...conversation}
                      key={conversation.conversationId}
                      activeConversationId={activeConversationId}
                    />
                  </div>
                )
              })}
            </div>
          );
        })}
      </div>
      {/* 底部区域 */}
      {renderFooter && renderFooter()}
    </div>
  );
};
