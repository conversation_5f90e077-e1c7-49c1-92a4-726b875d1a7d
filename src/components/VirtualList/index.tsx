/* eslint-disable import/no-extraneous-dependencies */
import React, { useState, useRef, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';

interface ListItem {
  id: string;
  content: string;
}

export const VirtualList: React.FC = () => {
  const [items] = useState<ListItem[]>(() =>
    Array.from({ length: 1000 }, (_, i) => ({
      id: `item-${i}`,
      content: `Item ${i + 1} - ${'Lorem ipsum dolor sit amet '.repeat(Math.floor(Math.random() * 10))}`
    }))
  );

  const parentRef = useRef<HTMLDivElement>(null);
  const itemSizeCache = useRef<Map<number, number>>(new Map());

  const rowVirtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: (index) => itemSizeCache.current.get(index) ?? 60, // 默认高度60px
    overscan: 5,
  });

  const measureSize = useCallback((node: HTMLDivElement | null, index: number) => {
    if (node) {
      const size = node.getBoundingClientRect().height;
      const currentSize = itemSizeCache.current.get(index);
      
      // 只有当高度变化时才更新
      if (currentSize !== size) {
        itemSizeCache.current.set(index, size);
        // 通知虚拟化器重新计算布局
        rowVirtualizer.measure();
      }
    }
  }, [rowVirtualizer]);

  return (
    <div style={{ width: '100%', maxWidth: '600px', margin: '0 auto', height: '500px', flex: '1'}}>
      <h2>Virtual List with Dynamic Heights</h2>
      
      <div
        ref={parentRef}
        style={{
          height: '100%',
          overflow: 'auto',
          border: '1px solid #ccc',
          borderRadius: '4px',
          position: 'relative', // 添加相对定位
        }}
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {rowVirtualizer.getVirtualItems().map((virtualItem) => {
            const item = items[virtualItem.index];
            
            return (
              <div
                key={item.id}
                ref={(node) => measureSize(node, virtualItem.index)}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  transform: `translateY(${virtualItem.start}px)`,
                  boxSizing: 'border-box',
                  borderBottom: '1px solid #eee',
                  // 确保项目不会超出容器
                  overflow: 'hidden',
                }}
                data-index={virtualItem.index} // 用于调试
              >
                <div>
                  <strong>{item.id}</strong>
                  <p>{item.content}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      
      <div style={{ marginTop: '16px' }}>
        <p>Total items: {items.length}</p>
        <p>Rendered items: {rowVirtualizer.getVirtualItems().length}</p>
        <p>Average item height: {Math.round(rowVirtualizer.getTotalSize() / items.length)}px</p>
      </div>
    </div>
  );
};