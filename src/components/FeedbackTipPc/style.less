.FeedbackTipPcWrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  *,
  :after,
  :before {
    box-sizing: border-box;
  }

  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 12;
  }

  .FeedbackTipPc {
    position: relative;
    // width: 60vw;
    // height: 90vh;
    width: 460px;
    // width: 60vw;
    // height: 406px;
    background: #FFFFFF;
    border-radius: 2px;
    z-index: 13;

    &-icon {
      width: 18px;
      height: 18px;
      margin-right: 8px;
    }

    &-titleClose {
      width: 14px;
      height: 14px;
      cursor: pointer;
    }

    &-title {
      width: 100%;
      height: 78px;
      padding: 30px 20px 24px 20px;
      background: @feedback-tip-title-bg;
      border-radius: 2px 2px 0px 0px;
      // border: 1px solid;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;

    }

    &-titleText {
      font-size: @feedback-tip-titleText-font-size;
      font-weight: normal;
      color: @feedback-tip-titleText-font-color;
      line-height: 24px;
      background: @feedback-tip-titleText-bg;
      -webkit-background-clip: text;
      -webkit-text-fill-color: @feedback-tip-titleText-font-color;
    }

    &-content {
      padding: 0 30px;
      // height: 670px;
      height: calc(100% - 158px);
      overflow-y: auto;
      margin-bottom: 20px;
      color: #666;
    }

    &-labels {
      display: flex;
      flex-wrap: wrap;
      /* 关键属性：允许换行 */
      // gap: 15px 10px;
      /* 可选：设置子元素间距 */
      // margin-top: 15px;
    }


    &-contentText {
      font-size: 14px;
      color: #666666;
      line-height: 22px;
      margin-top: 6px;
    }

    &-labelItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 15px;
      margin-right: 10px;
      padding-top: 4px;
      width: 90px;
      height: 30px;
      background: #FFFFFF;
      border-radius: 3px;
      cursor: pointer;

      font-size: 14px;
      color: #4A4A4A;
      line-height: 20px;

      &:last-child {
        padding-bottom: 20px;
      }
    }

    &-labelItemActive {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 15px;
      margin-right: 10px;
      padding-top: 4px;
      width: 90px;
      height: 30px;
      background: #FFFFFF;
      border-radius: 3px;
      border: 1px solid #108EE9;
      cursor: pointer;

      font-size: 14px;
      color: #108EE9;
      line-height: 20px;
    }

    &-labelItem-input {
      // width: 400px;
      height: 72px;
      margin-top: 15px;
      background: #FFFFFF;
      border-radius: 3px;
      border: 1px solid #CCCCCC;
    }
  }

  @media (min-width: 1919px) {
    .FeedbackTipPc {
      width: 460px;
    }
  }

  @media (min-height: 1079px) {
    .FeedbackTipPc {
      // height: 406px;
    }
  }

  .FeedbackTipFooter {
    float: right;
    display: flex;
    align-items: center;
    padding: 0 30px 30px 0;
  }

  .FooterCancelBtn {
    width: 80px;
    padding: 6px 12px;
    text-align: center;
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
    margin-right: 10px;
    font-size: 14px;
    font-weight: 400;
    color: #4A4A4A;
    line-height: 20px;
    cursor: pointer;
  }

  .FooterOkBtn {
    float: right;
    width: 80px;
    background: #108EE9;
    text-align: center;
    padding: 6px 12px;
    cursor: pointer;

    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
  }

  .FooterOkBtnDisable {
    float: right;
    width: 80px;
    background: #CCCCCC;
    text-align: center;
    padding: 6px 12px;
    cursor: not-allowed;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
  }

  /* 整个滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    /* 垂直滚动条宽度 */
    height: 6px;
    /* 水平滚动条高度 */
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent;
    /* 轨道背景色 */
    // border-radius: 6px;
    /* 轨道圆角 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #E5E7EE;
    /* 滑块背景色 */
    border-radius: 3px;
    /* 滑块圆角 */
    border: 3px solid #f1f1f1;
    /* 滑块边框 */
    width: 6px;
    height: 45px;
    background: #E5E7EE;
    border-radius: 3px;
  }

  /* 滚动条滑块悬停效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
    /* 悬停时滑块背景色 */
  }
}
