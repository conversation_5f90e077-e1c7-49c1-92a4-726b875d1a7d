import React from 'react';
import { mountComponent } from '../../utils/mountComponent';
import { ToastModal } from './ToastModal';
import { ToastProps } from './interface';

function show(content: string, type?: ToastProps['type'], subContent?: string, position?: ToastProps['position'], root?: HTMLElement) {
  mountComponent(<ToastModal content={content} type={type} position={position} subContent={subContent} />, root);
}

export const toastModal = {
  show,
  success(content: string, subContent?: string, position?: ToastProps['position'], root?: HTMLElement) {
    show(content, 'success', subContent, position, root);
  },
  fail(content: string, subContent?: string, position?: ToastProps['position'], root?: HTMLElement) {
    show(content, 'error', subContent, position, root);
  },
  loading(content: string, subContent?: string, position?: ToastProps['position'], root?: HTMLElement) {
    show(content, 'loading', subContent, position, root);
  },
};

export { ToastModal };
