.ToastModal-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  opacity: 0;
}

.ToastModal {
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  right: 0;
  width: 220px;
  height: 154px;
  padding: 30px 20px 20px 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: all 300ms ease 0s;
  transform: translateX(-50%) translateY(-50%);
  opacity: 0;
  visibility: hidden;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
  border-radius: 8px;

  &[data-type='success'] .Icon {
    color: @green;
  }

  &[data-type='success'] .ToastModal-content {
    display: flex;
    align-items: center;

    img {
      width: 14px;
      height: 14px;
      margin-right: 10px;
    }

    & .ToastModal-message {
      color: #333;
    }
  }

  &[data-type='success'] .ToastModal-subContent {
    margin-top: 8px;
    font-size: 13px;
    font-weight: 400;
    color: #777777;
  }

  &[data-type='success'] .ToastModal-OkBtn {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 104px;
    height: 32px;
    border-radius: 20px;
    margin-left: 33px;
    margin-top: 24px;
    padding: 6px 0;
    border: 1px solid #3E74F7;
    cursor: pointer;

    font-size: 14px;
    font-weight: 400;
    color: #3E74F7;
  }



  &[data-type='error'] .Icon {
    color: @red;
  }

  &[data-type='loading'] .Icon {
    color: @brand-1;
  }

  &.show {
    opacity: 1;
    visibility: visible;
  }

  .Icon {
    margin-right: 6px;
    font-size: 24px;
  }
}

.ToastModal-top {
  top: 15%;
}

.ToastModal-bottom {
  top: auto;
  bottom: 15%;
}

.ToastModal-typing {
  top: auto;
  bottom: 5px;
  transform: none;
  right: 15px;
  justify-content: flex-end;

  .ToastModal-content {
    // background: #cfe2ff;
    background: @toast-typing-content-bg;
    border-radius: 20px;
    font-size: 12px;
    line-height: 30px;
    text-align: center;
    padding: 0 10px;
  }

  .ToastModal-message {
    line-height: 30px;
    // color: #3e74f7;
    color: @toast-typing-message-color;
  }
}

.ToastModal-sensitive {
  top: auto;
  bottom: 100%; // 整个飘在输入框上面
  transform: none;
  right: 0;
  justify-content: flex-end;

  .ToastModal-content {
    background: rgba(227, 60, 57, 0.1);
    max-width: max-content;
    border-radius: 20px;
    font-size: 12px;
    line-height: 30px;
    padding: 0 10px;
  }

  .ToastModal-message {
    line-height: 30px;
    color: #e33c39;
  }
}

.ToastModal-message {
  flex: 1;
  font-size: 14px;
  color: @toast-message-color;
  line-height: 24px;
  word-break: break-word;
  margin-top: 0;
  margin-bottom: 0;
}
