import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import { Icon } from '../Icon';
import { ToastProps } from './interface';
import successSrc from './images/success.svg'

function renderIcon(type: ToastProps['type']) {
  switch (type) {
    case 'success':
      return <img src={successSrc} />;
    case 'error':
      return <Icon type="warning-circle" />;
    case 'loading':
      return <Icon type="spinner" spin />;
    default:
      return null;
  }
}

export const ToastModal: React.FC<ToastProps> = (props) => {
  const { content, type, subContent, position = 'center', onUnmount } = props;
  const [show, setShow] = useState(false);

  useEffect(() => {
    setShow(true);

  }, [onUnmount]);

  return (
    <>
      {show && <div className='ToastModal-mask'></div>}
      <div
        className={clsx('ToastModal', { show }, position && `ToastModal-${position}`)}
        data-type={type}
        role="alert"
        aria-live="assertive"
        aria-atomic="true"
      >
        <div className="ToastModal-content" role="presentation">
          {renderIcon(type)}
          <p className="ToastModal-message">{content}</p>
        </div>
        <div className='ToastModal-subContent'>
          {subContent}
        </div>
        <div className='ToastModal-OkBtn' onClick={() => setShow(false)}>确定</div>
      </div>
    </>

  );
};
