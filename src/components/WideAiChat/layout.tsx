import React, { useState } from 'react';
import { WideAiChatProps } from './interface';
import { HistoryConversation } from '../HistoryConversation';
import { ChatContextProps } from '../../hooks/useChatContext';
import ExpandRight from './images/expandRight.png';

export const WideAiChatLayout = (
  props: WideAiChatProps & { children: React.ReactNode; chatContext: ChatContextProps | undefined },
) => {
  const { children, renderHistoryConversation: CustomSider, chatContext, config, historyConversation } = props;
  const [showCollapseButton, setShowCollapseButton] = useState(true);
  const [showExpandButton, setShowExpandButton] = useState(false);
  let sideBar;
  if (CustomSider) {
    const localProps = {
      ...historyConversation,
      config,
      chatContext,
    };
    sideBar = <CustomSider {...localProps} />;
  } else {
    const { isNewConversation, handleNewConversation, toggleHistory, historyConversationList, conversationId, selectHistoryConversation } = chatContext || {};
    const conversationNavbarProps = {
      isNewConversation: isNewConversation, // 历史会话页默认传false
      onNewButtonClick: () => {
        handleNewConversation();
      },
      onCloseButtonClick: () => {
        toggleHistory();
      },
      showCloseButton: false,
      showNewButton: false,

      // showExpandButton: showExpandButton,
      // onExpandButtonClick: () => {
      //   console.log('展开历史会话面板');
      //   setShowExpandButton(false);
      //   setShowCollapseButton(true);
      // },
      showCollapseButton: showCollapseButton,
      onCollapseButtonClick: () => {
        console.log('折叠历史会话面板');
        setShowExpandButton(true);
        setShowCollapseButton(false);
      },
    }
    const renderBrand = () => {
      return (
        historyConversation?.renderBrand && <div onClick={handleNewConversation} style={{
          marginTop: '44px',
          marginBottom: '40px',
        }}>{historyConversation?.renderBrand()}</div>
      )
    };
    sideBar = (
      <HistoryConversation
        // style={{ height: 'calc(100% - 6px)', width: 'calc(100% - 6px)', margin: 0 }}
        style={{ margin: 0 }}
        title={historyConversation?.title}
        logo={historyConversation?.logo || config?.robot?.logo || ''}
        navbar={conversationNavbarProps}
        list={historyConversationList || []}
        activeConversationId={conversationId?.current}
        onConversationClick={(e: any) => selectHistoryConversation(e)}
        renderBrand={renderBrand}
        renderFooter={historyConversation?.renderFooter}
        showSearch={historyConversation?.showSearch}
      />
    );
  }

  const handleExpandClick = () => {
    console.log('展开历史会话面板');
    setShowExpandButton(false);
    setShowCollapseButton(true);
  }
  const miniSideBar = () => {
    const logo = historyConversation?.logo || config?.robot?.logo || '';

    return (
      <div className='CollapseSider'>
        <div className='CollapseSideContent'>
          <img src={logo} alt='' className='CollapseSiderLogo'></img>
          <img src={ExpandRight} alt='' className='SiderExpand' onClick={handleExpandClick} />
          <div className='SiderNewBtn' onClick={chatContext?.handleNewConversation}>
            <div className='SiderNewBtnText'>
              +
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`WideAiChatLayout ${!sideBar ? 'onlyChatContainer' : 'normal'}`} id='ChatComponentsWrap'>
      {showExpandButton ? miniSideBar() :
        <div className="WideAiChatSider">{sideBar}</div>
      }
      <div className="WideAiChatContent">{children}</div>
    </div>
  );
};
