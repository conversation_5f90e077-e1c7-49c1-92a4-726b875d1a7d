.WideAiChatLayout {
  display: flex;
  flex-direction: row;
  position: relative;
  height: 100%;
  border-radius: inherit;
  border-top: 1px solid #DEE8F5;

  .CollapseSider {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80px;
    height: 100%;
    background: #FFFFFF;
    box-shadow: 2px 0px 6px 0px rgba(0, 0, 0, 0.08);

    .CollapseSideContent {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 25px 20px;
      width: 80px;
      height: 370px;
      background: linear-gradient(180deg, #DFEBF7 0%, #FFFFFF 100%);
    }

    .CollapseSiderLogo {
      width: 32px;
      height: 32px;
    }

    .SiderExpand {
      width: 16px;
      height: 16px;
      margin-top: 55px;
      margin-bottom: 35px;
      cursor: pointer;
    }

    .SiderNewBtn {
      width: 40px;
      height: 40px;
      background: #3E74F7;
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
    }

    .SiderNewBtnText {
      width: 16px;
      height: 16px;
      font-size: 16px;
      line-height: 16px;
      text-align: center;
    }
  }

  &.onlyChatContainer {
    >.WideAiChatSider {
      display: none;
    }

    >.WideAiChatContent {
      padding: 0;
      margin: 0;
    }
  }

  >.WideAiChatSider {
    width: 290px;
    display: flex;
    flex-direction: column;
    height: 100%;
    border-right: 1px solid #DEE8F5;
    // background-color: #f9fbff;
    background: linear-gradient(180deg, #DFEBF7 0%, #FFFFFF 44%);

  }

  >.WideAiChatContent {
    flex: 1;
    height: 100%;
    padding: 0;
    margin: 0;
  }

  .PushDivContainer {
    justify-content: center;
  }

  .MobileNavBar {
    padding-top: 12px;
  }

  .logoIcon {
    width: 30px;
    height: 30px;
  }

  .ChatWrap {
    min-width: auto;
    // max-width: 800px;

    .WideMessageList {
      // padding-left: max(calc(50% - 660px), 24px);
      // padding-right: max(calc(50% - 660px), 24px);
      margin-left: calc(50% - 450px);
      margin-right: calc(50% - 450px);
      padding-left: 0;
      padding-right: 0;
      width: 900px;
    }

    .GuidePagePC .ChatContent {
      // padding-left: max(calc(50% - 660px), 24px);
      // padding-right: max(calc(50% - 660px), 24px);
      margin-left: calc(50% - 450px);
      margin-right: calc(50% - 450px);
      padding-left: 0;
      padding-right: 0;
      width: 900px;
    }

    .ChatFooter {
      // padding-left: max(calc(50% - 660px), 24px);
      // padding-right: max(calc(50% - 660px), 24px);
      margin-left: calc(50% - 450px);
      margin-right: calc(50% - 450px);
      padding-left: 0;
      padding-right: 0;
      width: 900px;
    }
  }

  .Toast-typing {
    right: calc(50% - 450px);
  }
}