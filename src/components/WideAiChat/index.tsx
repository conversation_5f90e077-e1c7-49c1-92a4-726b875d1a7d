import React, { useRef, useEffect, useState, useImperativeHandle } from 'react';
import { AiChat } from '../AiChat';
import { AiChatHandle } from '../AiChat/interface';
import { WideAiChatLayout } from "./layout";
import type { WideAiChatProps } from './interface';
import { ChatContextProps } from '../../hooks/useChatContext';
export type { WideAiChatProps } from './interface';
import { UploadFile } from '../Composer/wide/Upload';


export const WideAiChat = React.forwardRef<AiChatHandle | null, WideAiChatProps>((props: WideAiChatProps, ref) => {
  const aiChatRef = useRef<AiChatHandle>(null);
  const [chatConext, setChatConext] = useState<ChatContextProps>();
  useEffect(() => {
    const handler = (e?: any) => {
      // 实现一个 getter setter 类
      setChatConext({
        ...(aiChatRef.current?.chatContext || {}),
        ...(e?.detail || {}),
        onSend: (type: string, val: string, payload?: object, transformedFiles?: UploadFile[]) => aiChatRef.current?.chatContext?.onSend(type, val, payload, transformedFiles),
        handleStopAnswer: () => aiChatRef.current?.chatContext?.handleStopAnswer(),
        handleNewConversation: () => aiChatRef.current?.chatContext?.handleNewConversation(),
        selectHistoryConversation: (id: string) => aiChatRef.current?.chatContext?.selectHistoryConversation(id),
        toggleHistory: () => aiChatRef.current?.chatContext?.toggleHistory(),
      } as ChatContextProps);
    };
    window.document.addEventListener('chatui-update-chat-context', handler);
    handler();
    return () => {
      window.document.removeEventListener('chatui-update-chat-context', handler);
    }
  }, []);
  useImperativeHandle(ref, () => {
    return aiChatRef.current as AiChatHandle;
  });
  return (<WideAiChatLayout {...props} chatContext={chatConext as ChatContextProps}>
    <AiChat {...props} ref={aiChatRef} />
  </WideAiChatLayout>);
});