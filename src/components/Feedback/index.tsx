/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useCallback, useState } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import copy from 'copy-to-clipboard';
import { MessageProps } from '../Message';
import { Tooltip } from '../Tooltip';
import { FeedbackTipApp } from '../FeedbackTipApp';
import { FeedbackTipPc } from '../FeedbackTipPc';
import { isMobile } from '../../utils/canUse';
import DefaultDown from './images/default-down.svg'
import DefaultUp from './images/default-up.svg'
import ThumbsDown from './images/thumbs-down.svg'
import ThumbsUp from './images/thumbs-up.svg'
import Copy from './images/copy.svg'
import { LogPointConfigMap, ILogParams } from '../../LogPointConfigMap';
import { getThinkContent } from '../../utils/aiChat';
import { resetCitations } from '../AiChat/utils';
import { toastModal } from '../ToastModal';

interface LabelProps {
  id: string,
  label: string
}
export interface FeedbackProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
 * 消息
 */
  message: MessageProps;
  /**
   * 点赞点踩回调
   */
  onFeedBack?: (score: MessageProps['feedbackResult'], message: MessageProps, reason?: any) => void;
  /**
  * log上报回调
  */
  onReportLog?: (params: ILogParams | undefined) => void;

  /**
  * 复制文本回调
  */
  copyText?: (text: string) => void;
  showFeedbackModal?: boolean;
  feedbackLabels?: LabelProps[];
  /**
    * 点踩反馈弹窗配置
    */
  feedbackModalConfig?: {
    title?: string,
    inputPlaceholder?: string,
    showLabels?: boolean,
  };
  /**
 * 是否预览模式
 */
  isPreview?: boolean;
}

export const Feedback = (props: FeedbackProps) => {
  const { onReportLog, onFeedBack, message, copyText, showFeedbackModal = false, feedbackLabels, feedbackModalConfig, isPreview = false } = props;
  const upSrc = message?.feedbackResult === 'good' ? ThumbsUp : DefaultUp;
  const downSrc = message?.feedbackResult === 'bad' ? ThumbsDown : DefaultDown;

  // 点踩反馈弹窗
  const [showFeedbackTip, setShowFeedbackTip] = useState(false);

  const handleCopy = useCallback(() => {
    if (isPreview) {
      return;
    }
    // 只复制正文（去掉开头的换行符）
    let copyMsg = getThinkContent(message?.content?.text)?.content?.replace(/^\n+/, '') || '';
    for (let i = 0; i < (message?.contents?.length ?? 0); i++) { 
      copyMsg = copyMsg
        + (message?.contents?.[i]?.sub_problem_title ? `### **${message?.contents?.[i]?.sub_problem_title}** \n`: '')
        + (message?.contents?.[i]?.content ?? '');
    }
    // 去掉<think>和</think>
    const copyMsgWithoutThinkTag = copyMsg?.replace(/<think>|<\/think>/g, '');
    // 将span包裹的引用重置回[citation:数字]
    const copyMsgWithoutCitationSpan = resetCitations(copyMsgWithoutThinkTag);
    if (copyText) {
      copyText(copyMsgWithoutCitationSpan);
    } else {
      copy(copyMsgWithoutCitationSpan);
      // toast.success('复制成功', 1000, 'center');
      toastModal.success('复制成功', '仅供参考，请勿直接对客转发', 'center');
    }
    // 埋点上报
    const params = LogPointConfigMap.get('handleAnswerCopy');
    if (params) {
      onReportLog?.({
        ...params,
        btn_title: {
          ...(typeof params.btn_title === 'object' ? params.btn_title : {}),
          value: message?.messageId,
        }
      });
    }
  }, [isPreview, message?.content?.text, message?.contents, message?.messageId, copyText, onReportLog]);

  // 点踩反馈弹窗展示
  const handleFeedBackOpen = () => {
    setShowFeedbackTip(true);
  }

  // 点踩反馈弹窗关闭
  const handleFeedbackClose = () => {
    setShowFeedbackTip(false);
  }

  // 点赞操作
  const handleLike = () => {
    if (isPreview) {
      return;
    }
    onFeedBack && onFeedBack('good', message);
  }

  // 点踩操作
  const handleDisLike = () => {
    if (isPreview) {
      return;
    }
    // 不需要展示反馈弹窗  或  取消点踩  可直接下发接口
    if (!showFeedbackModal || message?.feedbackResult === 'bad') {
      onFeedBack && onFeedBack('bad', message);
      return;
    }
    // 需要展示反馈弹窗  且  点踩(包括点赞直接到点踩)  展示反馈弹窗
    if (showFeedbackModal && (!message?.feedbackResult || message?.feedbackResult === 'good')) {
      handleFeedBackOpen();
    }

  }

  // 反馈弹窗提交操作
  const handleFeedBackSubmit = (eTags?: any, eValue?: string) => {
    console.log('点踩反馈标签提交数据', eTags, eValue)
    const reason = { selectedIds: eTags?.map((t: { id: string; }) => t.id) || [], inputReason: eValue || '' }
    onFeedBack && onFeedBack('bad', message, reason);
  }

  if (!message?.needFeedback && !message?.showToken) {
    return null;
  }

  return (
    <div className="FeedbackWrap">
      {
        message?.showToken && (
          <div className="FeedbackToken">
            <div>{message?.totalTime ?? 0}s</div>
            <div>{message?.totalTokens ?? 0}Tokens</div>
          </div>
        )
      }
      {
        message?.needFeedback && (

          <div className="Feedback">
            {showFeedbackModal &&
              <>
                {showFeedbackTip && isMobile && <FeedbackTipApp onClose={handleFeedbackClose} labels={feedbackLabels} onSubmit={handleFeedBackSubmit} {...feedbackModalConfig} />}
                {showFeedbackTip && !isMobile && <FeedbackTipPc onClose={handleFeedbackClose} labels={feedbackLabels} onSubmit={handleFeedBackSubmit} {...feedbackModalConfig} />}
              </>
            }
            {
              isMobile ? (
                <div onClick={handleCopy}>
                  <img src={Copy} alt="copy" className='Thumbs' />
                </div>
              )
                : (
                  <Tooltip content='复制' placement='bottom' >
                    <div onClick={handleCopy}>
                      <img src={Copy} alt="copy" className='Thumbs' />
                    </div>
                  </Tooltip>
                )
            }
            {
              isMobile ? (
                <div onClick={handleLike}>
                  <img src={upSrc} alt="up" className='Thumbs' />
                </div>
              )
                : (
                  <Tooltip content='喜欢' placement='bottom' >
                    <div onClick={handleLike}>
                      <img src={upSrc} alt="up" className='Thumbs' />
                    </div>
                  </Tooltip>
                )
            }
            {
              isMobile ? (
                <div onClick={handleDisLike}>
                  <img src={downSrc} alt="down" className='Thumbs' />
                </div>
              )
                : (
                  <Tooltip content='不喜欢' placement='bottom' >
                    <div onClick={handleDisLike}>
                      <img src={downSrc} alt="down" className='Thumbs' />
                    </div>
                  </Tooltip>
                )
            }
          </div>
        )
      }
    </div>
  );
};