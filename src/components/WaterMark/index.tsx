/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef } from 'react';
import { WatermarkProps } from './interface';

export const WaterMark: React.FC<WatermarkProps> = ({
    show,
    text = '', // 水印内容
    opacity = 1, // 水印透明度
    rotate = -45, // 水印旋转角度
    fontSize = 16, // 水印字体大小
    color = 'rgba(0, 0, 0, 0.06)', // 水印颜色
    zIndex = 1000, // 水印层级
    children, // 子组件（页面内容）
    canvasWidth = 160, // Canvas 宽度，不传默认160
    canvasHeight = 160, // Canvas 高度，不传默认160
}) => {
    const canvasRef = useRef<any>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (!show || !canvasRef.current || !containerRef.current) {
            return;
        }
        console.log('watermark', show);
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        // 设置 Canvas 大小
        canvas.width = canvasWidth; // 水印宽度
        canvas.height = canvasHeight; // 水印高度

        // 绘制水印
        ctx.font = `${fontSize}px Arial`;
        ctx.fontWeight = 400;
        ctx.fillStyle = color;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.translate(canvas.width / 2, canvas.height / 2); // 将原点移动到 Canvas 中心
        ctx.rotate((rotate * Math.PI) / 180); // 旋转水印
        ctx.fillText(text, 0, 0); // 绘制水印文字

        // 将canvas转为背景图
        containerRef.current.style.backgroundImage = `url(${canvas.toDataURL()})`;
        containerRef.current.style.backgroundRepeat = 'repeat';
    }, [text, opacity, rotate, fontSize, color, show]);

    return (
        <div className="watermark-container" style={{ position: 'relative', zIndex: 1 }}>
            {/* 页面内容 */}
            {children}

            {/* 水印层 */}
            {show && <div
                ref={containerRef}
                className="watermark-overlay"
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundImage: `url(${canvasRef.current?.toDataURL()})`,
                    backgroundRepeat: 'repeat',
                    opacity: opacity,
                    pointerEvents: 'none', // 防止水印层干扰交互
                    zIndex: zIndex,
                }}
            />}

            {/* Canvas 用于生成水印图片 */}
            <canvas ref={canvasRef} style={{ display: 'none' }} />
        </div >
    );
};
