.MobileNavBar {
  padding: 8px 20px 0 15px;
  box-shadow: 0 15px 15px -10px @navbar-box-shadow;
  z-index: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: @navbar-height;
  background: @navbar-bg;
  position: relative;
  box-sizing: border-box;

  .returnButtonIcon {
    width: 12px;
    height: 20px;
  }

  .logoIcon {
    width: 28px;
    height: 28px;
  }

  .historyButtonIcon {
    width: 28px;
    height: 28px;
  }

  .newButtonIcon {
    margin-left: 12px;
    width: 28px;
    height: 28px;
  }

  .closeButtonIcon {
    margin-left: 12px;
    width: 20px;
    height: 20px;
  }

  .titleCenter {
    height: 100%;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      left: 50%;
      top: calc(50% + 4px);
      transform: translate(-50%, -50%);
    }
  }

  .titleLeft {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .title {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 0;

    img {
      margin-right: 14px;
    }

    span {
      display: block;
      font-size: @navbar-title-font-size;
      font-weight: bold;
      color: @navbar-title-color;
      line-height: 24px;
      white-space: nowrap;
      max-width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      flex: 1;
    }
  }
}


.PcNavBar {
  padding: 8px 20px 0 15px;
  box-shadow: 0 15px 15px -10px @navbar-box-shadow;
  z-index: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: @navbar-height;
  background: @navbar-bg;
  position: relative;

  .returnButtonIcon {
    width: 12px;
    height: 20px;
  }

  .logoIcon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
  }

  .historyButtonIcon {
    margin-right: 21px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .newButtonIcon {
    margin-right: 21px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .closeButtonIcon {
    width: 16px;
    height: 16px;
    // margin-right: 16px;
    cursor: pointer;
  }

  .titleCenter {

    height: 100%;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .title {
      position: absolute;
      left: 50%;
      top: calc(50% + 4px);
      transform: translate(-50%, -50%);
    }
  }

  .titleLeft {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .title {

    display: flex;
    justify-content: center;
    align-items: center;
    flex: 0;

    img {
      margin-right: 14px;
    }

    span {
      font-size: @navbar-title-font-size;
      font-size: @navbar-title-font-size;
      font-weight: bold;
      color: @navbar-title-color;
      color: @navbar-title-color;
      line-height: 24px;
      white-space: nowrap;
    }
  }
}

.collapseButtonIcon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.zoomReduceButtonIcon {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin-right: 20px;
}
