/**
 * https://xcg1a1l6ku.feishu.cn/wiki/C5IHwFpLNiKA7qkScCzc1PIOnVf
 */
import React, { useEffect, useState } from 'react';
import { Tooltip } from '../Tooltip';
import deepseekChat from './images/deepseekChat.png';
import AppBack from './images/appBack.png';
import AppHistory from './images/appHistory.png';
import AppNew from './images/appNewChat.png';
import PcHistory from './images/pcHistory.png';
import PcNew from './images/pcNewChat.png';
import PcClose from './images/pcClose.png';
import CollapseLeft from './images/collapseLeft.png';
import ExpandRight from './images/expandRight.png';
import CollapseWindowscreen from './images/collapseWindowscreen.png';
import ExpandFullscreen from './images/expandFullscreen.png';

export type NavbarProps = {
  open?: boolean,//是否展示navbar，默认true

  // 返回按钮设置项
  showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
  returnButtonIcon?: string;//返回按钮图标路径
  onReturnButtonClick?: () => void;//点击返回按钮响应处理函数

  // 标题区域设置
  showLogo?: boolean,//是否展示logo,默认为true
  logo?: string, // logo图标的地址
  title?: string; //头部标题文案，展示于logo右侧,默认为空字符串
  logoAndTitlePosition?: string;//标题区域的位置：pc端默认靠左边，移动端默认居中

  // 历史会话按钮设置项
  showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
  historyButtonIcon?: string,//历史会话按钮图标路径
  historyButtonPosition?: string,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
  onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数


  //新建会话按钮设置项
  showNewButton?: boolean;//是否显示新建会话按钮，默认为true
  newButtonIcon?: string,//新建会话按钮图标路径
  newButtonPosition?: string,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联新建对话配置的pushPosition
  onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

  //折叠面板按钮设置项
  showCollapseButton?: boolean;//是否显示折叠面板按钮，默认为false
  collapseButtonIcon?: string,//折叠面板按钮图标路径
  onCollapseButtonClick?: () => void; //点击折叠面板按钮响应处理函数

  //展开面板按钮设置项
  showExpandButton?: boolean;//是否显示展开面板按钮，默认为false
  expandButtonIcon?: string,//展开面板按钮图标路径
  onExpandButtonClick?: () => void; //点击展开面板按钮响应处理函数

  // 折叠到窗口按钮设置项
  showCollapseToWindowButton?: boolean;//是否显示折叠到窗口按钮，默认为false
  collapseToWindowButtonIcon?: string,//折叠到窗口按钮图标路径
  onCollapseToWindowButtonClick?: () => void; //点击折叠到窗口按钮响应处理函数

  // 放大到全屏按钮设置项
  showExpandToFullButton?: boolean;//是否显示放大到全屏按钮，默认为false
  expandToFullButtonIcon?: string,//放大到全屏按钮图标路径
  onExpandToFullButtonClick?: () => void; //点击放大到全屏按钮响应处理函数

  onSwitchWideNarrow?: () => void;

  // 关闭按钮设置项
  showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
  closeButtonIcon?: string; //关闭按钮图标路径
  onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数

  newConTooltip?: string;// 新建会话按钮提示语,pc必传值，app不传值
  wrapstyle?: React.CSSProperties; // CSS样式
};

const NavbarC: React.FC<NavbarProps> = (props) => {

  const {
    open = true,
    newConTooltip,
    // 返回按钮设置项
    showReturnButton,
    returnButtonIcon = AppBack,
    onReturnButtonClick,

    // 标题区域设置
    showLogo = true,
    logo = deepseekChat,
    title = '',
    logoAndTitlePosition = 'left',

    // 历史会话按钮设置项
    showHistoryButton = true,
    historyButtonIcon = newConTooltip ? PcHistory : AppHistory,
    historyButtonPosition = 'right',
    onHistoryButtonClick,


    //新建会话按钮设置项
    showNewButton = true,
    newButtonIcon = newConTooltip ? PcNew : AppNew,
    newButtonPosition = 'right',
    onNewButtonClick,

    // 关闭按钮设置项
    showCloseButton,
    closeButtonIcon = PcClose,
    onCloseButtonClick,

    //折叠面板按钮设置项
    showCollapseButton,
    collapseButtonIcon = CollapseLeft,
    onCollapseButtonClick,

    //展开面板按钮设置项
    showExpandButton,
    expandButtonIcon = ExpandRight,
    onExpandButtonClick,

    // 折叠到窗口按钮设置项
    showCollapseToWindowButton,
    collapseToWindowButtonIcon = CollapseWindowscreen,
    onCollapseToWindowButtonClick,

    // 放大到全屏按钮设置项
    showExpandToFullButton,
    expandToFullButtonIcon = ExpandFullscreen,
    onExpandToFullButtonClick,

    onSwitchWideNarrow,

    wrapstyle,
  } = props;

  const [titleTooltipShow, setTitleTooltipShow] = useState(false);
  useEffect(() => {
    const titleEle = document.getElementById('navbarTitle');
    if (titleEle) {
      if (titleEle.scrollWidth > titleEle.clientWidth) {
        setTitleTooltipShow(true);
      } else {
        setTitleTooltipShow(false);
      }
    }
  }, []);

  const TooltipImg = (prop: any) => {
    if (newConTooltip) return (
      <Tooltip {...prop.tooltip} >
        <img {...prop} />
      </Tooltip>
    )
    return (
      <img {...prop} />
    )
  }

  if (!open) return null;
  return (
    <div style={wrapstyle}>
      <div className={newConTooltip ? 'PcNavBar' : 'MobileNavBar'}>
        {showReturnButton && <img src={returnButtonIcon} className="returnButtonIcon" onClick={onReturnButtonClick} />}
        {showHistoryButton && historyButtonPosition === 'left' && <TooltipImg src={historyButtonIcon} className="historyButtonIcon" onClick={onHistoryButtonClick} tooltip={{
          content: '查看历史会话',
          placement: 'bottomRight'
        }} />}
        {showNewButton && newButtonPosition === 'left' && <TooltipImg src={newButtonIcon} className="newButtonIcon" onClick={onNewButtonClick} tooltip={{
          content: newConTooltip,
          placement: 'bottomRight'
        }} />}
        <div className={logoAndTitlePosition === 'center' ? 'titleCenter' : 'titleLeft'}>
          <div className='title'>
            {showLogo && <img src={logo || deepseekChat} className="logoIcon" />}
            {titleTooltipShow ? <Tooltip content={title} placement={'bottom'}><span id='navbarTitle'>{title}</span></Tooltip> : <span id='navbarTitle'>{title}</span>}
          </div>
        </div>
        {showHistoryButton && historyButtonPosition === 'right' && <TooltipImg src={historyButtonIcon} className="historyButtonIcon" onClick={onHistoryButtonClick} tooltip={{
          content: '查看历史会话',
          placement: 'bottomLeft'
        }} />}
        {showNewButton && newButtonPosition === 'right' && <TooltipImg src={newButtonIcon} className="newButtonIcon" onClick={onNewButtonClick} tooltip={{
          content: newConTooltip,
          placement: 'bottomRight'
        }} />}
        {/* 历史会话列表面板的折叠按钮 */}
        {showCollapseButton && <img src={collapseButtonIcon} className="collapseButtonIcon" onClick={onCollapseButtonClick} />}
        {/* 历史会话列表面板的展开按钮 */}
        {showExpandButton && <img src={expandButtonIcon} className="collapseButtonIcon" onClick={onExpandButtonClick} />}
        {/* 整个会话页面折叠成弹窗的按钮 */}
        {showCollapseToWindowButton && <img src={collapseToWindowButtonIcon} className="zoomReduceButtonIcon" onClick={() => { onCollapseToWindowButtonClick?.(); onSwitchWideNarrow?.() }} />}
        {/* 整个会话页面展开成全屏的按钮 */}
        {showExpandToFullButton && <img src={expandToFullButtonIcon} className="zoomReduceButtonIcon" onClick={() => { onExpandToFullButtonClick?.(); onSwitchWideNarrow?.() }} />}
        {showCloseButton && <img src={closeButtonIcon} className="closeButtonIcon" onClick={onCloseButtonClick} />}
      </div>
    </div>

  )
}

// 使用 memo 包装这个组件以避免不必要的渲染
export const Navbar = React.memo(NavbarC);
