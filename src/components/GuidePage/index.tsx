/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable compat/compat */
import React, { useEffect } from 'react';
import canUse from '../../utils/canUse';
import { LocaleProvider } from '../LocaleProvider';
import { Navbar } from '../Navbar';
import isSafari from '../../utils/isSafari';
import deepseekChat from './images/deepseekChat.png';
import { GuidePageProps } from './interface';

export type { GuidePageProps } from './interface';

export const GuidePage = React.forwardRef<HTMLDivElement, GuidePageProps>((props, ref) => {
  const {
    locale = 'zh-CN',
    locales,
    navbar,
    renderNavbar,
    guideWelcome: { showTitle = true, showsubTitle = true, ...guideWelcome },
    onRiskClick,
    isAorta = false,
    keyboardShow = false,
    onSend,
  } = props;

  // 用来区分手机端、pc端
  const isMobile = canUse('touch');

  useEffect(() => {
    if (isSafari()) {
      document.documentElement.dataset.safari = '';
    }
  }, []);

  const handleQuestionClick = (type: string, content: string) => {
    onSend && onSend(type, content);
  }

  const defaultLogoSrc = deepseekChat;
  const defaultTitle = 'Hi～我是 问TA';
  const defaultSubtitle = '作为您的智能助手，可以为您答疑解惑';
  // const deafultRiskText = '1、内容来源：由AI模型（Deepseek R1-32B）自动生成，非人工撰写；\n2、局限性说明：数据来源于海量历史数据，可能存在覆盖不全面，部分数据更新不及时，分析不准确等情况；\n3、使用要求：大模型应用仅作为辅助工具，为您提供展业分析参考。在实际工作应用中，请务必结合自身专业知识，审慎权衡各类因素并验证内容准确性、合规性、及时性，并遵守公司合规指引，禁止直接引用至客户沟通材料。'
  const defaultRiskTip = '内容由AI大模型自动生成，请勿直接作为对客材料 ';


  return (
    <LocaleProvider locale={locale} locales={locales}>
      <div className={isMobile ? "GuidePageApp" : "GuidePagePC"} ref={ref}>
        {renderNavbar ? renderNavbar() : <Navbar {...{ ...navbar }} />}
        <div className='ChatContent'>
          <div className='centerContent'>
            <img src={guideWelcome?.logo || defaultLogoSrc} className={"GuideHeader"}></img>
            {showTitle && <div className='FirstTitle'>{guideWelcome?.title || defaultTitle}</div>}
            {showsubTitle && <div className='SubTitle'>{guideWelcome?.subtitle || defaultSubtitle}</div>}
            {
              !keyboardShow && (
                <div className='RiskDesc' >
                  {isAorta ? <div onClick={onRiskClick} className='RiskDescClick'>
                    <div className='RiskDescText'>{guideWelcome?.riskTip || defaultRiskTip}</div>
                    <div className='RiskDescClickArrow'>{' >>'}</div>
                  </div> :
                    <div className='RiskDescText'>{guideWelcome?.riskTip || defaultRiskTip}</div>
                  }
                </div>
              )
            }
            {guideWelcome?.openMessages?.length ?
              <div className='OpenMessageList'>
                {guideWelcome?.openMessages?.map((item: any, index: number) => {
                  if (item?.type === 'text') {
                    return <div className='WelcomeMsg' key={index}>{item?.content}</div>
                  }
                  if (item?.type === 'list') {
                    return <div key={index} className='RelateQuestion' onClick={() => handleQuestionClick('text', item?.content)} >
                      {item?.content}
                    </div>
                  }
                  return <div className='WelcomeMsg' key={index}>{item?.content}</div>

                })}
              </div> : null
            }
            {/* <div className='WelcomeMsg'>1112222222222222222221</div>
            <div className='RelateQuestion'>2222</div>
            <div className='RelateQuestion'>3333</div>
            <div className='RelateQuestion'>4444</div>
            <div className='RelateQuestion'>5555</div>
            <div className='RelateQuestion'>6666</div> */}
          </div>
        </div>
      </div>
    </LocaleProvider>
  );
});
