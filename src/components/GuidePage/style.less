// & when (@global-style =true) {
//     html {
//         height: 100vh;

//         &[data-safari] {
//             height: calc(100vh - calc(100vh - 100%));
//         }
//     }

//     body,
//     #root {
//         height: 100%;
//     }

//     body {
//         margin: 0;
//     }
// }
// @ai-kit-prefix: obobobobob;
// @guide-page-subtitle-color:red;

.GuidePagePC,
.GuidePageApp {
  display: flex;
  flex-direction: column;
  // height: 100%;
  color: @body-color;
  font-family: @font-family-base;
  line-height: @line-height-base;
  -webkit-tap-highlight-color: transparent;
  background: @guide-page-bg;
  position: relative;
  // background: linear-gradient(180deg, #CFE1F4 0%, #FFFFFF 100%);
  border-radius: inherit;
  flex: 1;

  .ChatContent {
    position: relative;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-height: 0;
    padding-left: 20px;
    padding-right: 20px;
    overflow-y: auto;
    padding-bottom: 12px;

    .centerContent {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      justify-content: center;
    }

    .FirstTitle {
      margin-top: 25px;
      margin-bottom: 12px;
      font-size: 20px;
      font-weight: bold;
      color: @guide-page-title-color;
      line-height: 27px;
    }

    .SubTitle {
      font-size: 14px;
      color: @guide-page-subtitle-color;
      line-height: 19px;
      // margin-bottom: 50px;
    }

    .OpenMessageList {
      max-width: 100%;
    }

    .WelcomeMsg {
      width: fit-content;
      max-width: 100%;
      margin-top: 20px;
      word-break: break-all;
      background: #f0f0f0;
      border-radius: 8px;
      border: 1px solid #6666662b;
      padding: 12px;
      font-size: 16px;
      color: #333;
    }

    .RelateQuestion {
      width: fit-content;
      max-width: 100%;
      margin-top: 10px;
      word-break: break-all;
      background: #fff;
      border-radius: 12px;
      border: 1px solid #6666662b;
      padding: 8px;
      font-size: 16px;
      color: #333;
      cursor: pointer;
    }

    .RiskContent {
      background: rgba(255, 255, 255, 0.3);
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      border: 1px solid #fff;
      padding: 15px;
      font-size: 12px;
      color: #666;
      line-height: 18px;

      .RiskTitle {
        display: flex;
        align-items: center;
        height: 20px;
        font-size: 14px;
        color: #666666;
        line-height: 20px;
      }

      .riskIcon {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }

      >p {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }
  }

  .RiskDesc {
    display: flex;
    // position: absolute;
    // bottom: 12px;
    font-size: 14px;
    font-weight: 400;
    color: @guide-page-risk-tip-color;
    line-height: 20px;
    justify-content: center;
    margin-top: 10px;
    padding: 5px 0;
    width: 100%;
    background: linear-gradient(90deg, rgba(220, 233, 247, 0) 0%, rgba(220, 233, 247, 0.6) 50%, rgba(220, 233, 247, 0) 100%);
  }

  .RiskDescClick {
    display: flex;
    cursor: pointer;
    width: 100%;
    justify-content: center;
  }

  .RiskDescText {
    max-width: calc(100% - 24px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .RiskDescClickArrow {
    margin-left: 4px;
  }
}

.GuidePagePC {
  .GuideHeaderFirst {
    width: 98px;
    height: 98px;
    margin-top: 60px;
  }

  .GuideHeader {
    width: 98px;
    height: 98px;
    // margin-top: 190px;
  }
}

.GuidePageApp {
  .GuideHeaderFirst {
    width: 98px;
    height: 98px;
    margin-top: 36px;
  }

  .GuideHeader {
    width: 98px;
    height: 98px;
    // margin-top: 138px;
  }
}

.ChatFooter {
  position: relative;
  z-index: @zindex-footer;
  padding-bottom: @safe-bottom;
  padding-left: 15px;
  padding-right: 15px;
  background: transparent;
  // padding: 0 15px calc(@safe-bottom + 27px) 15px;

  &-Version {
    padding: 10px 0;
    font-size: 12px;
    color: #ccc;
    line-height: 17px;
    width: 100%;
    text-align: center;
  }
}