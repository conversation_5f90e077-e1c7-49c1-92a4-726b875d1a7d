import { NavbarProps } from '../Navbar';
import canUse from '../../utils/canUse';
import deepseekChat from '../Navbar/images/deepseekChat.png';
import AppBack from '../Navbar/images/appBack.png';
import AppHistory from '../Navbar/images/appHistory.png';
import AppNew from '../Navbar/images/appNewChat.png';
import PcHistory from '../Navbar/images/pcHistory.png';
import PcNew from '../Navbar/images/pcNewChat.png';
import PcClose from '../Navbar/images/pcClose.png';
import { ReferenceProps } from '../References/interface';
import { DisassemblyProblemsProps } from './interface';

// 用来区分手机端、pc端
const isMobile = canUse('touch');

/*
*** navbarValues参数说明 ***
**
  open?: boolean, // 是否展示navbar，默认true
  newConTooltip?: string; // 新建会话按钮提示语,pc必传值，app不传值

  // 返回按钮设置项
  showReturnButton?: boolean, // 是否展示返回按钮,默认pc不展示，app展示
  returnButtonIcon?: string; // 返回按钮图标路径
  onReturnButtonClick?: ()=>void; // 点击返回按钮响应处理函数，在组件调用处赋具体值

  // 标题区域设置
  showLogo?: boolean, // 是否展示logo,默认为true
  logo?: string, // logo图标的地址
  title?: string; // 头部标题文案，展示于logo右侧,默认为空字符串
  logoAndTitlePosition?: string; // 标题区域的位置：pc端默认靠左边，移动端默认居中

  // 历史会话按钮设置项
  showHistoryButton?: boolean, // 是否展示历史会话按钮，默认为true
  historyButtonIcon?: string, // 历史会话按钮图标路径
  historyButtonPosition?: string , // 'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
  onHistoryButtonClick?: () => void; // 点击历史会话按钮响应处理函数，在组件调用处赋具体值


  // 新建会话按钮设置项
  showNewButton?: boolean; // 是否显示新建会话按钮，默认为true
  newButtonIcon?: string, // 新建会话按钮图标路径
  onNewButtonClick?: () => void; // 点击新建会话按钮响应处理函数，在组件调用处赋具体值

  // 关闭按钮设置项
  showCloseButton?: boolean; // 是否显示关闭按钮，pc端默认true，移动端默认false
  closeButtonIcon?: string; // 关闭按钮图标路径
  onCloseButtonClick?: () => void; // 关闭按钮点击响应处理函数，在组件调用处赋具体值
**
*/

/*
* navbar默认配置
*/
const defaultNavBarValues = isMobile ? {
  open: true,
  newConTooltip: '',
  showReturnButton: true,
  returnButtonIcon: AppBack,
  showLogo: true,
  logo: deepseekChat,
  title: '',
  logoAndTitlePosition: 'center',
  showHistoryButton: true,
  historyButtonIcon: AppHistory,
  historyButtonPosition: 'right',
  showNewButton: true,
  newButtonIcon: AppNew,
  showCloseButton: false,
} : {
  open: true,
  newConTooltip: '开启新对话',
  showReturnButton: false,
  showLogo: true,
  logo: deepseekChat,
  title: '问TA',
  logoAndTitlePosition: 'left',
  showHistoryButton: true,
  historyButtonIcon: PcHistory,
  historyButtonPosition: 'right',
  showNewButton: true,
  newButtonIcon: PcNew,
  showCloseButton: true,
  closeButtonIcon: PcClose,
};

/*
* 引导页navbar默认配置
*/
const defaultWelcomeNavBarValues = isMobile ? {
  open: true,
  newConTooltip: '',
  showReturnButton: true,
  returnButtonIcon: AppBack,
  showLogo: false,
  logo: deepseekChat,
  title: '',
  logoAndTitlePosition: 'center',
  showHistoryButton: true,
  historyButtonIcon: AppHistory,
  historyButtonPosition: 'right',
  showNewButton: true,
  newButtonIcon: AppNew,
  showCloseButton: false,
  closeButtonIcon: '',
} : {
  open: true,
  newConTooltip: '开启新对话',
  showReturnButton: false,
  showLogo: false,
  logo: deepseekChat,
  title: '',
  logoAndTitlePosition: 'left',
  showHistoryButton: true,
  historyButtonIcon: PcHistory,
  historyButtonPosition: 'right',
  showNewButton: true,
  newButtonIcon: PcNew,
  showCloseButton: true,
  closeButtonIcon: PcClose,
};

// navBar组件props预处理
export const PreprocessNavBarConfig = (customLogo: string, commoNavBar?: NavbarProps, welcomeNavbar?: NavbarProps,) => {

  const navBarConfig = {
    navBar: { ...defaultNavBarValues, ...commoNavBar, logo: customLogo, },
    welcomNavBar: { ...defaultWelcomeNavBarValues },
  };
  // 如果外部传入welcomeNavbar,则欢迎页优先使用外部传入的welcomeNavbar，否则使用全局传入的commoNavBar
  if (welcomeNavbar && Object.keys(welcomeNavbar)?.length) {
    navBarConfig.welcomNavBar = {
      ...navBarConfig.welcomNavBar,
      ...welcomeNavbar,
      logo: customLogo,
    };
  } else if (commoNavBar && Object.keys(commoNavBar)?.length) {
    navBarConfig.welcomNavBar = {
      ...navBarConfig.welcomNavBar,
      ...commoNavBar,
      logo: customLogo,
    };
  }

  return navBarConfig;
}

export const aggregateData = (data: any) => {
  // 用于存储聚合结果的对象
  const aggregatedData: { [key: string]: any[] } = {};

  // 遍历数据数组
  data.forEach((item: any) => {
    const { messageId } = item;

    // 如果该类别还未出现过，则添加到聚合对象中
    if (!aggregatedData[messageId]) {
      aggregatedData[messageId] = [];
    }

    // 将当前项添加到对应类别的数组中
    aggregatedData[messageId].push(item);
  });

  return aggregatedData;
}

// 匹配[citation:数字]展示为：数字
export function replaceCitations(str: string, msg: any, references: ReferenceProps[]) {
  // 正则表达式匹配[citation:数字]模式
  const citationPattern = /\[citation:(\d+)\]/g;
  // 替换函数，用于生成HTML结构
  const replacementFunction = (_match: string, citationNumber: string) => {
    // 构造id属性值messageId+agent+problem_id+citationNumber
    const id = `${msg?.messageId}+${msg?.agent}+${(msg?.problem_id === null || msg?.problem_id === undefined) ? msg?.problemId : msg?.problem_id}+${citationNumber}`;
    // 找到相应的引用详情确定是外部引用还是内部引用
    const reference = references?.find((item: ReferenceProps) => Number(item?.citationIndex) === Number(citationNumber));
    // 做一个兜底，如果正文有引用但实际references里面没有直接不展示
    if (!reference) {
      return '';
    }
    // 返回HTML结构字符串
    return `<span data-id="${id}" class="ReferenceCitation ${reference?.source === 'inner' ? 'InnerCitation' : 'OuterCitation'}">${citationNumber}</span>`;
  };

  // 使用replace方法和正则表达式进行全局替换
  return str?.replace(citationPattern, replacementFunction);
}

// 替换函数，用于将匹配的span标签替换回[citation:(\d+)]格式,供复制的时候用
export function resetCitations(str: string) {
  // 正则表达式，用于匹配带有特定class的span标签，id是随机生成的
  const regex = /<span\s+data-id="[^"]+"\s+class="ReferenceCitation(?:\s+(?:InnerCitation|OuterCitation))?">(\d+)<\/span>/g;
  // const replaceFunction = (_match: string, citationNumber: string) => {
  //   return `[citation:${citationNumber}]`;
  // };
  const replaceFunction = () => {
    return '';
  };

  return str?.replace(regex, replaceFunction);
}

/**
 * 从嵌套数组中提取并根据 title 与 docName 去重 references
 * @param {Array<{ references: Array<ReferenceProps> }>} contents - 包含 references 数组的嵌套数组
 * @returns {Array<ReferenceProps>} - 去重后的 references 数组
 */
export function extractAndDeduplicateReferences(contents: DisassemblyProblemsProps[]): Array<ReferenceProps> {
  // 初始化一个 Map 用于存储去重后的 references
  // Map 的键是由 title 和 docName 组合而成的唯一标识符
  // Map 的值是去重后的 reference 对象
  const uniqueReferencesMap = new Map<string, ReferenceProps>();

  // 遍历 contents 数组
  contents?.forEach(item => {
    // 假设每个 item 都有一个名为 references 的数组属性
    if (Array.isArray(item?.references)) {
      // 使用 forEach 遍历每个 item 的 references 数组
      item?.references?.forEach(reference => {
        // 针对业问，如果没有被引用过的则不展示（periodType=source就表示被引用过）
        // if (item?.candidate_agent === 'yewen' && reference?.periodType !== 'source') {
        //   console.warn('reference', reference)
        //   return;
        // }
        // 构造唯一标识符：如果 title 和 docName 都存在，则使用它们的组合；否则只使用存在的那个字段作为标识符
        const uniqueIdentifier = reference?.title || reference?.docName || '';

        // 检查 Map 中是否已经存在该唯一标识符
        if (!uniqueReferencesMap.has(uniqueIdentifier)) {
          // 如果不存在，则将该 reference 对象添加到 Map 中
          uniqueReferencesMap.set(uniqueIdentifier, reference);
        }
      });
    }
  });

  // 将 Map 中的值转换为一个数组并返回（这就是去重后的 references 数组）
  return Array.from(uniqueReferencesMap.values());
}

export function replaceToolStartAndEnd(str: string) {
  // 正则表达式匹配[tool_start]模式
  const toolStartPattern = /\[tool_start(.*?)\]/g;
  // 正则表达式匹配[tool_end]模式
  const toolEndPattern = /\[tool_end(.*?)\]/g;
  const replaceToolStart = (_match: string, text: string) => {
    console.log('toolStart', text);
    // 返回HTML结构字符串
    return `<div class="Toolstart">${text}<span class="TypingEllipsis"><span class="TypingDot"></span><span class="TypingDot"></span><span class="TypingDot"></span></span></div>`;
  };

  const replaceToolEnd = (_match: string, text: string) => {
    console.log('toolEnd', text);
    // 返回HTML结构字符串
    return `<div class="Toolend">${text}</div>`;
  };

  if (toolEndPattern.test(str)) {
    const regex = /<div class="Toolstart">(.*?)<span class="TypingEllipsis">(\s*<span class="TypingDot"><\/span>\s*){3}<\/span><\/div>/g;
    if (regex.test(str)) {
      return  str.replace(regex, '').replace(toolEndPattern, replaceToolEnd);
    } else if (toolStartPattern.test(str)) {
      return  str.replace(toolStartPattern, '').replace(toolEndPattern, replaceToolEnd);
    } else {
      return str.replace(toolEndPattern, replaceToolEnd);
    }
  } else if (toolStartPattern.test(str)) {
    return str.replace(toolStartPattern, replaceToolStart);
  } else {
    return str;
  }
}
