import { ComposerProps, ComposerHandle, ComposerConfigProps } from '../Composer';
import { NavbarProps } from '../Navbar';
import { MessageContainerProps, MessageContainerHandle } from '../MessageContainer';
import { MessageWithoutId } from '../Message';
import { QuickReplyItemProps } from '../QuickReplies';
import { ILogParams } from '../../LogPointConfigMap';
import { GuidePageProps, WelcomeProps } from '../GuidePage/interface';
import { HistoryConversationProps, HistoryListInAiChatProps } from '../HistoryConversation/interface';
import { ReferenceProps } from '../References/interface';

export type AiChatProps = Omit<ComposerProps, 'onFocus' | 'onChange' | 'onBlur'> &
  Omit<MessageContainerProps, 'messages'> & {
    /**
     * 宽版模式断点
     */
    // wideBreakpoint?: string;
    /**
     * 当前语言
     */
    locale?: string;
    /**
     * 多语言
     */
    locales?: any; // FIXME
    /**
     * 导航栏配置
     */
    navbar?: NavbarProps;
    /**
     * 导航栏渲染函数
     */
    renderNavbar?: () => React.ReactNode;
    /**
     * 欢迎页配置
     */
    welcome?: WelcomeProps;
    /**
      * 欢迎页渲染函数
      */
    renderWelcome?: (props: GuidePageProps) => React.ReactNode & React.ForwardRefExoticComponent<
      GuidePageProps & React.RefAttributes<HTMLDivElement>>;
    /**
     * 加载更多文案
     */
    // loadMoreText?: string;
    /**
     * 在消息列表上面的渲染函数
     */
    // renderBeforeMessageList?: () => React.ReactNode;
    /**
     * 消息列表 ref
     */
    messagesRef?: React.RefObject<MessageContainerHandle>;
    /**
    * 消息列表 ref
    */
    initialMessages?: MessageWithoutId[];
    /**
     * 下拉加载回调
     */
    // onRefresh?: () => Promise<any>;
    /**
     * 滚动消息列表回调
     */
    // onScroll?: (event: React.UIEvent<HTMLDivElement, UIEvent>) => void;
    /**
     * 消息列表
     */
    // messages: MessageProps[];
    /**
     * 消息内容渲染函数
     */
    // renderMessageContent: (message: MessageProps) => React.ReactNode;
    /**
     * 快捷短语
     */
    quickReplies?: QuickReplyItemProps[];
    /**
     * 快捷短语的点击回调
     */
    onQuickReplyClick?: (item: QuickReplyItemProps, index: number) => void;
    /**
     * 快捷短语的滚动回调
     */
    onQuickReplyScroll?: () => void;
    /**
     * 快捷短语渲染函数
     */
    renderQuickReplies?: () => void;
    /**
     * 回到底部按钮的图标
     */
    backBottomButton?: { icon: string };
    /**
     * 输入区 ref
     */
    composerRef?: React.RefObject<ComposerHandle>;
    /**
     * 输入框初始内容
     */
    // text?: string;
    /**
     * 输入框占位符
     */
    // placeholder?: string;
    /**
     * 输入框聚焦回调
     */
    onInputFocus?: ComposerProps['onFocus'];
    /**
     * 输入框更新回调
     */
    onInputChange?: ComposerProps['onChange'];
    /**
     * 输入框失去焦点回调
     */
    onInputBlur?: ComposerProps['onBlur'];
    /**
     * 发送消息回调
     */
    // onSend: (type: string, content: string) => void;
    /**
     * 发送图片回调
     */
    // onImageSend?: (file: File) => Promise<any>;
    /**
     * 输入方式
     */
    // inputType?: InputType;
    /**
     * 输入方式切换回调
     */
    // onInputTypeChange?: () => void;
    /**
     * 语音输入
     */
    // recorder?: RecorderProps;
    /**
     * 工具栏
     */
    // toolbar?: ToolbarItemProps[];
    /**
     * 点击工具栏回调
     */
    // onToolbarClick?: () => void;
    /**
     * 点击附加内容回调
     */
    // onAccessoryToggle?: () => void;
    /**
     * 输入组件
     */
    Composer?: React.ElementType;
    renderComposer?:
    | React.ElementType<any>
    | React.ForwardRefExoticComponent<ComposerProps & React.RefAttributes<ComposerHandle>>;// FIXME
    composerConfig?: ComposerConfigProps;
    /**
     * 通用配置
     */
    config?: any; // config
    /**
     * 最底部渲染函数
     */
    renderFooterVersion?: () => void;
    /**
     * 点击点赞点踩
     */
    onFeedBack?: () => void;
    /**
     * log上报回调
     */
    onReportLog?: (params: ILogParams | undefined) => void;

    historyConversation: HistoryConversationProps & HistoryListInAiChatProps; // 历史消息
    renderHistoryConversation?: (props: HistoryConversationProps & HistoryListInAiChatProps) => React.JSX.Element;
    /**
    * 点踩反馈弹窗配置
    */
    feedbackModalConfig?: {
      title?: string,
      inputPlaceholder?: string,
      showLabels?: boolean,
    };
    /**
     * 是否呈现历史会话列表推出的状态，宽屏状态不需要
     */
    showPushHistory?: boolean;
    /**
     * 是否展示token和时间区域
     */
    showToken?: boolean;/**
    * 是否展示幻觉标识
    */
    showHallucination?: boolean;
    onSwitchWideNarrow?: (obj: any) => void;
    initConversationObj?: any;
  };

export interface AiChatHandle {
  chatContext: ChatContextProps;
}
export interface Tmessage {
  code: string;
  resultData?: any;
}

export interface RequestOptions {
  action: string;
  method: string;
  url: string;
  headers: object;
  payload?: any;
  timeout: number;
  useHttp: boolean;
  responseValidator?: (res: any) => boolean | Promise<boolean>;
  requestTransfer: (req: any) => any;
  responseTransfer: (res: any) => any;
}

export interface DisassemblyProblemsProps {
  sub_problem_title: string;
  problem_id: number;
  candidate_agent: string;
  problem_description?: string;
  isThinking?: boolean;
  thinkContent?: string;
  content?: string;
  isProblemEnd?: boolean;
  references?: ReferenceProps[];
  cards?: any[];
}
