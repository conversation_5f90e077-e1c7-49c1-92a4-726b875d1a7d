& when (@global-style =true) {
  html {
    height: 100vh;

    &[data-safari] {
      height: calc(100vh - calc(100vh - 100%));
    }
  }

  body,
  #root {
    height: 100%;
  }

  body {
    margin: 0;
  }
}

.PushDivContainer {
  display: flex;
  height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
  background: @chat-bg;

  *,
  :after,
  :before {
    box-sizing: border-box;
  }

  // background: #2c3e50; /* 与历史面板同色消除白边 */
}

.ChatOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 11;
  /* 低于历史面板 */
  display: none;
  /* 默认隐藏 */
}

.ChatOverlayShow {
  display: block;
  /* 展开时显示 */
}

.ChatWrap {
  position: relative;
  height: 100%;
  width: 100%;
  border-radius: inherit;
  transition: transform 0.3s linear;
  flex: 1;
  min-width: 100%;
  /* 防止挤压变形 */
}

/* 历史对话列表 */
.HistoryPanel {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  /* 用transform动画 */
  position: absolute;
  top: 0;
  // z-index: 12; /* 高于遮罩层 */
  overflow-y: auto;
}

.ModalWrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 20px;
  z-index: 999;
  background: #fff;
  text-align: center;
  overflow-y: auto;
  border-radius: inherit;
  box-sizing: border-box;
  overflow-x: hidden;

  .ModalCloseWrap {
    position: absolute;
    top: 0;
    right: 0;
    width: 32px;
    height: 32px;
    z-index: 999;
    text-align: center;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;


    &-close {
      width: 14px;
      height: 14px;
    }
  }
}

.ChatApp {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: @gray-6;
  color: @body-color;
  font-family: @font-family-base;
  line-height: @line-height-base;
  -webkit-tap-highlight-color: transparent;
  background: transparent;
  position: relative;
  border-radius: inherit;
}

.S--focusing {
  --safe-bottom: 0px;
}

@media (hover: none) {
  .S--focusing {
    .MessageList {
      margin-top: 75vh;
    }
  }
}

.ChatFooter {
  position: relative;
  z-index: @zindex-footer;
  padding-bottom: @safe-bottom;
  padding-left: 15px;
  padding-right: 15px;
  background: transparent;
  // padding: 0 15px calc(@safe-bottom + 27px) 15px;

  &-Version {
    padding: 10px 0;
    font-size: 12px;
    color: #ccc;
    line-height: 17px;
    width: 100%;
    text-align: center;
  }
}

.LowcodeLoading {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.SensitiveError {
  background: #fcebeb;
  border-radius: 20px;
  padding: 7px 10px;
  font-size: 12px;
  color: #e33c39;
  line-height: 16px;
  margin-bottom: 5px;
  width: fit-content;
  position: absolute;
  right: 15px;
  bottom: 100%;
}

.ReferenceCitation {
  display: inline-block;
  font-size: 12px;
  line-height: 16px;
  padding: 0 4px;
  min-width: 16px;
  height: 16px;
  border-radius: 8px;
  margin-left: 8px;

}

.ReferenceCitation:hover {
  cursor: pointer;
}

.InnerCitation {
  color: #50c8ed;
  background: #dcf4fc;
}

.OuterCitation {
  color: #6e96f8;
  background: #e2eafe;
}

.Toolend {
  width: 100%;
  background: linear-gradient(270deg, rgba(255, 255, 255, 0.6) 0%, #FFFFFF 100%);
  border-radius: 4px;
  padding: 4px 8px;
  margin-top: 8px;
  // margin-left: -1em;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 22px;
}


.Toolstart {
  display: flex;
  width: 100%;
  background: linear-gradient(270deg, rgba(255, 255, 255, 0.6) 0%, #FFFFFF 100%);
  border-radius: 4px;
  padding: 4px 8px;
  margin-top: 8px;
  // margin-left: -1em;
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  line-height: 22px;

  .TypingEllipsis {
    display: inline-flex;
    align-items: center;
    margin-left: 6px;
  }

  .TypingDot {
    width: 5px;
    height: 5px;
    background: rgba(51, 51, 51, 0.8);
    border-radius: 50%;
    margin: 0 2px;
    animation: dotFade 3s infinite ease-in-out;
  }

  .TypingDot:nth-child(1) {
    animation-delay: 0s;
  }

  .TypingDot:nth-child(2) {
    animation-delay: 0.5s;
  }

  .TypingDot:nth-child(3) {
    animation-delay: 1s;
  }

  @keyframes dotFade {

    0%,
    16.67% {
      opacity: 1;
    }

    33.33%,
    50% {
      opacity: 0;
    }

    66.67%,
    100% {
      opacity: 1;
    }
  }
}
