import { getRandomString } from '../../utils';
const messageId = getRandomString();
export const testHistiory = {
  "code": "0",
  "msg": "ok",
  "resultData": {
    "nextOffset": 0,
    "list": [
      {
        "messagePKId": 243349,
        "conversationId": "19516c53-6152-4dc9-a01f-3036cb64c366",
        "aortaConversationId": null,
        "messageId": messageId,
        "taskId": null,
        "list": [
          {
            "type": "text",
            "content": {
              "text": "今日天气"
            },
            "contentList": null,
            "references": null,
            "source": null
          }
        ],
        "createTime": 1750225959000,
        "role": "user",
        "model": null,
        "relatedQuestionList": null,
        "needFeedback": true,
        "feedbackResult": null,
        "endFlag": false,
        "shootIntention": false,
        "aiMsgExtInfoJson": null,
        "backUpTalking": null,
        "agent": null,
        "langfuseTraceId": null,
        "channel": "app",
        "problemId": null,
        "problemTitle": null,
        "problemDescription": null,
        "feedback": null
      },
      {
        "messagePKId": 243353,
        "conversationId": "19516c53-6152-4dc9-a01f-3036cb64c366",
        "aortaConversationId": null,
        "messageId": messageId,
        "taskId": null,
        "list": [
          {
            "type": "text",
            "content": {
              "text": ""
            },
            "contentList": null,
            "references": null,
            "source": null
          }
        ],
        "createTime": 1750225963000,
        "role": "assistant",
        "model": "MAS",
        "relatedQuestionList": null,
        "needFeedback": true,
        "feedbackResult": null,
        "endFlag": false,
        "shootIntention": false,
        "aiMsgExtInfoJson": null,
        "backUpTalking": null,
        "agent": "coordinator",
        "langfuseTraceId": "c2fb4acb-957b-4f6b-b1bd-de620442d6d8",
        "channel": "app",
        "problemId": null,
        "problemTitle": null,
        "problemDescription": null,
        "feedback": null
      },
      {
        "messagePKId": 243353,
        "conversationId": "19516c53-6152-4dc9-a01f-3036cb64c366",
        "aortaConversationId": null,
        "messageId": messageId,
        "taskId": null,
        "list": [
          {
            "type": "text",
            "content": {
              "text": "用户原问题: 今日天气<br>\n补充和微调: 用户询问今日天气，属于强时效性问题，根据规则应直接使用 \"hiagent_search\" 进行查询。<br>\n问题拆解与Agent调用:<br>\n  - **问题1**: 今日天气<br>\n    **问题描述**: 今日天气<br>\n    **调用Agent**: DS-R1(大模型联网分析)<br>"
            },
            "contentList": null,
            "references": null,
            "source": null
          }
        ],
        "createTime": 1750225963000,
        "role": "assistant",
        "model": "MAS",
        "relatedQuestionList": null,
        "needFeedback": true,
        "feedbackResult": null,
        "endFlag": false,
        "shootIntention": false,
        "aiMsgExtInfoJson": null,
        "backUpTalking": null,
        "agent": "problem_analyze",
        "langfuseTraceId": "c2fb4acb-957b-4f6b-b1bd-de620442d6d8",
        "channel": "app",
        "problemId": null,
        "problemTitle": null,
        "problemDescription": null,
        "feedback": null
      },
      {
        "messagePKId": 243353,
        "conversationId": "19516c53-6152-4dc9-a01f-3036cb64c366",
        "aortaConversationId": null,
        "messageId": messageId,
        "taskId": null,
        "list": [
          {
            "type": "json",
            "content": {
              "original_question": "今日天气",
              "thought": "用户询问今日天气，属于强时效性问题，根据规则应直接使用 \"hiagent_search\" 进行查询。",
              "disassembly_problems": [
                {
                  "problem_description": "今日天气",
                  "sub_problem_title": "今日天气",
                  "problem_id": 0,
                  "candidate_agent": "hiagent_search"
                }
              ]
            },
            "contentList": null,
            "references": null,
            "source": null
          }
        ],
        "createTime": 1750225963000,
        "role": "assistant",
        "model": "MAS",
        "relatedQuestionList": null,
        "needFeedback": true,
        "feedbackResult": null,
        "endFlag": false,
        "shootIntention": false,
        "aiMsgExtInfoJson": null,
        "backUpTalking": null,
        "agent": "problem_decompose",
        "langfuseTraceId": "c2fb4acb-957b-4f6b-b1bd-de620442d6d8",
        "channel": "app",
        "problemId": null,
        "problemTitle": null,
        "problemDescription": null,
        "feedback": null
      },
      {
        "messagePKId": 243353,
        "conversationId": "19516c53-6152-4dc9-a01f-3036cb64c366",
        "aortaConversationId": null,
        "messageId": messageId,
        "taskId": null,
        "list": [
          {
            "type": "thinking",
            "content": {
              "text": "好的，我现在需要处理用户的问题“今日天气”。根据用户的历史对话记录，用户之前询问过同样的问题，当时的回复是直接调用hiagent_search进行查询。现在我需要参考提供的搜索资料来生成准确的回答。\n\n首先，当前时间是2025年6月18日13:52星期三。根据任务要求，天气类问题需要严格遵守规则，特别是优先使用最新的资料，避免矛盾。现在有多个资料，其中资料1的发布时间是2025年6月18日13:30:08，是最新的。资料1中的信息显示今天（6月18日星期三）的天气为多云-雷阵雨，气温22-34℃，湿度46%，南风1级。空气质量为良。同时，逐小时预报显示傍晚会有雷阵雨。\n\n其他资料如资料2、3、4也提到今天的雷雨天气，局地可能有强对流天气，包括大风和冰雹，需要提醒用户注意防范。但根据规则，应以最新资料为准。需要注意的是，资料1中的逐小时预报显示下午有雷阵雨，而资料2、3提到傍晚到夜间有雷雨，可能结合这些信息给出更全面的提醒。\n\n需要确保回答符合格式要求，使用加粗标题，列出温度、天气情况、湿度、风向，并给出生活提示。另外，不要提及资料来源，直接回答即可。需要检查是否有矛盾的信息，例如温度范围，各资料是否一致。资料1中的温度22-34℃，与其他资料中的33℃或34℃基本一致，所以可以确定温度范围正确。\n\n综合所有信息，回答应包括今天的天气概况，温度范围，湿度，风向，以及雷雨和大风冰雹的提示。生活指数方面，运动不适宜，洗车也不宜。需要提醒用户注意雷雨天气带来的影响，如防风防雹和安全出行。\n\n最后，按照markdown格式排版，使用加粗标题，列表结构，确保清晰易读。引用资料1、2、3中的相关信息，用外部资料标注来源。同时，避免任何矛盾或不确定的表述，严格按照最新资料回答。\n\n[tool_start:资讯数据读取查询中]\n"
            },
            "contentList": null,
            "references": null,
            "source": null
          },
          {
            "type": "text",
            "content": {
              "text": "\n\n**今日天气报告**（2025年6月18日 星期三）  \n- **温度**：22℃ ~ 34℃，当前气温31℃[citation:1]。  \n- **天气情况**：白天多云，傍晚至夜间将转**雷阵雨**，局地可能伴随**短时大风和冰雹**，需注意防范强对流天气[citation:2][citation:3]。  \n- **湿度**：46%[citation:1]。  \n- **风向/风力**：南风1级，午后至晚间风力逐渐增强至3级[citation:1][citation:3]。  \n\n**生活提示**：  \n1. **防雷避雨**：18:00后可能出现雷雨，外出建议携带雨具，避免在树下或广告牌旁停留[citation:2][citation:3]。  \n2. **洗车建议**：今日不宜洗车，未来24小时有雨[citation:1]。  \n3. **运动建议**：高温（当前31℃）且午后有强紫外线，户外运动易中暑，推荐室内活动[citation:1]。  \n\n**逐小时关键变化**：  \n- 15:00 ~ 20:00：多云到雷阵雨，气温32℃ → 30℃，西南风3级[citation:1]。  \n- 21:00 ~ 次日凌晨：持续雷阵雨，气温降至25℃以下，需注意防雷安全[citation:1]。"
            },
            "contentList": null,
            "references": null,
            "source": null
          },
          {
            "type": "references",
            "content": null,
            "contentList": null,
            "references": [
              {
                "site_name": "墨迹天气",
                "summary": "",
                "citationIndex": 0,
                "publish_time": 1750224608,
                "source_type": "weather",
                "id": "691a1d759b8c0d7db4a39f6e0db497254da8b7e9",
                "title": "北京市今天天气|本周天气|下周天气|未来天气",
                "url": "https://html5.moji.com/tpd/days40_web/index.html#/home?cityid=2&platform=toutiao"
              },
              {
                "site_name": "搜索引擎-网易新闻",
                "summary": "",
                "citationIndex": 1,
                "publish_time": 1750204020,
                "source_type": "search_engine",
                "id": "6cec3f78cf271f53-2f42392c2b89574e",
                "title": "北京今日最高气温34℃，傍晚至夜间有雷雨，局地雨强较大",
                "url": "https://c.m.163.com/news/a/K2AP0C7P0514R9KQ.html?spss=backflow-index-hotlist"
              },
              {
                "site_name": "搜索引擎-网易",
                "summary": "",
                "citationIndex": 2,
                "publish_time": 1750201140,
                "source_type": "search_engine",
                "id": "866978d579eb6e43-70dda2f97bb42311",
                "title": "北京今日天气",
                "url": "https://www.163.com/news/article/K2AM8E1300019TLK.html"
              },
              {
                "site_name": "搜索引擎-天气网",
                "summary": "",
                "citationIndex": 3,
                "publish_time": 1750176000,
                "source_type": "search_engine",
                "id": "f80520b63590c1d8-f1d35c5bf9f64cbe",
                "title": "北京天气预报",
                "url": "https://beijing.tianqi.com/shaoguan/jingdian.html"
              },
              {
                "site_name": "搜索引擎",
                "summary": "",
                "citationIndex": 4,
                "publish_time": 1750115104,
                "source_type": "search_engine",
                "id": "0f2bc37163b7836a-bcb92f220f036403",
                "title": "北京今天晴热在线最高温33℃ 明天将迎雷雨过程局地短时雨强较大",
                "url": "http://m.zj.weather.com.cn/news/2025/06/4224097.shtml"
              }
            ],
            "source": null
          }
        ],
        "createTime": 1750225963000,
        "role": "assistant",
        "model": "MAS",
        "relatedQuestionList": null,
        "needFeedback": true,
        "feedbackResult": null,
        "endFlag": false,
        "shootIntention": false,
        "aiMsgExtInfoJson": null,
        "backUpTalking": null,
        "agent": "hiagent_search",
        "langfuseTraceId": "c2fb4acb-957b-4f6b-b1bd-de620442d6d8",
        "channel": "app",
        "problemId": "0",
        "problemTitle": "今日天气",
        "problemDescription": "今日天气",
        "feedback": null
      }
    ],
    "noMore": true
  },
  "messageType": "0",
  "traceId": "4bf8862cfa87dbaa6cbac893c26ad89f"
}