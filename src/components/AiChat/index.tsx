/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable compat/compat */
import React, { useEffect, useState, useRef, useCallback, useImperativeHandle } from 'react';
// import LCRender from '@lowcode/lc-render';
import { useMemo } from 'react';
import { GuidePage } from '../GuidePage';
import { LocaleProvider } from '../LocaleProvider';
import { Navbar } from '../Navbar/index';
import { ChatContext } from '../../hooks/useChatContext';
import { toast } from '../Toast';

import {
  MessageContainer,
  // MessageContainerProps,
  MessageContainerHandle,
} from '../MessageContainer';
import { QuickReplies, QuickReplyItemProps } from '../QuickReplies';
import { Composer as DComposer, ComposerHandle } from '../Composer';
import { MessageProps } from '../Message';
// import { Bubble } from '../Bubble';
// import { RichText } from '../RichText';
// import { Card } from '../Card';
// import { Feedback } from '../Feedback';
import isSafari from '../../utils/isSafari';
// import { getLowcodeUrl } from '../../utils/lowcode';
// import FetchClient from '../../utils/fetchClient';
import { postRequest } from '../../utils/requestClient';
// import { GuessAskMore } from '../GuessAskMore';
// import { AiImage } from '../Image';
// import { LoadingSpinner } from '../LoadingSpinner';
// import { FileCard } from '../FileCard';
// import { Video } from '../Video';
// import { MarkDown } from '../MarkDown';
import { RiskTipApp } from '../RiskTipApp';
import { RiskTipPc } from '../RiskTipPc';
// import { HallucinationMarker } from '../HallucinationMarker'
// import { ThinkContent } from '../ThinkContent'
// import { getRandomString } from '../../utils';
import { useMessages } from '../../../src';
import { isMobile } from '../../utils/canUse';
// import { getThinkContent } from '../../utils/aiChat';
import { getDateFormat } from '../../utils/aiChat';
import { HistoryConversation } from '../HistoryConversation';
import { LogPointConfigMap, ILogParams } from '../../LogPointConfigMap';
import { WaterMark } from '../WaterMark';
import {
  AiChatHandle,
  AiChatProps,
  // RequestOptions,
  DisassemblyProblemsProps,
} from './interface';
import { ThinkLinkProps } from '../ThinkContent/interface';
import { ConversationItemProps } from '../HistoryConversation/interface';
import { ReferenceProps } from '../References/interface';
import { FeedbackLabelProps } from '../FeedbackTipPc/interface';
import {
  PreprocessNavBarConfig,
  aggregateData,
  replaceCitations,
  extractAndDeduplicateReferences,
  replaceToolStartAndEnd,
} from './utils';
import { UploadFile } from '../Composer/wide/Upload';

import {
  useCreateConversation,
  useStopMessage,
  useGetConversationList,
  useGetMessageList,
  useSendMessage,
  useFeedback,
} from '../../hooks/aiAgentChat/index';
import { FeedbackGuide } from '../FeedbackGuide';
// import { testHistiory } from './config';

const feedbackGuideLocalStorageKey = 'AORTACHAT_FEEDBACKGUIDE_OPEN_TIME';

export const AiChat = React.forwardRef<AiChatHandle, AiChatProps>((props, ref) => {
  const {
    // wideBreakpoint,
    locale = 'zh-CN',
    locales,
    navbar,
    renderNavbar,
    welcome,
    renderWelcome,
    loadMoreText,
    renderBeforeMessageList,
    messagesRef,
    onRefresh,
    onScroll,
    initialMessages = [],
    renderMessageContent,
    renderCardContent,
    onBackBottomShow,
    onBackBottomClick,
    quickReplies,
    onQuickReplyClick,
    onQuickReplyScroll,
    renderQuickReplies,
    backBottomButton,
    text,
    placeholder,
    onInputFocus,
    onInputChange,
    onInputBlur,
    onSend,
    onImageSend,
    inputOptions,
    composerRef,
    inputType,
    onInputTypeChange,
    recorder,
    toolbar,
    onToolbarClick,
    onAccessoryToggle,
    // rightAction,
    Composer = DComposer,
    config,
    renderFooterVersion,
    onReportLog,
    renderComposer: CustomerComposer,
    composerConfig,
    historyConversation,
    showFeedbackModal,
    feedbackModalConfig,
    isWide,
    showPushHistory = true,
    showToken = true,
    showHallucination = true,
    onSwitchWideNarrow,
    initConversationObj,
  } = props;

  let msgsRef = React.useRef<MessageContainerHandle>(null);

  if (messagesRef) {
    msgsRef = messagesRef;
  }

  // 输入区
  let composerInputRef = React.useRef<ComposerHandle>(null);
  if (composerRef) {
    composerInputRef = composerRef;
  }

  const { messages, appendMsg, updateMsg, prependMsgs, setTyping, getTyping, getMessages, resetList } = useMessages(initialMessages);

  // const [isComponentFirstLoad, setIsComponentFirstLoad] = useState(!localStorage.getItem('hasChatUIComponentLoaded'));

  // 需要用到魔方卡片时，外部需要定义window.LCRender
  //   import LCRender from '@lowcode/lc-render';
  //   window.LCRender = LCRender;
  // const LCRender = window.LCRender || null;

  // config参数解析 start
  const appId = config?.appId;
  const isAorta = config?.appId === 'aorta';
  const userId = config?.userId;
  const sceneId = config?.sceneId;
  const source = config?.source;
  const isDev = config?.isDev || false;//是否测试环境
  window.isLowCodeDev = isDev; //用于设置魔方域名

  // const lowCodeConfig = config?.lowCode;//魔方组件的rootValue
  const requestConfig = config?.requests;
  // const mobileAction = requestConfig?.mobileAction;//公共接口移动端tcp请求的action，tcp需要配置

  // 公司数据配置项
  const companyDataConfig = props?.composerConfig?.companyDataConfig;
  // console.log('lowCodeConfig=' + lowCodeConfig);

  const defaultPushPercent = isMobile ? 75 : 100;
  const pushPosition = config?.pushPosition ? config?.pushPosition : 'right';
  const pushPercent = config?.pushPercent ? config?.pushPercent : defaultPushPercent;
  const showInternetSearch = props?.composerConfig?.showInternetSearch ?? false;

  // 默认请求配置
  // const defaultRequests = {
  //   /**
  //   * 基础URL
  //   */
  //   baseUrl: '',

  //   //初始引导接口
  //   init: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/initialGuide',
  //   },

  //   //问答接口
  //   send: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/chat',
  //   },

  //   //查询历史接口
  //   history: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/historyRecord',
  //     pageSize: 3,
  //   },

  //   //快捷问题接口
  //   quickReply: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/quickNavigation',
  //   },

  //   //点赞点踩接口
  //   score: {
  //     action: mobileAction,
  //     url: '/airobot/api/groovy/ai/adapter/feedBack',
  //   },
  // };
  // 发送问答接口
  const sendConfig = requestConfig?.send;
  // 点赞点踩接口
  const scoreConfig = requestConfig?.score;
  // 停止生成
  const stopConfig = requestConfig?.stop;
  // 关联问题接口
  // const relatedConfig = requestConfig?.related;
  // 快捷问题
  // const quickReplyConfig = requestConfig?.quickReply;
  // 不传默认用浏览器的行为打开链接
  // const openWebPage = config?.bridge?.openWebPage || window.open;

  // 历史消息接口
  const historyConfig = requestConfig?.history;
  // 初始接口
  const initConfig = requestConfig?.init;
  // 历史会话列表接口
  const historyConversationConfig = requestConfig?.historyConversation;
  // 阅读风险提示接口
  const riskReadConfig = requestConfig?.riskRead;
  // 查询是否阅读了风险提示接口
  const queryRiskReadConfig = requestConfig?.queryRiskRead;
  //  敏感词校验
  const sensitiveConfig = requestConfig?.sensitive;
  // 点踩反馈标签查询
  const feedbackLabelConfig = requestConfig?.feedbackTagList;

  // config参数解析 end

  // const fetchClient = new FetchClient(requestConfig?.baseUrl);

  // 第一次答案返回以后conversationId才有值
  const conversationId = useRef(initConversationObj?.initConversationId || '');

  // 是否切换了会话,初始值应为true
  const changedConversation = useRef(true);

  // 初始化引导页消息
  const [welcomeInfo, setWelcomeInfo] = useState<any>(null);
  // 没有更多历史消息了（初始化时以初始化的接口返回是否有历史消息字段为准）
  const [noMoreHistory, setNoMoreHistory] = useState(true);
  // 当前是历史消息的第几页
  const historyPageNum = useRef(1);
  // 历史消息偏移量
  const historyNextOffset = useRef('');
  // 用来记录哪些消息被停止生成了（主要是setState是异步的，导致虽然已经设置了stopped，但是sse接口的循环体内还没有拿到，下一条消息又开始发送了，导致旧消息即使停止生成了，还会再追加消息）
  const stoppedMsgs = useRef<MessageProps['_id'][]>([]);

  // 快捷问题
  // const [quickReplies, setQuickReplies] = useState<QuickReplyItemProps[]>( []);
  // 是否展示停止生成按钮
  const [showStopAnswer, setShowStopAnswer] = useState(false);

  // 历史会话面板是不是打开
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  // 风险提示弹窗打开还是关闭
  const [showRiskTip, setShowRiskTip] = useState(false);

  // 是否是新会话
  const [isNewConversation, setIsNewConversation] = useState(welcome?.open === false ? false : true); // showNewConversation

  // 是否发送会话endFlag结束了。由于sendHooksData最后几帧只返回{endFlag: true},因此单独加一个字段记录
  // 在点击 切换会话和发起新会话时，重置为false。
  const isSendEndFlag = useRef(false);
  // 是否是新发起会话点击切换了会话
  const isNewChangedConversation = useRef(false);
  // 反馈引导弹窗 计时内 打开
  const feedbackGuideTimer = useRef<any>(null);
  // 是否展示反馈引导弹窗
  const [showFeedbackGuide, setShowFeedbackGuide] = useState(false);
  // 因为sse接口里面需要判断，异步的state不准因此用Ref的
  // const isNewConversationRef = useRef(true); // showNewConversation
  const isNewConversationRef = useRef(!initConversationObj);

  const [historyConversationList, setHistoryConversationList] = useState<ConversationItemProps[]>([]);

  // 是否选中互联网搜索 默认打开
  const [enableInternetSearch, setEnableInternetSearch] = useState(true);

  // 是否已经读了风险提示
  const [riskRead, setRiskRead] = useState(false);

  // 是否键盘弹起
  const [keyboardShow, setKeyboardShow] = useState(false);

  // 是否展示敏感词检验报错信息
  const [showSensitiveError, setShowSensitiveError] = useState(false);
  const isCheckingSensitiveRef = useRef(false);

  // 点踩反馈标签列表
  const [feedbackLabels, setFeedbackLabels] = useState<FeedbackLabelProps[]>([]);
  const [selectedAgents, setSelectedAgents] = useState<string[]>(companyDataConfig?.selectedAgents || []);

  // 切换会话以后需要滚动到底步
  const needScrollToEnd = useRef(false);
  // 用来缓存问题总结
  // const reporter = useRef('');
  // 用来缓存问题总结是否结束
  const reporterEnded = useRef(false);
  // 用来缓存所有的子问题列表
  const allProblems = useRef<DisassemblyProblemsProps[]>([]);
  // 用来缓存当前该追加哪一条子问题的正文内容了
  const currentContentIndex = useRef(0);
  // 用来缓存当前该追加业问哪一个子问题的思考内容了
  const currentYewenThinkIndex = useRef(0);
  // 用来缓存当前该追加DS哪一个子问题的思考内容了
  const currentDeepseekThinkIndex = useRef(0);
  // 用来缓存当前该追加睿评哪一个子问题的思考内容了
  const currentRuipingThinkIndex = useRef(0);
  // 用来缓存业问的子问题列表
  const yewenProblems = useRef<DisassemblyProblemsProps[]>([]);
  // 用来缓存业问的子问题列表
  const deepseekProblems = useRef<DisassemblyProblemsProps[]>([]);
  // 用来缓存睿评的子问题列表
  const ruipingProblems = useRef<DisassemblyProblemsProps[]>([]);
  // 用来缓存思维链内容
  const thinkLinks = useRef<ThinkLinkProps[]>([]);
  // 问题分析的内容
  const problemAnalyze = useRef('');
  // coordinator的内容
  const coordinator = useRef('');
  // 是否已经append了新的msg
  const hasAppendMsg = useRef(false);
  // 用来记录追加到哪一步了
  const currentStep = useRef('problem_analyze');
  // const msgText = useRef('');
  const originHeight = useRef(0);

  // 用于 O(1) 查找 problem_id 对应的下标
  // const allProblemsMap = useRef<{ [problem_id: string]: number }>({});
  // const yewenProblemsMap = useRef<{ [problem_id: string]: number }>({});
  // const ruipingProblemsMap = useRef<{ [problem_id: string]: number }>({});
  // const deepseekProblemsMap = useRef<{ [problem_id: string]: number }>({});


  // 初始化接口调用
  const { data: initHooksData, run: initHooksRun } = useCreateConversation({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: requestConfig?.baseUrl || '',
      ...initConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      appKey: requestConfig?.appKey,
    },
  });

  const { data: historyConversationHooksData, run: historyConversationHooksRun } = useGetConversationList({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: requestConfig?.baseUrl || '',
      ...historyConversationConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      appKey: requestConfig?.appKey,
      scenario: 'Mas',
    },
  });

  const { data: historyHooksData, run: historyHooksRun } = useGetMessageList({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: requestConfig?.baseUrl || '',
      ...historyConfig
    },
    requestParams: {
      // offsetMessagePkId: historyNextOffset?.current,
      /* hiAgent平台不支持分页，pageNum为1，pageSize由外部传入 */
      pageNum: requestConfig?.platform === 'hiAgent' ? 1 : historyPageNum?.current,
      pageSize: historyConfig?.pageSize || 100,
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
    },
  });

  const feedbackMessageRef = useRef<{ type?: any, score?: any, message?: any, reason?: any }>({});
  const { data: feedbackHooksData, run: feedbackHooksRun } = useFeedback({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: requestConfig?.baseUrl || '',
      ...scoreConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
      messageId: '',
      score: 'good',
      type: 'submit'
    },
  })

  // 拿到最后一条消息
  const getLastMsg = useCallback(() => {
    // return messages?.[messages?.length - 1];
    return getMessages()?.[getMessages()?.length - 1];
  }, []);

  const { run: stopHooksRun } = useStopMessage({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: requestConfig?.baseUrl || '',
      ...stopConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      // sceneId: sceneId,
      conversationId: conversationId?.current,
      appKey: requestConfig?.appKey,
      taskId: getLastMsg()?._id as string,
      messageId: getLastMsg()?.messageId as string,
    },
  });

  const { data: sendHooksData, error: sendHooksError, run: sendHooksRun } = useSendMessage({
    requestConfig: {
      manual: true,
      platform: requestConfig?.platform || 'custom',
      baseUrl: requestConfig?.baseUrl || '',
      ...sendConfig
    },
    requestParams: {
      appId: appId,
      userId: userId,
      question: '',
      stream: sendConfig?.stream,
      // sceneId: sceneId,
      appKey: requestConfig?.appKey,
      messageInterval: sendConfig?.messageInterval,

    },
  });

  // // 拿到最后一条答案消息
  // function getLastAnswerMsg() {
  //   const msgs = messages;
  //   if (msgs?.length > 0) {
  //     return (messages || [])?.findLast((record: MessageProps) => record?.position === 'left');
  //   }
  //   return undefined;
  // }

  // // 拿到最后一条问题消息
  // function getLastQuestionMsg() {
  //   return messages?.findLast((record: MessageProps) => record?.position === 'right');
  // }

  //   const sseWorkerCode = `
  // self.onmessage = async function (e) {
  //   const { type, payload } = e.data;
  //   if (type === 'start') {
  //     const { url, headers, body } = payload;
  //     try {
  //       const response = await fetch(url, {
  //         method: 'POST',
  //         headers,
  //         body: JSON.stringify(body),
  //       });
  //       if (!response.body) {
  //         self.postMessage({ type: 'error', error: 'No response body' });
  //         return;
  //       }
  //       const reader = response.body.getReader();
  //       const decoder = new TextDecoder('utf-8');
  //       let buffer = '';
  //       while (true) {
  //         const { done, value } = await reader.read();
  //         if (done) break;
  //         buffer += decoder.decode(value, { stream: true });
  //         let boundary;
  //         while ((boundary = buffer.indexOf('\\n\\n')) !== -1) {
  //           const rawEvent = buffer.slice(0, boundary);
  //           buffer = buffer.slice(boundary + 2);
  //           self.postMessage({ type: 'data', data: rawEvent });
  //         }
  //       }
  //       self.postMessage({ type: 'end' });
  //     } catch (err) {
  //       self.postMessage({ type: 'error', error: err instanceof Error ? err.message : String(err) });
  //     }
  //   }
  // };
  // `;

  //   function createSSEWorker() {
  //     const blob = new Blob([sseWorkerCode], { type: 'application/javascript' });
  //     return new Worker(URL.createObjectURL(blob));
  // }

  // function handleInputFocus(e: React.FocusEvent<HTMLTextAreaElement>) {
  //   setTimeout(() => {
  //     if (msgsRef && msgsRef.current) {
  //       msgsRef.current.scrollToEnd({ animated: false, force: true });
  //     }
  //   }, 100);

  //   if (onInputFocus) {
  //     onInputFocus(e);
  //   }

  //   if (isMobile) {
  //     setKeyboardShow(true);
  //   }
  // }

  const handleInputFocus = useCallback((e: React.FocusEvent<HTMLTextAreaElement>) => {
    // 优化：主线程空闲时再滚动到底部，兼容性处理
    const doScrollToEnd = () => {
      if (msgsRef && msgsRef.current) {
        msgsRef.current.scrollToEnd({ animated: false, force: true });
      }
    };


    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      (window as any).requestIdleCallback(doScrollToEnd, { timeout: 300 });
    } else {
      setTimeout(doScrollToEnd, 100);
    }

    if (onInputFocus) {
      onInputFocus(e);
    }

    // // IOS在此判断，安卓通过监听resize
    // if (isMobile && isIos) {
    //   setKeyboardShow(true);
    // }
  }, []);

  const handleInputBlur = useCallback((e: React.FocusEvent<HTMLTextAreaElement>) => {
    // // IOS在此判断，安卓通过监听resize
    // if (isMobile && isIos) {
    //   setKeyboardShow(false);
    // }

    if (onInputBlur) {
      onInputBlur(e);
    }
  }, []);

  // 点赞点踩
  const handleFeedBack = useCallback((score: MessageProps['feedbackResult'], message: MessageProps, reason: any) => {
    feedbackMessageRef.current = {};
    let type = 'submit';
    if (score === message?.feedbackResult) {
      type = 'reset';
    }
    if (props.config.onFeedback) {
      props.config.onFeedback(score, message);
    }
    // 埋点上报
    if (score) {
      let params: ILogParams;
      if (type === 'submit') {
        params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGood') : LogPointConfigMap.get('handleFeedBackBad')) as ILogParams;
      } else {
        params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGoodCancel') : LogPointConfigMap.get('handleFeedBackBadCancel')) as ILogParams;
      }
      onReportLog?.(params);
    }

    feedbackMessageRef.current = { type: type, score: score, message: message, reason: reason };

    if (scoreConfig?.url) {
      feedbackHooksRun(
        {
          score,
          type,
          messageId: message?.messageId,
          appId: appId,
          userId: userId,
          sceneId: sceneId,
          conversationId: conversationId?.current,
          reason: reason,
          channel: isMobile ? 'app' : 'pc',
        }
      );
    }
  }, []);

  const feedbackUpdateMsg = useCallback(() => {
    let feedbackResult = feedbackMessageRef?.current?.score;
    if (feedbackMessageRef?.current?.type === 'reset') {
      feedbackResult = undefined;
    }
    const TIME_GAP = 5 * 60 * 1000;
    let lastTs = 0;
    const ts = feedbackMessageRef?.current?.message?.createdAt || Date.now();
    const hasTime = feedbackMessageRef?.current?.message.hasTime || ts - lastTs < 0 || ts - lastTs > TIME_GAP;
    const newMessage = { ...feedbackMessageRef?.current?.message, feedbackResult, hasTime };
    console.warn('12121212')
    updateMsg?.(feedbackMessageRef?.current?.message._id, newMessage);
  }, []);

  useEffect(() => {
    if (feedbackHooksData && feedbackHooksData?.code === '0') {
      if (feedbackMessageRef?.current?.reason) {
        toast.success('反馈成功', 1000, 'center');
      }
      feedbackUpdateMsg();

    }
  }, [feedbackHooksData])

  // const handleFeedBack = useCallback((score: MessageProps['feedbackResult'], message: MessageProps, reason: any) => {
  //   let type = 'submit';
  //   if (score === message?.feedbackResult) {
  //     type = 'reset';
  //   }
  //   if (props.config.onFeedback) {
  //     props.config.onFeedback(score, message);
  //   }
  //   // 埋点上报
  //   if (score) {
  //     let params: ILogParams;
  //     if (type === 'submit') {
  //       params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGood') : LogPointConfigMap.get('handleFeedBackBad')) as ILogParams;
  //     } else {
  //       params = (score === "good" ? LogPointConfigMap.get('handleFeedBackGoodCancel') : LogPointConfigMap.get('handleFeedBackBadCancel')) as ILogParams;
  //     }
  //     onReportLog?.(params);
  //   }

  //   // fetchClient.post<Tmessage>({
  //   if (scoreConfig?.url) {
  //     postRequest(requestConfig?.baseUrl, {
  //       payload: {
  //         score,
  //         type,
  //         messageId: message?.messageId,
  //         appId: appId,
  //         userId: userId,
  //         sceneId: sceneId,
  //         conversationId: conversationId?.current,
  //         reason: reason,
  //       },
  //       ...scoreConfig
  //     }).then((data) => {
  //       if (data && data?.code === '0') {
  //         if (reason) {
  //           toast.success('反馈成功', 1000, 'center');
  //         }
  //         let feedbackResult = score;
  //         if (type === 'reset') {
  //           feedbackResult = undefined;
  //         }
  //         const newMessage = { ...message, feedbackResult };
  //         updateMsg?.(message._id, newMessage);
  //       }
  //     });
  //   }
  // }, [])

  // 停止生成
  const handleStopAnswer = useCallback(() => {
    // 拿到最后一条答案消息
    const message = getLastMsg();
    if (message) {

      // 埋点上报
      const params = LogPointConfigMap.get('handleStopAnswer');
      onReportLog?.(params);

      const newMessage = { ...message, stopped: true };
      console.warn('1313131')
      updateMsg?.(message._id, newMessage);
      stoppedMsgs?.current?.push(message?._id);
      setShowStopAnswer(false);
      // fetchClient.post<Tmessage>({
      //   url: stopConfig?.url,
      //   body: {

      if (stopConfig?.url) {
        // postRequest(requestConfig?.baseUrl, {
        //   payload: {
        //     appId: appId,
        //     userID: userId,
        //     sceneId: sceneId,
        //     taskId: message?._id,
        //     messageId: message?.messageId,
        //   },
        //   ...stopConfig
        // });
        stopHooksRun({
          taskId: message?._id,
          messageId: message?.messageId,
          conversationId: conversationId?.current,
        });
      }
    }
  }, [])

  const setUnsend = useCallback(() => {
    // 找到最后一条问题消息
    const lastMessage = getLastMsg();
    // 如果最后一条消息是问题，那么打上感叹号
    if (lastMessage?.position === 'right') {
      console.warn('14141414')
      updateMsg?.(lastMessage?._id, { ...lastMessage, unsend: true });
    } else {
      // 表示最后一条消息是回答，标记答案生成异常，并且隐藏停止生成按钮
      console.warn('151515151')
      updateMsg?.(lastMessage?._id, { ...lastMessage, sendError: true });
      // 隐藏停止生成按钮
      setShowStopAnswer(false);
    }
  }, [])

  const dealwithSSEData = useCallback((originParsedData: any) => {
    // 如果发现返回code为502，这是那种消息返回到一半异常closed掉的场景
    if (originParsedData?.BINDATA?.code === '502') {
      setUnsend();
      return;
    }
    const answerInfos = originParsedData?.BINDATA?.resultData?.answerInfos;
    const textItem = answerInfos?.find((item: any) => item.type === 'stream');
    const parsedData = textItem?.text && JSON.parse(textItem?.text);
    // const parsedData = originParsedData;
    if (!parsedData) {
      return;
    }

    if (!conversationId?.current) {
      conversationId.current = parsedData?.conversationId || parsedData?.conversation_id;
    }
    const msgId = parsedData?.messageId;

    // 本条消息如果被停止生成了跳出最外层循环
    if (stoppedMsgs?.current?.includes(msgId)) {
      return;
    }

    // 如果当前会话Id与接口里面返回的会话Id不一致，表示换了会话或者新开了会话，不需要再处理
    if (conversationId?.current !== parsedData?.conversationId && conversationId?.current !== parsedData?.conversation_id) {
      return;
    }

    // 开新会若原来会话里面若还有SSE接口没处理结束的，则直接不再处理
    if (isNewConversationRef?.current === true) {
      return;
    }

    const parsedDataListLength = parsedData?.list?.length;
    for (let i = 0; i < parsedDataListLength; i++) {
      const msgData = parsedData?.list?.[i];
      // 第一件事，拿到帧以后不更新消息内容，只缓存数据！
      // 针对问题分析部分的处理（问题分析还未结束的情况）
      if (parsedData?.agent === 'problem_analyze') {
        problemAnalyze.current = problemAnalyze?.current + msgData?.content?.text;
        thinkLinks.current[0] = {
          title: '问题分析',
          thinkContent: problemAnalyze?.current,
          isThinking: true,
        };
      }

      if (parsedData?.agent === 'coordinator') {
        coordinator.current = coordinator?.current + msgData?.content?.text;
        currentStep.current = 'coordinator';
      }

      // 业务知识检索的思考内容处理
      if (msgData?.type === 'thinking' && parsedData?.agent === 'yewen' && yewenProblems?.current?.length > 0) {
        // 先将相应的problem_id思考内容追加
        const index = yewenProblems?.current.findIndex(item => String(item?.problem_id) === String(parsedData?.problem_id));
        const thinkContent = (yewenProblems?.current?.[index]?.thinkContent ?? '') + msgData?.content?.text
        yewenProblems.current[index] = { ... (yewenProblems?.current?.[index]) as DisassemblyProblemsProps, thinkContent };
      }

      // DS大模型查询思考内容处理
      if (msgData?.type === 'thinking' && (parsedData?.agent === 'hiagent_search' || parsedData?.agent === 'default_local_llm') && deepseekProblems?.current?.length > 0) {
        // 先将相应的problem_id思考内容追加
        const index = deepseekProblems?.current?.findIndex(item => (String(item?.problem_id) === String(parsedData?.problem_id) && parsedData?.agent === item?.candidate_agent));
        const thinkContent = (deepseekProblems?.current?.[index]?.thinkContent ?? '') + msgData?.content?.text;
        deepseekProblems.current[index] = { ... (deepseekProblems?.current?.[index]) as DisassemblyProblemsProps, thinkContent };
      }

      // 睿评大模型查询思考内容处理
      if (msgData?.type === 'thinking' && parsedData?.agent === 'ruiping' && ruipingProblems?.current?.length > 0) {
        // 先将相应的problem_id思考内容追加
        const index = ruipingProblems?.current?.findIndex(item => String(item?.problem_id) === String(parsedData?.problem_id));
        const thinkContent = replaceToolStartAndEnd((ruipingProblems?.current?.[index]?.thinkContent ?? '') + msgData?.content?.text);
        ruipingProblems.current[index] = { ... (ruipingProblems?.current?.[index]) as DisassemblyProblemsProps, thinkContent };
      }

      // // 总结的正文部分，都缓存起来，当思考全部结束再开始追加
      // if (parsedData?.agent === 'reporter') {
      //   reporter.current = (reporter?.current ?? '') + (msgData?.content?.text ?? '');
      // }

      // 正文处理(yewen和hiagent_search的text才算正文)
      if (msgData?.type === 'text'
        && allProblems?.current?.length > 0
        && msgData?.content?.text
        && (parsedData?.agent === 'yewen' || parsedData?.agent === 'hiagent_search' || parsedData?.agent === 'default_local_llm' || parsedData?.agent === 'ruiping')) {

        // 拿到业问的正文，设置业问思考结束
        if (parsedData?.agent === 'yewen') {
          const thinkIndex = yewenProblems?.current?.findIndex(item => String(item?.problem_id) === String(parsedData?.problem_id));
          if (yewenProblems?.current?.[thinkIndex]?.isThinking === true && thinkIndex > -1 && thinkIndex !== undefined) {
            console.warn('业问思考子问题yewenProblems', thinkIndex, '结束。。。', parsedData);
            yewenProblems.current[thinkIndex] = {
              ...yewenProblems?.current?.[thinkIndex],
              isThinking: false,
            }

            // 如果业问思考内容正在追加的是当前子问题
            if (currentYewenThinkIndex.current === thinkIndex) {
              currentYewenThinkIndex.current = currentYewenThinkIndex.current + 1;
              // 还有可能本来排序在后面的子问题早在此问题结束以前就已经有过end帧了,就也需要把指针往后移1
              for (let j = currentYewenThinkIndex.current; j <= yewenProblems?.current.length; j++) {
                if (yewenProblems?.current[j]?.isThinking === false) {
                  currentYewenThinkIndex.current = currentYewenThinkIndex.current + 1;
                } else {
                  break;
                }
              }
            }
          }
        }

        // 拿到睿评的正文，设置睿评思考结束
        if (parsedData?.agent === 'ruiping') {
          const thinkIndex = ruipingProblems?.current.findIndex(item => String(item?.problem_id) === String(parsedData?.problem_id));
          // 首先更新相应的子问题思考状态为思考结束
          if (ruipingProblems?.current?.[thinkIndex]?.isThinking === true && thinkIndex > -1) {
            console.warn('ruiping思考子问题ruipingProblems', thinkIndex, '结束。。。', parsedData);
            ruipingProblems.current[thinkIndex] = {
              ...ruipingProblems?.current?.[thinkIndex],
              isThinking: false,
            }
            // 如果ruiping思考内容正在追加的是当前子问题
            if (currentRuipingThinkIndex.current === thinkIndex) {
              currentRuipingThinkIndex.current = currentRuipingThinkIndex.current + 1;

              // 还有可能本来排序在后面的子问题早在此问题结束以前就已经有过end帧了,就也需要把指针往后移1
              for (let j = currentRuipingThinkIndex.current; j < ruipingProblems?.current.length; j++) {
                if (ruipingProblems?.current[j]?.isThinking === false) {
                  currentRuipingThinkIndex.current = currentRuipingThinkIndex.current + 1;
                } else {
                  break;
                }
              }
            }
          }
        }

        // 拿到DS的正文，设置DS思考结束
        if (parsedData?.agent === 'hiagent_search' || parsedData?.agent === 'default_local_llm') {
          const thinkIndex = deepseekProblems?.current.findIndex(item => (String(item?.problem_id) === String(parsedData?.problem_id) && parsedData?.agent === item?.candidate_agent));
          // 首先更新相应的子问题思考状态为思考结束
          if (deepseekProblems?.current?.[thinkIndex]?.isThinking === true && thinkIndex > -1) {
            console.warn('deepseek思考子问题deepseekProblems', thinkIndex, '结束。。。', parsedData);
            deepseekProblems.current[thinkIndex] = {
              ...deepseekProblems?.current?.[thinkIndex],
              isThinking: false,
            }
            // 如果deepseek思考内容正在追加的是当前子问题
            if (currentDeepseekThinkIndex.current === thinkIndex) {
              currentDeepseekThinkIndex.current = currentDeepseekThinkIndex.current + 1;

              // 还有可能本来排序在后面的子问题早在此问题结束以前就已经有过end帧了,就也需要把指针往后移1
              for (let j = currentDeepseekThinkIndex.current; j < deepseekProblems?.current.length; j++) {
                if (deepseekProblems?.current[j]?.isThinking === false) {
                  currentDeepseekThinkIndex.current = currentDeepseekThinkIndex.current + 1;
                } else {
                  break;
                }
              }
            }
          }
        }

        // 出现正文帧的时候更新相应子问题的正文内容
        const index = allProblems?.current.findIndex(item => (String(item?.problem_id) === String(parsedData?.problem_id) && parsedData?.agent === item?.candidate_agent));
        const msgText = replaceCitations(msgData?.content?.text ?? '', parsedData, allProblems?.current?.[index]?.references ?? [] as ReferenceProps[])
        allProblems.current[index] = {
          ... (allProblems?.current?.[index]) as DisassemblyProblemsProps,
          content: (allProblems?.current?.[index]?.content ?? '') + msgText,
        };
      }

      // 出现参考内容的时候缓存起来
      if (msgData?.type === 'references' && msgData?.references) {
        console.warn('参考内容出现啦。。。。', parsedData)
        // 出现参考内容的时候
        const index = allProblems?.current.findIndex(item => String(item?.problem_id) === String(parsedData?.problem_id));
        allProblems.current[index] = {
          ... (allProblems?.current?.[index]) as DisassemblyProblemsProps,
          references: msgData?.references,
        };
      }

      // 出现睿评卡片的时候缓存起来 msgData?.type === 'image-card' || msgData?.type === 'media-card'
      if (msgData?.type === 'mas-card') {
        console.warn('睿评卡片出现啦。。。。', parsedData, msgData);
        // 出现睿评卡片的时候
        const index = allProblems?.current.findIndex(item => item?.problem_id === parsedData?.problem_id);
        allProblems.current[index] = {
          ... (allProblems?.current?.[index]) as DisassemblyProblemsProps,
          // msgData?.content里面包含content和type两个字段（type === 'image-card' || type === 'media-card'）
          cards: [JSON.parse(msgData?.content ?? '{}')],
        };
      }
    }

    // 如果这个子问题结束，需追加的子问题编号+1
    if (parsedData?.problem_endflag) {
      const index = allProblems?.current.findIndex(item => (String(item?.problem_id) === String(parsedData?.problem_id) && parsedData?.agent === item?.candidate_agent));
      console.warn('子问题', index, '结束。。。', parsedData, allProblems?.current?.[index]?.sub_problem_title);
      // 追加参考内容
      allProblems.current[index] = {
        ... (allProblems?.current?.[index]) as DisassemblyProblemsProps,
        isProblemEnd: true,
      };

      // 如果是当前正在追加的那个子问题结束，把追加指针往后移1
      if (currentContentIndex.current === index) {
        currentContentIndex.current = currentContentIndex.current + 1;
        // 还有可能本来排序在后面的子问题早在此问题结束以前就已经有过end帧了,就也需要把指针往后移1
        for (let j = currentContentIndex.current; j < allProblems?.current?.length; j++) {
          if (allProblems?.current[j]?.isProblemEnd === true) {
            currentContentIndex.current = currentContentIndex.current + 1;
          } else {
            break;
          }
        }
      }
    }
    // 第二件事，看看当前步骤到哪里了，按照步骤去挑缓存的东西来追加
    // 到了coordinator步骤
    if (currentStep?.current === 'coordinator') {
      const msg = {
        _id: msgId,
        messageId: msgId,
        type: 'markdown',
        createdAt: parsedData?.createTime,
        content: {
          text: coordinator?.current ?? '',
        },
        thinkLinks: thinkLinks?.current,
        isThinking: true,
      }

      if (hasAppendMsg?.current) {
        updateMsg?.(msgId, msg);
      } else {
        appendMsg?.(msg);
        hasAppendMsg.current = true;
      }
    }
    // 到了问题分析步骤
    if (currentStep?.current === 'problem_analyze' && thinkLinks?.current?.length > 0) {
      // 因为默认是problem_analyze步骤，thinkLinks判空可以避免当thinkLinks?.current里面什么都没有的时候，appendMsg导致泡泡有短暂的空白
      const msg = {
        _id: msgId,
        messageId: msgId,
        type: 'markdown',
        createdAt: parsedData?.createTime,
        content: { text: '' },
        thinkLinks: thinkLinks?.current,
        isThinking: true,
      }
      if (hasAppendMsg?.current) {
        updateMsg?.(msgId, msg);
      } else {
        appendMsg?.(msg);
        hasAppendMsg.current = true;
      }
    }

    // 针对问题分析部分结束的处理（endFlag为true的时候，list没有东西，不能放在for循环里面处理）
    if (parsedData?.agent === 'problem_analyze' && parsedData?.endFlag) {
      thinkLinks.current[0] = {
        title: '问题分析',
        thinkContent: problemAnalyze?.current,
        isThinking: false,
      };

      const msg = {
        _id: msgId,
        thinkLinks: thinkLinks?.current,
        isThinking: true,
      };
      console.warn('问题分析部分结束了。。。');
      updateMsg?.(msgId, msg);
    }

    // // 同样list为空，所以标记总结是否结束的帧也不能放在for循环里
    // if (parsedData?.agent === 'reporter' && parsedData?.endFlag) {
    //   console.warn('总结结束了。。。')
    //   reporterEnded.current = true;
    // }

    // 问题拆解的数据帧结束以后缓存起来，后续用来判断相应的子问题thinking是否结束等
    if (parsedData?.agent === 'problem_decompose' && parsedData?.endFlag === true) {
      const originProblems = (parsedData?.full_content?.disassembly_problems ?? []) as DisassemblyProblemsProps[];
      const problems = originProblems.map((item) => ({
        ...item,
        isThinking: true,
      }));
      yewenProblems.current = problems?.filter(item => item?.candidate_agent === 'yewen') ?? [];
      ruipingProblems.current = problems?.filter(item => item?.candidate_agent === 'ruiping') ?? [];
      deepseekProblems.current = problems?.filter(item => (item?.candidate_agent === 'hiagent_search' || item?.candidate_agent === 'default_local_llm')) ?? [];
      allProblems.current = [...yewenProblems.current, ...ruipingProblems.current, ...deepseekProblems.current];
      console.warn('问题拆解结果。。。', problems, 'yewenProblems.current=', yewenProblems?.current, 'ruipingProblems.current=', ruipingProblems?.current, 'deepseekProblems.current=', deepseekProblems?.current);
      // 问题拆解以后，根据子问题情况确定步骤
      if (yewenProblems?.current?.length > 0) {
        currentStep.current = 'yewen_thinking';
      } else if (ruipingProblems?.current?.length > 0) {
        currentStep.current = 'ruiping_thinking';
      } else {
        currentStep.current = 'deepseek_thinking';
      }
      // 问题分析结束以后，需要让相应的agent思考全部转圈
      if (yewenProblems?.current?.length > 0) {
        thinkLinks.current[1] = {
          title: '业务知识检索',
          thinkContent: '',
          isThinking: true,
        };
      }

      if (ruipingProblems?.current?.length > 0) {
        thinkLinks.current[2] = {
          title: '市场智研分析',
          thinkContent: '',
          isThinking: true,
        };
      }

      if (deepseekProblems?.current?.length > 0) {
        thinkLinks.current[3] = {
          title: 'DS大模型分析',
          thinkContent: '',
          isThinking: true,
        };
      }
      console.warn('问题拆解换步骤：', currentStep?.current);

      // 初始化各个子问题的索引，使用map优化findindex性能
      // allProblemsMap.current = {};
      // yewenProblemsMap.current = {};
      // ruipingProblemsMap.current = {};
      // deepseekProblemsMap.current = {};
      // allProblems.current.forEach((item, idx) => {
      //   allProblemsMap.current[item.problem_id] = idx;
      // });
      // yewenProblems.current.forEach((item, idx) => {
      //   yewenProblemsMap.current[item.problem_id] = idx;
      // });
      // deepseekProblems.current.forEach((item, idx) => {
      //   deepseekProblemsMap.current[item.problem_id] = idx;
      // });
    }

    // 到了业问思考步骤
    if (currentStep?.current === 'yewen_thinking') {
      let thinkContentLines = [];
      for (let problemIndex = 0; problemIndex <= currentYewenThinkIndex?.current; problemIndex++) {
        const thinkContent = yewenProblems?.current?.[problemIndex]?.thinkContent ?? '';
        thinkContentLines.push(thinkContent);
      }
      const thinkContentCollection = thinkContentLines?.join('\n');
      thinkLinks.current[1] = {
        title: '业务知识检索',
        thinkContent: thinkContentCollection,
        isThinking: true,
      };
      // 如果所有的业问子问题都已经思考结束，则更新业问思维链总的状态为思考完成
      if (yewenProblems?.current?.findIndex(item => item?.isThinking === true) === -1) {
        console.warn('业问所有子问题都思考结束')
        thinkLinks.current[1] = {
          ...thinkLinks?.current?.[1],
          isThinking: false,
        }
        if (ruipingProblems?.current?.length > 0) {
          currentStep.current = 'ruiping_thinking';
        } else if (deepseekProblems?.current?.length > 0) {
          // 如果有deepseek思考，则下一步是追加deepseek思考内容
          currentStep.current = 'deepseek_thinking';
        } else {
          // 否则下一步是追加总结
          // currentStep.current = 'reporter';
          currentStep.current = 'content';
        }
        console.warn('业问思考结束换步骤：', currentStep?.current);
      }
      const msg = {
        _id: msgId,
        thinkLinks: thinkLinks?.current,
      }
      updateMsg?.(msgId, msg);
    }

    // 到了睿评思考步骤
    if (currentStep?.current === 'ruiping_thinking') {
      let thinkContentLines = [];
      for (let problemIndex = 0; problemIndex <= currentRuipingThinkIndex?.current; problemIndex++) {
        const thinkContent = ruipingProblems?.current?.[problemIndex]?.thinkContent ?? '';
        thinkContentLines.push(thinkContent);
      }
      const thinkContentCollection = thinkContentLines?.join('\n');
      thinkLinks.current[2] = {
        title: '市场智研分析',
        thinkContent: thinkContentCollection,
        isThinking: true,
      };
      // 如果所有的睿评子问题都已经思考结束，则更新睿评思维链总的状态为思考完成
      if (ruipingProblems?.current?.findIndex(item => item?.isThinking === true) === -1) {
        console.warn('睿评所有子问题都思考结束')
        thinkLinks.current[2] = {
          ...thinkLinks?.current?.[2],
          isThinking: false,
        }
        // 如果有deepseek思考，则下一步是追加deepseek思考内容
        if (deepseekProblems?.current?.length > 0) {
          currentStep.current = 'deepseek_thinking';
        } else {
          // 否则下一步是追加总结
          // currentStep.current = 'reporter';
          currentStep.current = 'content';
        }
        console.warn('业问思考结束换步骤：', currentStep?.current);
      }
      const msg = {
        _id: msgId,
        thinkLinks: thinkLinks?.current,
      }
      updateMsg?.(msgId, msg);
    }

    // 到了deepseek思考步骤
    if (currentStep?.current === 'deepseek_thinking') {
      let thinkContentLines = [];
      for (let problemIndex = 0; problemIndex <= currentDeepseekThinkIndex?.current; problemIndex++) {
        const thinkContent = deepseekProblems?.current?.[problemIndex]?.thinkContent ?? '';
        thinkContentLines.push(thinkContent);
      }
      const thinkContentCollection = thinkContentLines?.join('\n');
      thinkLinks.current[3] = {
        title: 'DS大模型分析',
        thinkContent: thinkContentLines?.length > 0 ? thinkContentCollection : '',
        isThinking: true,
      };
      // 如果所有的DS子问题都已经思考结束，则更新DS思维链总的状态为思考完成
      if (deepseekProblems?.current?.findIndex(item => item?.isThinking === true) === -1) {
        console.warn('deepseek所有子问题都思考结束', parsedData, deepseekProblems)
        thinkLinks.current[3] = {
          ...thinkLinks?.current?.[3],
          isThinking: false,
        }
        // // 下一步是追加总结
        // currentStep.current = 'reporter';

        // 没有reporter步骤了，下一步是追加正文
        currentStep.current = 'content';
        console.warn('DS思考结束换步骤：', currentStep?.current);
      }
      const msg = {
        _id: msgId,
        thinkLinks: thinkLinks?.current,
      }
      updateMsg?.(msgId, msg);
    }

    // // 到了追加总结步骤
    // if (currentStep?.current === 'reporter') {
    //   const msg = {
    //     _id: msgId,
    //     isThinking: false,
    //     content: {
    //       text: reporter?.current ? `## **${reporter?.current}**` : '',
    //     },
    //   }
    //   updateMsg?.(msgId, msg);
    //   // 拿到总结的结束帧以后才可以下一步
    //   if (reporterEnded.current) {
    //     currentStep.current = 'content';
    //     console.warn('总结以后换步骤：', currentStep?.current);
    //   }
    // }

    if (currentStep?.current === 'content') {
      const finalContent = [];
      for (let i = 0; i <= currentContentIndex?.current; i++) {
        finalContent.push(allProblems?.current?.[i])
      }
      const msg = {
        _id: msgId,
        isThinking: false,
        contents: finalContent,
      }
      updateMsg?.(msgId, msg);
    }

    // 最后结束的帧
    if (parsedData?.endFlag && parsedData?.agent === 'MAS') {
      console.warn('最后一帧endFlag来了。。。', parsedData)
      isSendEndFlag.current = true;
      setShowStopAnswer(false);
      const msg = {
        _id: parsedData?.message_id,
        needFeedback: parsedData?.needFeedback,
        showHallucination: showHallucination,
        uniqReferences: extractAndDeduplicateReferences(allProblems?.current),
      };
      updateMsg?.(parsedData?.message_id, msg);
      setShowStopAnswer(false);
      console.warn('ye', yewenProblems?.current);
      console.warn('ds', deepseekProblems?.current);
      console.warn('all', allProblems?.current);
    }
  }, []);

  const dealwithCommonData = useCallback((resultData: any) => {
    if (!conversationId?.current) {
      conversationId.current = resultData?.conversationId;
    }

    const list = resultData?.list;
    for (let i = 0; i < list?.length; i++) {
      const item = list?.[i] ?? {};
      const msg = {
        createdAt: item?.createTime,
        type: item?.type,
        content: item?.content,
        needFeedback: i === (list?.length - 1) ? resultData?.needFeedback ?? true : false,
        feedbackResult: resultData?.feedbackResult,
        _id: item?.taskId,
        messageId: resultData?.messageId,
      }
      appendMsg?.(msg);
    }

    if (resultData?.relatedQuestionList?.length > 0) {
      appendMsg?.({
        type: 'relatedQuestionList',
        content: {
          relatedQuestionList: resultData?.relatedQuestionList,
        },
      });
    }
  }, []);

  const handleSend = useCallback(async (type: string, val: string, payload?: object, transformedFiles?: UploadFile[]) => {
    // 输入框没有东西，点击发送不做任何处理
    if (!val) {
      toast.show('请输入您的问题', undefined, 1000, 'center');
      return false;
    }

    if (isCheckingSensitiveRef.current || getTyping() || showStopAnswer) {
      toast.show('稍等片刻，等回答生成后再发送哦～', undefined, 1000, 'typing', document.getElementById("MessageContainer") as HTMLElement);
      return false;
    }

    // 如果需要校验敏感词
    if (sensitiveConfig?.url) {
      isCheckingSensitiveRef.current = true;
      const sensitiveRes = await postRequest(requestConfig?.baseUrl, {
        payload: {
          question: val,
          appId: appId,
          userId: userId,
          sceneId: sceneId,
        },
        ...sensitiveConfig
      });

      // 校验返回成功后把正在校验状态设置为false
      if (sensitiveRes?.code) {
        isCheckingSensitiveRef.current = false;
      }

      // 如果敏感词校验不通过则提示
      if (!sensitiveRes?.resultData?.ifPass) {
        setShowSensitiveError(true);
        // toast.show('您的输入可能包含投资者敏感信息，请核对~', undefined, 1000, 'sensitive', document.getElementById("AiChatComposer") as HTMLElement);
        return false;
      }
    }

    const isFirstChat = isNewConversationRef.current;
    isCheckingSensitiveRef.current = false;
    setIsNewConversation(false);
    isNewConversationRef.current = false;

    if (type === 'text' && val.trim()) {
      // 重置所有sse接口里面缓存的那些变量避免污染
      if (sendConfig?.stream) {
        // 用来缓存问题总结
        // reporter.current = '';
        // 用来缓存问题总结是否结束
        reporterEnded.current = false;
        // 用来缓存所有的子问题列表
        allProblems.current = [];
        // 用来缓存当前该追加哪一条子问题的正文内容了
        currentContentIndex.current = 0;
        // 用来缓存当前该追加业问哪一个子问题的思考内容了
        currentYewenThinkIndex.current = 0;
        // 用来缓存当前该追加DS哪一个子问题的思考内容了
        currentDeepseekThinkIndex.current = 0;
        // 用来缓存当前该追加睿评哪一个子问题的思考内容了
        currentRuipingThinkIndex.current = 0;
        // 用来缓存业问的子问题列表
        yewenProblems.current = [];
        // 用来缓存业问的子问题列表
        deepseekProblems.current = [];
        // 用来缓存睿评的子问题列表
        ruipingProblems.current = [];
        // 用来缓存思维链内容
        thinkLinks.current = [];
        // 问题分析的内容
        problemAnalyze.current = '';
        // coordinator的内容
        coordinator.current = '';
        // 是否已经append了新的msg
        hasAppendMsg.current = false;
        // 用来记录追加到哪一步了
        currentStep.current = 'problem_analyze';
      }

      appendMsg?.({
        type: 'text',
        content: { text: val },
        position: 'right',
      });

      let questionExtends = {} as any;

      if (transformedFiles && transformedFiles?.length > 0) {
        const formatFiles = transformedFiles?.map((file) => ({
          size: file?.size,
          name: file?.name,
          path: file?.response?.Result?.Path,
          // 如果后端没有返回完整的文件url，需要根据path拼接前缀，则使用padUrlPrefix
          url: file?.response?.Result?.Url
            || (composerConfig?.uploadConfig?.padUrlPrefix && composerConfig?.uploadConfig?.padUrlPrefix(file?.response?.Result?.Path))
            || '',
        }));
        questionExtends.files = formatFiles;
        for (let index = 0; index < formatFiles?.length; index++) {
          appendMsg?.({
            type: 'file',
            content: { ...formatFiles[index] },
            position: 'right',
          });
        }
      }

      setTyping(true);
      // 发送了问题以后，滚动到最下面（避免发送时滚动还没停，延时一点再scrollToEnd滚一次）
      setTimeout(() => {
        if (msgsRef && msgsRef.current) {
          msgsRef.current.scrollToEnd({ animated: false, force: true });
        }
      }, 100);

      if (sendConfig) {
        sendConfig.payload = {
          question: val,
          appId: appId,
          userId: userId,
          sceneId: sceneId,
          source: source,
          conversationId: conversationId?.current,
          enableInternetSearch,
          isFirstChat,
          questionExtends,
          channel: isMobile ? 'app' : 'pc',
          // 没有在睿评灰度白名单里面的不能给后端传ruiping这个agent
          selectedAgents: companyDataConfig?.hasRuipingGrey ? selectedAgents : selectedAgents?.filter((item) => item !== 'ruiping'),
          ...payload,
        };
      }

      setShowStopAnswer(true);
      sendHooksRun(sendConfig?.payload);

      return true;

      // if (sendConfig.requestTransfer) {
      //   sendConfig.payload = sendConfig.requestTransfer(sendConfig.payload);
      // }
      // //1、sse接口请求，使用 fetchSSE 函数请求流式接口
      // if (sendConfig?.stream) {
      //   fetchSSE(sendConfig);
      // } else {
      //   //2、普通接口请求
      //   postRequest(requestConfig?.baseUrl, sendConfig

      //     //   //2、Else 非sse接口请求
      //     //   fetchClient.post<Tmessage>({
      //     //     url: sendConfig?.url,
      //     //     body: { question: val, appId: appId, userId: userId, sceneId: sceneId },
      //     //     headers: sendConfig?.headers
      //   ).then((data: any) => {
      //     // console.log('Data received:', data); // 处理或显示数据
      //     if (data && data?.code === '0') {
      //       const resultData = data?.resultData;
      //       if (!conversationId?.current) {
      //         conversationId.current = resultData?.conversationId;
      //       }

      //       const list = resultData?.list;
      //       for (let i = 0; i < list?.length; i++) {
      //         const item = list?.[i] ?? {};
      //         const msg = {
      //           createdAt: item?.createTime,
      //           type: item?.type,
      //           content: item?.content,
      //           needFeedback: i === (list?.length - 1) ? resultData?.needFeedback : false,
      //           feedbackResult: resultData?.feedbackResult,
      //           _id: item?.taskId,
      //           messageId: resultData?.messageId,
      //         }
      //         appendMsg?.(msg);
      //       }

      //       if (resultData?.relatedQuestionList?.length > 0) {
      //         appendMsg?.({
      //           type: 'relatedQuestionList',
      //           content: {
      //             relatedQuestionList: resultData?.relatedQuestionList,
      //           },
      //         });
      //       }
      //     } else {
      //       setTyping(false);
      //       setUnsend();
      //     }
      //   })
      //     .catch(error => {
      //       setTyping(false);
      //       setUnsend();
      //       console.error('Error fetching data:', error); // 错误处理
      //     });
      // }
    }
    return true;
  }, [showStopAnswer, enableInternetSearch, selectedAgents, companyDataConfig]);

  // 公司数据切换值
  const handleCompanyDataSelectAgentsChange = useCallback((options: string[]) => {
    setSelectedAgents(options);
  }, []);

  // 输入内容改变则不显示敏感信息
  const handleInputChange = useCallback(() => {
    setShowSensitiveError(false);
  }, []);

  const handleQuickReplyClick = useCallback((item: QuickReplyItemProps) => {
    // if (isComponentFirstLoad) {
    //   handleFirstLoad();
    // }
    handleSend('text', item?.content || item?.code || item?.name || '', { allowChange: false });
  }, []);

  const dealwithHistoryMessageList = useCallback((data: any) => {
    if (data && data?.code === '0') {
      const resultData = data?.resultData ?? {};
      const list = aggregateData(resultData?.list ?? []);
      const msgs: MessageProps[] = [];

      // 对每个按照messageId分组后的数据进行处理
      Object.keys(list).forEach((messageId) => {
        const msgList = list?.[messageId] ?? [];
        const questionMsg = msgList?.find((item: any) => item?.role === 'user') ?? {};
        // 追加问题
        msgs.push({
          _id: `${messageId}-question`,
          type: 'text',
          messageId,
          position: 'right',
          createdAt: questionMsg?.createTime,
          needFeedback: false,
          showToken: false,
          showHallucination: false,
          content: {
            text: questionMsg?.list?.[0]?.content?.text || '',
          }
        });

        // 开始处理答案的逻辑
        const firstAnswerMsg = msgList?.find((item: any) => item?.role !== 'user') ?? {};
        const commonParms = {
          _id: messageId,
          messageId: messageId,
          position: 'left' as MessageProps['position'],
          createdAt: firstAnswerMsg?.createTime,
          needFeedback: firstAnswerMsg?.needFeedback ?? false,
          feedbackResult: firstAnswerMsg?.feedbackResult ?? null,
          showToken,
          showHallucination: showHallucination,
          type: 'markdown',
          feedback: firstAnswerMsg?.feedback ?? null,
          traceId: firstAnswerMsg?.langfuseTraceId ?? null,
          endFlag: true,
        }
        const coordinatorText = msgList?.find((item: any) => item?.agent === 'coordinator')?.list?.[0]?.content?.text ?? '';
        // 这种情况直接追加coordinator的语句以后不许要再往下走了
        if (coordinatorText) {
          msgs.push({
            ...commonParms,
            content: {
              text: coordinatorText,
            },
          });
          return;
        }
        const problemAnalyzeText = msgList?.find((item: any) => item?.agent === 'problem_analyze')?.list?.[0]?.content?.text ?? '';
        const reporterText = msgList?.find((item: any) => item?.agent === 'reporter')?.list?.[0]?.content?.text ?? '';
        const problemDecomposeJson = msgList?.find((item: any) => item?.agent === 'problem_decompose') ?? {};
        const originProblemDecompose = problemDecomposeJson?.list?.[0]?.content?.disassembly_problems ?? [];
        const yewenProblemsDecompose = originProblemDecompose?.filter((item: any) => item?.candidate_agent === 'yewen');
        const deepseekProblemsDecompose = originProblemDecompose?.filter((item: any) => (item?.candidate_agent === 'hiagent_search' || item?.candidate_agent === 'default_local_llm'));
        const ruipingProblemsDecompose = originProblemDecompose?.filter((item: any) => item?.candidate_agent === 'ruiping');
        const problemDecompose = [...yewenProblemsDecompose, ...ruipingProblemsDecompose, ...deepseekProblemsDecompose];
        let yewenThink = '';
        let deepseekThink = '';
        let ruipingThink = '';

        // 针对问题拆解的帧分组出来相应的思考和正文
        for (let i = 0; i < problemDecompose?.length; i++) {
          const relatedMsg = msgList?.find((item: any) => item?.agent === problemDecompose?.[i]?.candidate_agent
            && String(item?.problemId) === String(problemDecompose?.[i]?.problem_id));
          // 拼接参考资料，因为不确定排序完以后参考资料会不会在for循环的时候先设置到problemDecompose[i]里面，所以直接在外层设置好，正文引用颜色判断需要使用
          const msgReferences = relatedMsg?.list?.find((item: any) => item?.type === 'references')?.references ?? [];
          problemDecompose[i].references = msgReferences;
          // 历史接口默认设为true表示子问题已回答完毕
          problemDecompose[i].isProblemEnd = true;

          for (let j = 0; j < relatedMsg?.list?.length; j++) {
            const msg = relatedMsg?.list?.[j];
            // 按照agent拼接思考内容
            if (msg?.type === 'thinking') {
              if (relatedMsg?.agent === 'yewen') {
                yewenThink = yewenThink + (msg?.content?.text ?? '') + '\n';
              }
              if (relatedMsg?.agent === 'hiagent_search' || relatedMsg?.agent === 'default_local_llm') {
                deepseekThink = deepseekThink + (msg?.content?.text ?? '') + '\n';
              }
              if (relatedMsg?.agent === 'ruiping') {
                ruipingThink = ruipingThink + (msg?.content?.text ?? '') + '\n';
              }
            }
            // 拼接参考资料
            // if (msg?.type === 'references') {
            //    problemDecompose[i].references = msg?.references ?? [];
            // }
            // 拼接正文
            if (msg?.type === 'text') {
              const msgText = replaceCitations(msg?.content?.text ?? '', relatedMsg, problemDecompose?.[i]?.references);
              problemDecompose[i].content = msgText;
            }
            // 出现睿评卡片的时候
            if (msg?.type === 'mas-card') {
              problemDecompose[i].cards = [JSON.parse(msg?.content ?? '{}')]; // msg?.content里面包含content和type两个字段（type === 'image-card' || type === 'media-card'）
            }
          }
        }

        let thinkLinksContent = [];
        thinkLinksContent[0] = {
          title: '问题分析',
          thinkContent: problemAnalyzeText,
          isThinking: false,
        }

        if (yewenProblemsDecompose?.length > 0) {
          thinkLinksContent[1] = {
            title: '业务知识检索',
            thinkContent: yewenThink,
            isThinking: false,
          }
        }

        if (ruipingProblemsDecompose?.length > 0) {
          thinkLinksContent[2] = {
            title: '市场智研分析',
            thinkContent: replaceToolStartAndEnd(ruipingThink),
            isThinking: false,
          }
        }

        if (deepseekProblemsDecompose?.length > 0) {
          thinkLinksContent[3] = {
            title: 'DS大模型分析',
            thinkContent: deepseekThink,
            isThinking: false,
          }
        }

        const finalContent = [];
        for (let i = 0; i <= problemDecompose?.length; i++) {
          finalContent.push(problemDecompose?.[i])
        }

        const msg = {
          ...commonParms,
          content: {
            text: reporterText ? `## **${reporterText}**` : '',
          },
          thinkLinks: thinkLinksContent,
          isThinking: false,
          contents: finalContent,
          uniqReferences: extractAndDeduplicateReferences(problemDecompose),
        };
        msgs.push(msg);
      });
      prependMsgs?.(msgs);
      setNoMoreHistory(resultData?.noMore);
      historyNextOffset.current = resultData?.nextOffset;
      historyPageNum.current = historyPageNum?.current + 1;
      if (needScrollToEnd.current) {
        setTimeout(() => {
          if (msgsRef && msgsRef.current) {
            msgsRef.current.scrollToEnd({ animated: false, force: true });
          }
        }, 100);
        needScrollToEnd.current = false; // 重置回false
      }
      return msgs;
    }
    return [];
  }, [])

  const handleRefresh = useCallback(() => {
    // 接口还没返回的时候，禁用下拉刷新
    setNoMoreHistory(true);

    // return fetchClient.post<Tmessage>({
    //   url: historyConfig?.url,
    //   body: {
    //     offsetMessagePkId: historyNextOffset.current,
    //     pageNum: historyPageNum.current,
    //     pageSize: historyConfig?.pageSize || 6,
    //     appId: appId,
    //     userId: userId,
    //     sceneId: sceneId,
    //   },
    //   headers: historyConfig?.headers
    return postRequest(requestConfig?.baseUrl, {
      payload: requestConfig?.platform === 'hiAgent' ? {
        UserID: userId,
        AppConversationID: conversationId?.current,
        Limit: 1 * (historyConfig?.pageSize || 100),
        AppKey: requestConfig?.appKey,
      } : {
        offsetMessagePkId: historyNextOffset?.current,
        pageNum: historyPageNum?.current,
        pageSize: historyConfig?.pageSize || 100,
        appId: appId,
        userId: userId,
        sceneId: sceneId,
        conversationId: conversationId?.current,
      },
      ...historyConfig
    }).then((data) => {
      const msgs = dealwithHistoryMessageList(data);
      return msgs;
    });
  }, []);

  const toggleHistory = useCallback(() => {
    // 打开面板的时候,刷新历史会话
    if (!isHistoryOpen && showPushHistory) {
      if (historyConversationConfig?.url) {
        // postRequest(requestConfig?.baseUrl, {
        //   payload: { appId: appId, userId: userId, sceneId: sceneId },
        //   ...historyConversationConfig
        // }).then((data) => {
        //   if (data && data?.code === '0') {
        //     const list = data?.resultData?.list ?? [];
        //     setHistoryConversationList(list);
        //   }
        // });
        if (showPushHistory) {
          // 需要打开历史会话面板的情况在打开的时候查
          historyConversationHooksRun();
        }
      }
    }

    // 埋点上报
    const params = LogPointConfigMap.get(isHistoryOpen ? 'toggleHistoryClose' : 'toggleHistoryOpen');
    onReportLog?.(params);

    if (showPushHistory) {
      setIsHistoryOpen(!isHistoryOpen);
    }
  }, [isHistoryOpen, showPushHistory]);

  const handleNewConversation = useCallback(() => {
    onSwitchWideNarrow?.(null);
    isSendEndFlag.current = false;
    isNewChangedConversation.current = false;
    // 开新会话，如果历史面板打开则关闭
    if (isHistoryOpen) {
      toggleHistory();
    }

    // 先判断一下当前会话有没有消息，如果没有则表示已经是最新会话了,什么也不做
    if (isNewConversationRef?.current) {
      if (isMobile) {
        toast.show('当前已是新对话', undefined, 1000, 'center');
      }
      return;
    }

    // 清空输入框
    if (composerInputRef && composerInputRef.current) {
      composerInputRef.current.setText('');
    }
    setShowSensitiveError(false);

    if (showStopAnswer) {
      handleStopAnswer();
    }

    // 埋点上报
    const params = LogPointConfigMap.get('handleNewConversation');
    onReportLog?.(params);

    setIsNewConversation(true);
    isNewConversationRef.current = true;
    resetList();

    if (initConfig?.url) {
      // 调用欢迎语接口获取conversationId
      // postRequest(requestConfig?.baseUrl, {
      //   payload: { appId: appId, userId: userId, sceneId: sceneId },
      //   ...initConfig
      // }).then((data) => {
      //   if (data && data?.code === '0') {
      //     const resultData = data?.resultData;
      //     setWelcomeInfo(resultData || {});
      //     conversationId.current = resultData?.conversationId;
      //     setNoMoreHistory(true);
      //   }
      // });
      initHooksRun();
    } else {
      // 不调用接口拿conversationId的情况清空
      conversationId.current = '';
    }
  }, [isHistoryOpen, showStopAnswer]);

  useEffect(() => {
    if (initHooksData && initHooksData?.code === '0') {
      const resultData = initHooksData?.resultData;
      setWelcomeInfo(resultData || {});
      conversationId.current = resultData?.conversationId;
      setNoMoreHistory(true);
    }
  }, [initHooksData]);

  useEffect(() => {
    if (historyConversationHooksData && historyConversationHooksData?.code === '0') {
      const list = historyConversationHooksData?.resultData?.list ?? [];
      setHistoryConversationList(list);
    }
  }, [historyConversationHooksData]);

  useEffect(() => {
    dealwithHistoryMessageList(historyHooksData);
    // dealwithHistoryMessageList(testHistiory);
  }, [historyHooksData]);

  useEffect(() => {
    if (sendHooksData && sendHooksData?.code === '0') {
      isCheckingSensitiveRef.current = false;
      const resultData = sendHooksData?.resultData;
      // testData = {
      //   ...sendHooksData
      //   list: testData.list.push(sendHooksData)
      // }
      if (sendConfig?.stream) {
        dealwithSSEData(resultData);
      } else {
        dealwithCommonData(resultData);
      }

      // 保证只在返回数据endFlag为true-最后一条的时候执行
      if (!showPushHistory && changedConversation.current && sendHooksData?.resultData?.endFlag) {
        // 不是打开历史会话面板的情况需要在发送成功再查一次历史会话列表
        historyConversationHooksRun();
      }
    }
  }, [sendHooksData]);

  useEffect(() => {
    // 如果发送有报错，则打上报错标记
    if (sendHooksError) {
      setTyping(false);
      setUnsend();
    }
  }, [sendHooksError])

  // useEffect(() => {
  //   if (sendHooksData?.code === '0' && sendHooksData?.resultData?.endFlag) {
  //     if (!showPushHistory && changedConversation.current) {
  //       // 不是打开历史会话面板的情况需要在发送成功再查一次历史会话列表
  //       historyConversationHooksRun();
  //     }
  //   }
  // }, [sendHooksData?.resultData?.endFlag])

  useEffect(() => {
    if (isSafari()) {
      document.documentElement.dataset.safari = '';
    }
    // 切换宽窄屏的时候不用重新调初始化接口
    if (initConfig?.url && !initConversationObj) {
      initHooksRun();
      // 初始进入
      // fetchClient.post<Tmessage>({
      //   url: initConfig?.url,
      //   body: { appId: appId, userId: userId, sceneId: sceneId },
      //   headers: initConfig?.headers
      // postRequest(requestConfig?.baseUrl, {
      //   payload: { appId: appId, userId: userId, sceneId: sceneId },
      //   ...initConfig
      // }).then((data) => {
      //   if (data && data?.code === '0') {
      //     const resultData = data?.resultData;
      //     setWelcomeInfo(resultData || {});
      //     // setIsComponentFirstLoad(!resultData?.hasHistory);
      //     conversationId.current = resultData?.conversationId;

      //     // if (!resultData?.hasHistory) {
      //     // setIsComponentFirstLoad(!localStorage.getItem('hasChatUIComponentLoaded'));
      //     // }
      //     // if (resultData?.title || resultData?.subtitle) {
      //     // if (resultData?.welcomeMsg) {
      //     //   appendMsg?.({
      //     //     type: 'text',
      //     //     // content: { text: `${resultData?.title || ''}${resultData?.subtitle || ''}` },
      //     //     content: { text: `${resultData?.welcomeMsg || ''}` },
      //     //     createdAt: Date.now(),
      //     //     hasTime: true,
      //     //   });
      //     // }

      //     // if (resultData?.relatedQuestionList?.length > 0) {
      //     //   appendMsg?.({
      //     //     type: 'relatedQuestionList',
      //     //     content: {
      //     //       relatedQuestionList: resultData?.relatedQuestionList,
      //     //     },
      //     //   });
      //     // }
      //     // setNoMoreHistory(!resultData?.hasHistory);
      //   }
      // });
    }
    // if (quickReplyConfig?.url) {
    //   // 快捷问题查询
    //   // fetchClient.post<Tmessage>({
    //   //   url: quickReplyConfig?.url,
    //   //   body: { appId: appId, userId: userId, sceneId: sceneId },
    //   //   headers: quickReplyConfig?.headers
    //   postRequest(requestConfig?.baseUrl, {
    //     payload: { appId: appId, userId: userId, sceneId: sceneId },
    //     ...quickReplyConfig
    //   }).then((data) => {
    //     if (data && data?.code === '0') {
    //       const quickReply = data?.resultData?.quickReply ?? [];
    //       const newQuickReplies = [];

    //       for (let i = 0; i < quickReply?.length; i++) {
    //         newQuickReplies.push({
    //           name: quickReply[i]?.title,
    //           code: quickReply[i]?.content,
    //         });
    //       }
    //       setQuickReplies(newQuickReplies)
    //     }
    //   });
    // }

    if (historyConversationConfig?.url) {
      // 查历史会话
      // postRequest(requestConfig?.baseUrl, {
      //   payload: { appId: appId, userId: userId, sceneId: sceneId },
      //   ...historyConversationConfig
      // }).then((data) => {
      //   if (data && data?.code === '0') {
      //     const list = data?.resultData?.list ?? [];
      //     setHistoryConversationList(list);
      //     // 如果没有历史会话，那展示新会话页面
      //     // if (list?.length === 0) {
      //     //   setIsNewConversation(true);
      //     //   isNewConversationRef.current = true;
      //     // } else if (!showNewConversation) {
      //     //   // 如果没有要展示新会话，则默认展示历史会话中最新的那一条的历史消息,并且滚动到底部
      //     //   conversationId.current = list?.[0]?.conversationId
      //     //   handleRefresh(true);
      //     // }
      //   }
      // });
      historyConversationHooksRun();
    }

    if (queryRiskReadConfig?.url) {
      // 查询是否阅读了风险提示接口
      postRequest(requestConfig?.baseUrl, {
        payload: { appId: appId, userId: userId, sceneId: sceneId, businessTypeCode: "AortaAIContract" },
        ...queryRiskReadConfig
      }).then((data) => {
        if (data && data?.code === '0') {
          setRiskRead(data?.resultData?.hasRead ?? false);
          // 没签署过强制提示风险
          if (!data?.resultData?.hasRead) {
            setShowRiskTip(true);
          }
        }
      });
    }

    if (feedbackLabelConfig?.url) {
      postRequest(requestConfig?.baseUrl, {

        payload: { appId: appId, userId: userId, sceneId: sceneId },
        ...feedbackLabelConfig
      }
      ).then((data) => {
        if (data && data?.code === '0') {
          setFeedbackLabels(data?.resultData ?? []);
        }
      })
    }
  }, []);

  const toggleEnableInternetSearch = useCallback(() => {
    setEnableInternetSearch(!enableInternetSearch);

    // 埋点上报
    const params = !enableInternetSearch ? LogPointConfigMap.get('toggleEnableInternetSearchOpen') : LogPointConfigMap.get('toggleEnableInternetSearchClose');
    onReportLog?.(params);
  }, [enableInternetSearch]);

  // 切换历史会话
  const selectHistoryConversation = useCallback((conversation: any) => {
    isSendEndFlag.current = false;
    isNewChangedConversation.current = true;
    // 如果点中的是本来就高亮的，关闭弹窗，啥也不做
    if (conversationId?.current === conversation.conversationId) {
      changedConversation.current = false;
      toggleHistory();
      return;
    }

    conversationId.current = conversation.conversationId;
    changedConversation.current = true;

    setIsNewConversation(false);
    isNewConversationRef.current = false;

    if (showStopAnswer) {
      handleStopAnswer();
    }

    // 清空输入框
    if (composerInputRef && composerInputRef.current) {
      composerInputRef.current.setText('');
    }

    resetList();

    toggleHistory();

    // 切换历史会话消除敏感词提示
    setShowSensitiveError(false);

    historyPageNum.current = 1;
    historyNextOffset.current = '';
    // 刷新历史会话消息，并滚动到底部
    needScrollToEnd.current = true;
    setNoMoreHistory(true);

    // 支持动态传参
    historyHooksRun({ conversationId: conversationId?.current, pageNum: historyPageNum.current });
    // 埋点上报
    const params = LogPointConfigMap.get('selectHistoryConversation');
    if (params) {
      onReportLog?.({
        ...params,
        btn_title: {
          ...(typeof params.btn_title === 'object' ? params.btn_title : {}),
          value: conversation.conversationId,
        }
      });
    }
  }, [showStopAnswer, toggleHistory]);

  const handleRiskOpen = useCallback(() => {
    setShowRiskTip(true);
    // 埋点上报
    const params = LogPointConfigMap.get('handleRiskOpen');
    onReportLog?.(params);
  }, []);

  const handleRiskClose = useCallback(() => {
    setShowRiskTip(false);
    // 埋点上报
    const params = LogPointConfigMap.get('handleRiskClose');
    onReportLog?.(params)
  }, []);

  const handleRiskAgree = useCallback(() => {
    if (riskReadConfig?.url) {
      // 埋点上报
      const params = LogPointConfigMap.get('handleRiskAgree');
      onReportLog?.(params);
      postRequest(requestConfig?.baseUrl, {
        payload: {
          appId: appId,
          userId: userId,
          sceneId: sceneId,
          businessTypeCode: "AortaAIContract",
          // 20250318这种数字格式
          sequenceNumber: Number(new Date().toISOString().slice(0, 10).replace(/-/g, '')),
          // 2025-03-18 11:37:27这种格式
          stamp: getDateFormat(Date.now()),
        },
        ...riskReadConfig
      }).then((res) => {
        if (res?.resultData?.ifSuccess) {
          setRiskRead(true);
        }
      });
    }
  }, []);

  const handleFeedbackGuideClose = () => {
    clearTimeout(feedbackGuideTimer.current);
    feedbackGuideTimer.current = null;
    setShowFeedbackGuide(false);
  }
  const handleFeedbackGuideOk = () => {
    if (msgsRef && msgsRef.current) {
      clearTimeout(feedbackGuideTimer.current);
      feedbackGuideTimer.current = null;
      setShowFeedbackGuide(false);
      msgsRef.current.scrollToEnd({ animated: false, force: true });
    }
  }

  const handleResize = useCallback(() => {
    const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight
    if (resizeHeight < originHeight?.current) {
      // 软键盘弹起
      setKeyboardShow(true);
    } else {
      // 软键盘收起
      setKeyboardShow(false);
    }
  }, [])

  useEffect(() => {
    if (isMobile) {
      // 通过监听 resize 事件监听键盘收起和弹起
      originHeight.current = document.documentElement.clientHeight || document.body.clientHeight;
      window.addEventListener('resize', handleResize);
    }

    return () => {
      if (isMobile) {
        window.removeEventListener('resize', handleResize);
      }
    }
  }, []);

  useEffect(() => {
    if (initConversationObj?.initConversationId) {
      conversationId.current = initConversationObj?.initConversationId;
      historyHooksRun({ conversationId: initConversationObj?.initConversationId, pageNum: historyPageNum.current });
    }
  }, [initConversationObj])

  // 切换宽窄屏回调函数
  const handleSwitchWideNarrow = () => {
    if (msgsRef && msgsRef.current) {
      onSwitchWideNarrow?.({ initConversationId: conversationId?.current });
    }
  };

  useEffect(() => {
    const lastOpenTime = localStorage.getItem(feedbackGuideLocalStorageKey);

    if (isNewChangedConversation.current || isHistoryOpen) {
      clearTimeout(feedbackGuideTimer.current);
      feedbackGuideTimer.current = null;
      return;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const twoWeeks = 14 * 24 * 60 * 60; // 14天的秒数
    // 隔2周提醒一次
    if (!lastOpenTime || (lastOpenTime && (currentTime - Number(lastOpenTime)) > twoWeeks)) {
      // 新会话，第一次问答结束， 十秒内不再发送新问题, 则展示反馈引导弹窗。保证在当前页面，没有切换会话，没有打开会话列表
      if (messages?.length === 2 && isSendEndFlag.current && !isNewChangedConversation.current && !isHistoryOpen) {
        feedbackGuideTimer.current = setTimeout(() => {
          setShowFeedbackGuide(true);
          localStorage.setItem(feedbackGuideLocalStorageKey, String(currentTime));
        }, 10 * 1000)

      }
    }
  }, [messages, isNewChangedConversation.current, isSendEndFlag.current, isHistoryOpen]);

  const isHistoryShow = typeof props.historyConversation?.show === 'boolean' ? props.historyConversation?.show : true;

  const historyPanelStyle = pushPosition === 'left'
    ?
    {
      left: 0,
      display: isHistoryShow ? '' : 'none',
      transform: 'translateX(-100%)', /* 初始隐藏在左边 101去掉*/
      width: `${pushPercent}%`, /* 历史面板展开宽度 */
    }
    : {
      right: 0,
      display: isHistoryShow ? '' : 'none',
      transform: 'translateX(100%)', /* 初始隐藏在右边 */
      width: `${pushPercent}%`, /* 历史面板展开宽度 */
    };

  /* 历史面板展开时复位 */
  const historyPanelPushStyle = isHistoryOpen ? { transform: 'translateX(0)' } : {}

  /* 历史面板展开时，用transform实现视觉偏移 */
  const chatPannelPushStyle = isHistoryShow ? (
    pushPosition === 'left'
      ? {
        transform: `translateX(${pushPercent}%)` /* 向左推相应宽度 */,
      }
      : {
        transform: `translateX(-${pushPercent}%)` /* 向右推相应宽度 */,
      }) : {};

  const conversationNavbarProps = isMobile
    ? {
      showLogo: true,
      logo: config?.robot?.logo || '',
      title: '历史会话记录',
      showNewButton: false,
      wrapstyle: navbar?.wrapstyle,
    } : {
      showLogo: true,
      logo: config?.robot?.logo || '',
      title: '历史会话记录',
      onNewButtonClick: handleNewConversation,
      onCloseButtonClick: toggleHistory,
      showNewButton: true,
      newConTooltip: '开启新对话',
      showCloseButton: true,
      wrapstyle: navbar?.wrapstyle,
      ...historyConversation?.navbar
    };

  const chatContextState: ChatContext = useMemo(() => {

    const data = {
      showStopAnswer,
      isNewConversation,
      setShowStopAnswer,
      setIsNewConversation,
      historyConversationList,
      conversationId,
    };
    window.document.dispatchEvent(new CustomEvent('chatui-update-chat-context', { detail: data }))
    return data;
  }, [showStopAnswer, isNewConversation, historyConversationList, conversationId?.current]);

  useImperativeHandle(ref, () => {
    return {
      chatContext: {
        ...chatContextState,
        config,
        onSend: onSend || handleSend,
        handleStopAnswer,
        handleNewConversation,
        selectHistoryConversation,
        toggleHistory,
      },
    };
  }, [chatContextState, config, onSend, handleStopAnswer]);
  return (
    <div className="PushDivContainer">
      {
        showPushHistory &&
        <div className="HistoryPanel" style={{ ...historyPanelStyle, ...historyPanelPushStyle }}>
          <HistoryConversation
            style={{ height: 'calc(100% - 6px)', width: 'calc(100% - 6px)', margin: 0 }}
            title={historyConversation?.title}
            logo={historyConversation?.logo || config?.robot?.logo || ''}
            navbar={conversationNavbarProps}
            list={historyConversationList}
            activeConversationId={conversationId?.current}
            onConversationClick={selectHistoryConversation}
            renderBrand={historyConversation?.renderBrand}
            renderFooter={historyConversation?.renderFooter}
            showSearch={historyConversation?.showSearch}
          />
        </div>
      }

      <div className="ChatWrap" id="ChatComponentsWrap" style={isHistoryOpen ? chatPannelPushStyle : {}}>
        {/* 历史会话面板打开时的遮罩层 */}
        {showPushHistory && isHistoryOpen && <div className={`ChatOverlay ${isHistoryOpen && isHistoryShow ? 'ChatOverlayShow' : ''}`} onClick={toggleHistory}></div>}
        {showRiskTip && isMobile && <RiskTipApp onClose={handleRiskClose} onAgree={handleRiskAgree} hasAgreedRisk={riskRead} onChatClose={navbar?.onCloseButtonClick} />}
        {showRiskTip && !isMobile && <RiskTipPc onClose={handleRiskClose} onAgree={handleRiskAgree} hasAgreedRisk={riskRead} onChatClose={navbar?.onCloseButtonClick} />}
        {showFeedbackGuide && <FeedbackGuide onClose={handleFeedbackGuideClose} onOkBtn={handleFeedbackGuideOk} />}
        <LocaleProvider locale={locale} locales={locales}>
          <WaterMark  {...{ ...config?.waterMark, show: config?.waterMark?.show && !isNewConversation }}  >
            <div className="ChatApp">
              {/* 新开会话需要展示GuidePage */}
              {isNewConversation ?
                <>
                  {renderWelcome ? renderWelcome({
                    // guideWelcome: welcomeInfo ? { ...welcomeInfo, logo: welcome?.logo || config?.robot?.logo || '' } : welcome,
                    guideWelcome: { ...welcomeInfo, ...welcome, logo: welcome?.logo || config?.robot?.logo || '' },
                    navbar: {
                      ...PreprocessNavBarConfig(welcome?.navbar?.logo || navbar?.logo || config?.robot?.logo || '', navbar, welcome?.navbar)?.welcomNavBar,
                      newConTooltip: isMobile ? '' : (isNewConversation ? '当前已是新对话' : '开启新对话'),
                      onNewButtonClick: handleNewConversation, onHistoryButtonClick: toggleHistory,
                      onSwitchWideNarrow: handleSwitchWideNarrow
                    },
                    onRiskClick: handleRiskOpen,
                    isAorta: isAorta,
                    keyboardShow: keyboardShow
                  }) :
                    <GuidePage
                      // 接口返回配置只取title,subtitle,riskTip展示，如外部配置了则用外部配置的覆盖掉
                      guideWelcome={{ ...welcomeInfo, ...welcome, logo: welcome?.logo || config?.robot?.logo || '' }}
                      // guideWelcome={welcomeInfo ? { ...welcomeInfo, logo: welcome?.logo || config?.robot?.logo || '' } : welcome}
                      // navbar={{ ...navbar, showLogo: false, onNewButtonClick: handleNewConversation, onHistoryButtonClick: toggleHistory, isNewConversation: isNewConversation, }}
                      navbar={{
                        // 欢迎页navbar自定义logo图标先取welcome?.navbar?.logo，没有再取navbar?.logo，没有再取config?.robot?.logo
                        // 如果都没有，Navbar组件中有默认内置logo图标
                        ...PreprocessNavBarConfig(welcome?.navbar?.logo || navbar?.logo || config?.robot?.logo || '', navbar, welcome?.navbar)?.welcomNavBar,
                        newConTooltip: isMobile ? '' : (isNewConversation ? '当前已是新对话' : '开启新对话'),
                        onNewButtonClick: handleNewConversation, onHistoryButtonClick: toggleHistory,
                        onSwitchWideNarrow: handleSwitchWideNarrow
                      }}
                      onRiskClick={handleRiskOpen}
                      isAorta={isAorta}
                      keyboardShow={keyboardShow}
                      onSend={onSend || handleSend}
                      // 如果有welcome?.renderNavbar则用welcome?.renderNavbar的自定义渲染；没有则用welcome?.navbar的参数渲染；
                      // 以上两个都没有再用公共renderNavbar方法渲染
                      // 如果连公共renderNavbar方法也没有，在PreprocessNavBarConfig中处理了取公共navbar参数渲染
                      renderNavbar={welcome?.renderNavbar ? welcome?.renderNavbar : (welcome?.navbar && Object.keys(welcome?.navbar)?.length ? undefined : renderNavbar)}
                    />
                  }
                </> :
                <>
                  {/* {renderNavbar ? renderNavbar() : <Navbar {...navbar} logo={navbar?.logo || config?.robot?.logo || ''} onNewButtonClick={handleNewConversation} onHistoryButtonClick={toggleHistory} />} */}
                  {renderNavbar ? renderNavbar() :
                    <Navbar
                      // 公共navbar自定义logo图标先取navbar?.logo，没有再取config?.robot?.logo
                      // 如果都没有，Navbar组件中有默认内置logo图标
                      {...PreprocessNavBarConfig(navbar?.logo || config?.robot?.logo || '', navbar)?.navBar}
                      newConTooltip={isMobile ? '' : `${isNewConversation ? '当前已是新对话' : '开启新对话'}`}
                      onNewButtonClick={handleNewConversation}
                      onHistoryButtonClick={toggleHistory}
                      onSwitchWideNarrow={handleSwitchWideNarrow}
                    />
                  }
                  <MessageContainer
                    ref={msgsRef}
                    loadMoreText={loadMoreText}
                    messages={messages}
                    lowCodeConfig={config?.lowCode}
                    renderBeforeMessageList={renderBeforeMessageList}
                    renderMessageContent={renderMessageContent}
                    renderCardContent={renderCardContent}
                    onRefresh={onRefresh || (!noMoreHistory ? handleRefresh : undefined)}
                    onScroll={onScroll}
                    backBottomButton={backBottomButton}
                    onBackBottomShow={onBackBottomShow}
                    onBackBottomClick={onBackBottomClick}
                    onFeedBack={handleFeedBack}
                    onReportLog={onReportLog}
                    copyText={config?.bridge?.copyText}
                    openPage={config?.bridge?.openWebPage}
                    openFileViews={config?.bridge?.openFileViews}
                    onSend={(onSend ? onSend : undefined) || handleSend}
                    isDev={config.isDev}
                    userId={config.userId}
                    waterMark={config?.waterMark}
                    robot={config.robot}
                    // 是否展示反馈弹窗
                    showFeedbackModal={showFeedbackModal}
                    // 反馈弹窗自定义配置
                    feedbackModalConfig={feedbackModalConfig}
                    feedbackLabels={feedbackLabels}
                    downloadFileAndView={config?.bridge?.downloadFileAndView}
                    openRawUrl={config?.bridge?.openRawUrl}
                    openCallbackUrl={config?.bridge?.openCallbackUrl}
                    keyboardShow={keyboardShow}
                    initConversationObj={initConversationObj}
                  />
                </>}
              {renderQuickReplies ? (
                renderQuickReplies()
              ) : (
                <QuickReplies
                  quickReplies={quickReplies}
                  openPage={config?.bridge?.openWebPage}
                  onClick={onQuickReplyClick || handleQuickReplyClick}
                  onScroll={onQuickReplyScroll}
                />
              )}
              <div className="ChatFooter">
                {CustomerComposer
                  ? <CustomerComposer ref={composerInputRef}
                    inputType={inputType}
                    text={text}
                    inputOptions={{ maxRows: isNewConversation ? 3 : 6, ...inputOptions }}
                    placeholder={placeholder}
                    onAccessoryToggle={onAccessoryToggle}
                    recorder={recorder}
                    toolbar={toolbar}
                    onToolbarClick={onToolbarClick}
                    onInputTypeChange={onInputTypeChange}
                    onFocus={handleInputFocus}
                    onChange={handleInputChange || onInputChange}
                    onBlur={handleInputBlur}
                    onSend={onSend || handleSend}
                    onImageSend={onImageSend}
                    // rightAction={rightAction}
                    // 没有配置停止生成的接口时，不展示停止生成按钮
                    showStopAnswer={showStopAnswer && stopConfig?.url}
                    onStopAnswer={handleStopAnswer}
                    showInternetSearch={showInternetSearch}
                    enableInternetSearch={enableInternetSearch}
                    toggleEnableInternetSearch={toggleEnableInternetSearch}
                    aboveNode={showSensitiveError ? (<div className='SensitiveError'>您的输入可能包含投资者敏感信息，请核对~</div>) : undefined}
                  />
                  : <Composer
                    // wideBreakpoint={wideBreakpoint}
                    isWide={isWide}
                    ref={composerInputRef}
                    inputType={inputType}
                    text={text}
                    inputOptions={{ maxRows: isNewConversation ? 3 : 6, ...inputOptions }}
                    placeholder={placeholder}
                    onAccessoryToggle={onAccessoryToggle}
                    recorder={recorder}
                    toolbar={toolbar}
                    onToolbarClick={onToolbarClick}
                    onInputTypeChange={onInputTypeChange}
                    onFocus={handleInputFocus}
                    onChange={handleInputChange || onInputChange}
                    onBlur={handleInputBlur}
                    onSend={onSend || handleSend}
                    onImageSend={onImageSend}
                    // rightAction={rightAction}
                    // 没有配置停止生成的接口时，不展示停止生成按钮
                    showStopAnswer={showStopAnswer && stopConfig?.url}
                    onStopAnswer={handleStopAnswer}
                    showInternetSearch={composerConfig?.showInternetSearch}
                    uploadConfig={composerConfig?.uploadConfig}
                    enableInternetSearch={enableInternetSearch}
                    toggleEnableInternetSearch={toggleEnableInternetSearch}
                    aboveNode={showSensitiveError ? (<div className='SensitiveError'>您的输入可能包含投资者敏感信息，请核对~</div>) : undefined}
                    onCompanyDataSelectAgentsChange={handleCompanyDataSelectAgentsChange}
                    selectedAgents={selectedAgents}
                    companyDataConfig={companyDataConfig}
                    onReportLog={onReportLog}
                  />}
                {
                  renderFooterVersion
                    ? renderFooterVersion()
                    : (<div className="ChatFooter-Version" />)
                  // : (<div className="ChatFooter-Version">内容由大数据模型生成，不构成投资建议</div>)
                }
              </div>
            </div>
          </WaterMark>
        </LocaleProvider>
      </div>
    </div >
  );
});
