import React, { useState } from 'react';
import { ReferencesProps, ReferenceProps } from './interface';
import { isMobile } from '../../utils/canUse';
import upSrc from '../ThinkContent/images/up.svg'
import downSrc from '../ThinkContent/images/down.svg'
import { LogPointConfigMap } from '../../LogPointConfigMap';

export const References = (props: ReferencesProps) => {
  const { references, openPage, downloadFileAndView, openRawUrl, openCallbackUrl, onReportLog, reMeasureSize } = props;

  const [isReferencesCollapsed, setIsReferencesCollapsed] = useState(true);

  const toggleReferencesCollapse = () => {
    // 埋点上报
    const params = LogPointConfigMap.get(isReferencesCollapsed ? 'toggleReferencesCollapseOpen' : 'toggleReferencesCollapseClose');
    onReportLog?.(params);
    setIsReferencesCollapsed(!isReferencesCollapsed);
    // 通知虚拟列表重新测算尺寸
    if (reMeasureSize) {
      reMeasureSize();
    }
  }

  const handleClick = (item: ReferenceProps) => {
    // 埋点上报
    const params = LogPointConfigMap.get('clickReference');
    if (params) {
      onReportLog?.({
        ...params,
        btn_title: {
          ...(typeof params.btn_title === 'object' ? params.btn_title : {}),
          value: item.title || item?.docName,
        }
      });
    }
    if (isMobile) { // app端有rawUrl（云文档链接）则用云文档预览方法打开，没有rwaUrl则看有downloadUrl（内部引用）取downloadUrl进行下载预览,没有则取url(外部引用)进行打开
      if (item?.callbackUrl) {
        if (openCallbackUrl) {
          openCallbackUrl(item?.callbackUrl);
        } else {
          console.error('openCallbackUrl function is not defined');
        }
      } else if (item?.rawUrl) {
        if (openRawUrl) {
          openRawUrl(item?.rawUrl);
        } else {
          console.error('openRawUrl function is not defined');
        }
      } else if (item?.downloadUrl && item?.fileType) {
        const logParams = LogPointConfigMap.get('referencePageView');
        if (logParams) {
          onReportLog?.({
            ...logParams,
            btn_title: {
              ...(typeof logParams.btn_title === 'object' ? logParams.btn_title : {}),
            }
          });
        }
        if (downloadFileAndView) {
          downloadFileAndView(item?.downloadUrl, item?.fileType);
        } else {
          console.error('暂无预览方法，无法预览文件');
        }
      } else if (item?.url) {
        if (openPage) {
          openPage(item?.url)
        } else {
          window.open(item?.url)
        }
      } else {
        console.error('no callbackUrl, downloadUrl or url');
      }

    } else { // pc端有url(云文档 或 外部引用)取url进行预览，没有则取downloadUrl进行下载预览
      if (item?.callbackUrl) {
        if (openCallbackUrl) {
          openCallbackUrl(item?.callbackUrl);
        } else {
          console.error('openCallbackUrl function is not defined');
        }
      } else if (item?.url) {
        const logParams = LogPointConfigMap.get('referencePageView');
        if (logParams) {
          onReportLog?.({
            ...logParams,
            btn_title: {
              ...(typeof logParams.btn_title === 'object' ? logParams.btn_title : {}),
            }
          });
        }
        if (openPage) {
          openPage(item?.url)
        } else {
          window.open(item?.url)
        }
      } else {
        // 只返回downloadUrl的先获取二进制文件流再进行后续预览操作
        if (item?.downloadUrl && item?.fileType) {
          if (downloadFileAndView) {
            const logParams = LogPointConfigMap.get('referencePageView');
            if (logParams) {
              onReportLog?.({
                ...logParams,
                btn_title: {
                  ...(typeof logParams.btn_title === 'object' ? logParams.btn_title : {}),
                }
              });
            }
            downloadFileAndView(item?.downloadUrl, item?.fileType);
          } else {
            console.error('暂无预览方法，无法预览文件');
          }
        } else {
          console.error('no callbackUrl, url or downloadUrl');
        }
      }
    }
  }

  if (references?.length < 1) {
    return null;
  }

  return (
    <div className="ReferencesWrap">
      <div className="ReferencesTitle" onClick={toggleReferencesCollapse}>
        参考来源{` ${references?.length}`}
        {isReferencesCollapsed
          ? <img src={downSrc} className="CollapsedIcon" />
          : <img src={upSrc} className="CollapsedIcon" />
        }
      </div>
      {
        !isReferencesCollapsed && (
          <div className="ReferencesContent">
            {
              references?.map((item) => (
                <div
                  key={item?.title || item?.docName}
                  className='ReferenceItem'
                  onClick={() => handleClick(item)}
                >
                  <span className='ReferenceItem-dot'>·</span>{`${item?.source === 'inner' ? '' : '【联网】'}${item?.title || item?.docName}`}
                </div>
              ))
            }
          </div>
        )
      }
    </div>
  );
};