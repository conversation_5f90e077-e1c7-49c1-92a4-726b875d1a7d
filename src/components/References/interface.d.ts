// 参考资料
export interface ReferencesProps extends React.HTMLAttributes<HTMLDivElement> {
  references: ReferenceProps[];
  openPage?: (url: string) => void;
  downloadFileAndView?: (url: Blob | string, type: string) => void;
  openRawUrl?: (url: string) => void;
  openCallbackUrl?: (url: string) => void;
  /**
    * log上报回调
    */
  onReportLog?: (params: ILogParams | undefined) => void;
  /**
    * 是否预览
    */
  isPreview?: boolean;
  // 通知虚拟列表重新测算尺寸
  reMeasureSize?: () => void;
}

export interface ReferenceProps {
  /**
   * 链接地址
   */
  url?: string;
  /**
   * 网页名称
   */
  title: string;
  source?: string;
  docName?: string;
  downloadUrl?: string;
  fileType?: string;
  citationIndex?: number;
  periodType?: string;
  category?: string;
  docId?: string;
  /**
   * app云文档链接地址
   */
  rawUrl?: string;
  /**
   * 资讯研报回调地址
   */
  callbackUrl?: string;
}