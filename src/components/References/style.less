.ReferencesWrap {
  margin-top: 20px;

  .ReferencesTitle {
    font-size: 14px;
    color: #888;
    line-height: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;

    .CollapsedIcon {
      margin-left: 4px;
      height: 14px;
      width: 14px;
    }
  }

  .ReferencesContent {
    border-radius: 12px;
    border: 1px solid #d5d5d5;
    padding: 15px 12px;
    margin-top: 20px;

    .ReferenceItem {
      font-size: 12px;
      color: #666;
      line-height: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-top: 15px;
      cursor: pointer;

      &-dot {
        margin-right: 6px;
      }

      &:first-child {
        margin-top: 0;
      }
    }
  }
}

.WordViewWrap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-size: 20px;
  z-index: 10000;
  background: #fff;
  text-align: center;

  .closeWrap {
    position: relative;
    z-index: 10001;
    width: 100%;
    height: 32px;
    background-color: #474747;
  }

  &-close {
    position: absolute;
    top: 9px;
    right: 9px;
    width: 14px;
    height: 14px;
    z-index: 10001;
  }
}

.WordContent {
  width: 100%;
  height: calc(~'100% - 32px');
  overflow: auto;
  border: 0;
  position: relative;
  z-index: 50;


  .docx-wrapper {
    padding: 0 !important;
  }

}
