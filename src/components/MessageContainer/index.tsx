/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable no-underscore-dangle */
import React, { useState, useEffect, useRef, useCallback, useImperativeHandle } from 'react';
import { PullToRefresh, PullToRefreshHandle, ScrollToEndOptions } from '../PullToRefresh';
import { Message, MessageProps, CardProps } from '../Message';
import { BackBottom } from '../BackBottom';
import canUse from '../../utils/canUse';
import throttle from '../../utils/throttle';
import getToBottom from '../../utils/getToBottom';
import { ILogParams } from '../../LogPointConfigMap';
import { FeedbackLabelProps } from '../FeedbackTipPc/interface';
import { useVirtualizer } from '@tanstack/react-virtual';

const listenerOpts = canUse('passiveListener') ? { passive: true } : false;

export interface MessageContainerProps {
  messages: MessageProps[];
  renderMessageContent?: (message: MessageProps) => React.ReactNode;
  renderCardContent?: (cards: CardProps[]) => React.ReactNode;
  loadMoreText?: string;
  onRefresh?: () => Promise<any>;
  onScroll?: (event: React.UIEvent<HTMLDivElement, UIEvent>) => void;
  renderBeforeMessageList?: () => React.ReactNode;
  backBottomButton?: { icon?: string };
  onBackBottomShow?: () => void;
  onBackBottomClick?: () => void;
  onFeedBack?: (score: MessageProps['feedbackResult'], message: MessageProps, reason?: any) => void;
  onReportLog?: (params: ILogParams | undefined) => void;
  copyText?: (text: string) => void;
  backBottomButtonProps?: any; // 回到底部按钮组件属性透传
  // 模方卡片用到的
  lowCodeConfig?: object;
  isDev?: boolean;
  userId?: string;
  waterMark?: object;
  robot?: {
    logo?: string;
  };
  // GuessAskMore卡片需要的发送
  onSend?: (type: string, content: string, params?: object) => Promise<boolean>;
  // GuessAskMore卡片可能需要的打开链接
  openPage?: () => {};
  // 文件预览方法
  openFileViews?: () => {};
  // 是否展示反馈弹窗
  showFeedbackModal?: boolean;
  //
  feedbackModalConfig?: {
    title?: string,
    inputPlaceholder?: string,
    showLabels?: boolean,
  };
  // 反馈标签
  feedbackLabels?: FeedbackLabelProps[];
  // app端pdf预览方法
  downloadFileAndView?: () => {};
  // app端打开云文档预览方法
  openRawUrl?: () => {};
  openCallbackUrl?: () => {};
  keyboardShow?: boolean;
  initConversationObj?: any;
}

export interface MessageContainerHandle {
  ref: React.RefObject<HTMLDivElement>;
  scrollToEnd: (options?: ScrollToEndOptions) => void;
}

function isNearBottom(el: HTMLElement, n: number) {
  const offsetHeight = Math.max(el.offsetHeight, 600);
  return getToBottom(el) < offsetHeight * n;
}

const MessageContainerC = React.forwardRef<MessageContainerHandle, MessageContainerProps>(
  (props, ref) => {
    const {
      messages,
      loadMoreText,
      onRefresh,
      onScroll,
      renderBeforeMessageList,
      renderMessageContent,
      renderCardContent,
      backBottomButton,
      onBackBottomShow,
      onBackBottomClick,
      onFeedBack,
      onReportLog,
      copyText,
      backBottomButtonProps = {},
      lowCodeConfig,
      isDev,
      userId,
      waterMark,
      onSend,
      openPage,
      openFileViews,
      robot,
      showFeedbackModal,
      feedbackLabels,
      feedbackModalConfig,
      downloadFileAndView,
      openRawUrl,
      openCallbackUrl,
      keyboardShow,
      initConversationObj,
    } = props;

    const [showBackBottom, setShowBackBottom] = useState(false);
    const [newCount, setNewCount] = useState(0);
    const showBackBottomtRef = useRef(showBackBottom);
    const newCountRef = useRef(newCount);
    const messagesRef = useRef<HTMLDivElement>(null);
    const scrollerRef = useRef<PullToRefreshHandle>(null);
    const lastMessage = messages[messages.length - 1];

    const parentRef = useRef<HTMLDivElement>(null);
    // 上次滚动结束以后离底部的距离
    const lastToBottom = useRef(0);
    // 用来控制是不是用户往上面滚过了
    const isUserScrolled = useRef(false);
    const itemSizeCache = useRef<Map<number, number>>(new Map());

    const rowVirtualizer = useVirtualizer({
      count: messages.length,
      getScrollElement: () => parentRef.current,
      estimateSize: (index) => itemSizeCache.current.get(index) ?? 60, // 默认高度60px
      overscan: 5,
    });

    const measureSize = useCallback((node: HTMLDivElement | null, index: number) => {
      if (node) {
        const size = node.getBoundingClientRect().height;
        const currentSize = itemSizeCache.current.get(index);

        // 只有当高度变化时才更新
        if (currentSize !== size) {
          itemSizeCache.current.set(index, size);
          // 通知虚拟化器重新计算布局
          rowVirtualizer.measure();
        }
      }
    }, [rowVirtualizer]);

    // 有子组件内部需要重新测算高度的话，手动调用此方法，比如折叠收起
    const reMeasureSize = useCallback(() => {
      rowVirtualizer.measure();
    }, [rowVirtualizer]);

    const getScrollToTop = useCallback(() => {
      return parentRef?.current?.scrollTop ?? 0;
    }, []);

    const clearBackBottom = () => {
      setNewCount(0);
      setShowBackBottom(false);
    };

    const scrollToEnd = useCallback((opts?: ScrollToEndOptions) => {
      if (scrollerRef.current) {
        if (!showBackBottomtRef.current || (opts && opts.force)) {
          scrollerRef.current.scrollToEnd(opts);
          if (showBackBottomtRef.current) {
            clearBackBottom();
          }
        }
      }

      // 数据多的情况下一次不能滚到底，100毫秒再滚一次
      if (!isUserScrolled.current || opts?.force) {
        rowVirtualizer.scrollToOffset(rowVirtualizer?.getTotalSize(), { behavior: 'auto' });
        // 根据虚拟以后最后一个index做比对来判断是否真的滚到底，没有滚到底则延迟100再滚一次
        const lastVisibleIndex = rowVirtualizer?.getVirtualItems().slice(-1)[0]?.index;
        if (lastVisibleIndex !== messages?.length - 1) {
          console.warn('没滚到底，延迟100再滚一次');
          setTimeout(() => {
            // rowVirtualizer.scrollToIndex(messages?.length - 1, { align: 'end', behavior: 'auto' });
            rowVirtualizer.scrollToOffset(rowVirtualizer?.getTotalSize(), { behavior: 'auto' });
          }, 100);
        }
      }

      // 清除回到底部按钮的逻辑
      if (showBackBottomtRef.current) {
        clearBackBottom();
      }
    }, [messages?.length, rowVirtualizer]);

    const handleBackBottomClick = () => {
      scrollToEnd({ animated: false, force: true });
      // setNewCount(0);
      // setShowBackBottom(false);

      if (onBackBottomClick) {
        onBackBottomClick();
      }
    };

    const checkShowBottomRef = useRef(
      throttle((el: HTMLElement) => {
        if (isNearBottom(el, 3)) {
          if (newCountRef.current) {
            // 如果有新消息，离底部0.5屏-隐藏提示
            if (isNearBottom(el, 0.5)) {
              // setNewCount(0);
              // setShowBackBottom(false);
              clearBackBottom();
            }
          } else {
            setShowBackBottom(false);
          }
        } else {
          // 3屏+显示回到底部
          setShowBackBottom(true);
        }
      }),
    );

    const handleScroll = (e: React.UIEvent<HTMLDivElement, UIEvent>) => {
      if (!parentRef.current) return;

      const currentToBottom = getToBottom(parentRef.current);
      // 当前离底部的位置如果大于上一次，则表示往上滚动，当差值大于这个阈值表示用户操作了往上滚
      if (currentToBottom - lastToBottom.current > 10) {
        // console.warn('↑ 向上滚动阈值以上', currentToBottom, lastToBottom.current);
        isUserScrolled.current = true;
      } else if (currentToBottom < lastToBottom.current) {
        // 当前离底部的位置如果小于上一次，则表示往下滚动
        const scrollHeight = (e.target as HTMLElement)?.scrollHeight || 0;
        const clientHeight = (e.target as HTMLElement)?.clientHeight || 0;
        const scrollTop = (e.target as HTMLElement)?.scrollTop || 0;
        // console.warn('↓ 向下滚动', lastToBottom.current, currentToBottom, '高度们:',scrollHeight, clientHeight , scrollTop, '差值', Math.abs(scrollHeight - clientHeight - scrollTop));

        // 当往下滚到离底部只有1以内了，就当已经是滚动到底了
        if (Math.abs(scrollHeight - clientHeight - scrollTop) < 1) {
          if (isUserScrolled) {
            isUserScrolled.current = false;
          }
        }
      }
      lastToBottom.current = currentToBottom;

      checkShowBottomRef.current(e.target);
      if (onScroll) {
        onScroll(e);
      }
    };

    useEffect(() => {
      newCountRef.current = newCount;
    }, [newCount]);

    useEffect(() => {
      showBackBottomtRef.current = showBackBottom;
    }, [showBackBottom]);

    useEffect(() => {
      // const scroller = scrollerRef.current;
      // const wrapper = scroller && scroller.wrapperRef.current;
      const wrapper = parentRef && parentRef.current;

      if (!wrapper || !lastMessage || lastMessage.position === 'pop') {
        return;
      }

      if (lastMessage.position === 'right') {
        // 自己发的消息，强制滚动到底部
        // scrollToEnd({ force: true });
        scrollToEnd();
      } else if (isNearBottom(wrapper, 1)) {
        // const animated = !!wrapper.scrollTop;
        // scrollToEnd({ animated, force: true });
        scrollToEnd();
      } else {
        setNewCount((c) => c + 1);
        setShowBackBottom(true);
      }
    }, [lastMessage, scrollToEnd]);

    useEffect(() => {
      const wrapper = messagesRef.current!;

      let needBlur = false;
      let startY = 0;

      function reset() {
        needBlur = false;
        startY = 0;
      }

      function touchStart(e: TouchEvent) {
        const { activeElement } = document;
        if (activeElement && activeElement.nodeName === 'TEXTAREA') {
          needBlur = true;
          startY = e.touches[0].clientY;
        }
      }

      function touchMove(e: TouchEvent) {
        if (needBlur && Math.abs(e.touches[0].clientY - startY) > 20) {
          (document.activeElement as HTMLElement).blur();
          reset();
        }
      }

      wrapper.addEventListener('touchstart', touchStart, listenerOpts);
      wrapper.addEventListener('touchmove', touchMove, listenerOpts);
      wrapper.addEventListener('touchend', reset);
      wrapper.addEventListener('touchcancel', reset);

      return () => {
        wrapper.removeEventListener('touchstart', touchStart);
        wrapper.removeEventListener('touchmove', touchMove);
        wrapper.removeEventListener('touchend', reset);
        wrapper.removeEventListener('touchcancel', reset);
      };
    }, []);

    useEffect(() => {
      if (initConversationObj?.initIndex) {
        setTimeout(() => {
          // rowVirtualizer.scrollToIndex(initConversationObj?.initIndex);
          scrollToEnd();
        }, 500)
      }
    }, [initConversationObj, scrollToEnd])

    useImperativeHandle(ref, () => ({ ref: messagesRef, scrollToEnd }), [ scrollToEnd]);

    return (
      <div className="MessageContainer" ref={messagesRef} tabIndex={-1} id="MessageContainer">
        {renderBeforeMessageList && renderBeforeMessageList()}
        <PullToRefresh
          onRefresh={onRefresh}
          onScroll={handleScroll}
          loadMoreText={loadMoreText}
          ref={scrollerRef}
          scrollToTop={getScrollToTop()}
          rowVirtualizer={rowVirtualizer}
        >
          {/* <div className="MessageList">
            {messages.map((msg) => (
              <Message
                {...msg}
                user={{ ...(msg.user || {}), avatar: msg.position === 'left' ? robot?.logo || msg?.user?.avatar : msg?.user?.avatar }}
                renderMessageContent={renderMessageContent}
                renderCardContent={renderCardContent}
                key={msg._id}
                onFeedBack={onFeedBack}
                onReportLog={onReportLog}
                copyText={copyText}
                lowCodeConfig={lowCodeConfig}
                isDev={isDev}
                userId={userId}
                onSend={onSend}
                openPage={openPage}
                openFileViews={openFileViews}
                showFeedbackModal={showFeedbackModal}
                // 反馈弹窗自定义配置
                feedbackModalConfig={feedbackModalConfig}
                feedbackLabels={feedbackLabels}
                downloadFileAndView={downloadFileAndView}
              />
            ))}
          </div> */}
          <div
            ref={parentRef}
            style={{
              height: '100%',
              // 键盘弹起的时候不给滚动（解决ios兼容问题COSMOS-19062）
              overflow: keyboardShow ? 'hidden' : 'auto',
              position: 'relative', // 添加相对定位
              flex: 1,
              /* 关键CSS，千万别删！！！不然IOS滚动位置就算乱了 */
              WebkitOverflowScrolling: 'touch', /* 启用iOS平滑滚动 */
              overscrollBehaviorY: 'none', /* 禁用弹性滚动 */
            }}
            onScroll={handleScroll}
            className="WideMessageList"
          >
            <div
              style={{
                height: `${rowVirtualizer?.getTotalSize()}px`,
                width: '100%',
                position: 'relative',
              }}
            >
              {rowVirtualizer.getVirtualItems().map((virtualItem) => {
                const msg = messages[virtualItem.index];

                return (
                  <div
                    key={msg._id}
                    ref={(node) => measureSize(node, virtualItem.index)}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualItem.start}px)`,
                      boxSizing: 'border-box',
                      // 确保项目不会超出容器
                      overflow: 'hidden',
                    }}
                    data-index={virtualItem.index} // 用于调试
                  >
                    <Message
                      {...msg}
                      user={{ ...(msg.user || {}), avatar: msg.position === 'left' ? robot?.logo || msg?.user?.avatar : msg?.user?.avatar }}
                      renderMessageContent={renderMessageContent}
                      renderCardContent={renderCardContent}
                      key={msg._id}
                      onFeedBack={onFeedBack}
                      onReportLog={onReportLog}
                      copyText={copyText}
                      lowCodeConfig={lowCodeConfig}
                      isDev={isDev}
                      userId={userId}
                      waterMark={waterMark}
                      onSend={onSend}
                      openPage={openPage}
                      openFileViews={openFileViews}
                      showFeedbackModal={showFeedbackModal}
                      // 反馈弹窗自定义配置
                      feedbackModalConfig={feedbackModalConfig}
                      feedbackLabels={feedbackLabels}
                      downloadFileAndView={downloadFileAndView}
                      openRawUrl={openRawUrl}
                      openCallbackUrl={openCallbackUrl}
                      reMeasureSize={reMeasureSize}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </PullToRefresh>
        {showBackBottom && (
          <BackBottom
            count={newCount}
            backBottomButton={backBottomButton}
            onClick={handleBackBottomClick}
            onDidMount={onBackBottomShow}
            {...backBottomButtonProps}
          />
        )}
      </div>
    );
  },
);

// 使用 memo 包装这个组件以避免不必要的渲染
export const MessageContainer = React.memo(MessageContainerC);
