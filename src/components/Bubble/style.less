.Bubble {
  max-width: @bubble-max-width;
  // min-width: 0;
  min-width: 1px; // for IE bug
  background: @bubble-left-bg;
  border-radius: 12px;
  position: relative;

  &.text,
  &.typing {
    min-width: 40px;
    padding: 12px 15px;
    box-sizing: border-box;
    // white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    user-select: text;
    font-size: @bubble-text-font-size;
    line-height: 24px;
  }

  &.image {
    margin-right: 20px;
    padding: 12px 15px;

    img {
      display: block;
      max-width: @bubble-img-max-width;
      max-height: @bubble-img-max-height;
      height: auto;
      border-radius: inherit;
    }
  }

  &.file {
    box-sizing: border-box;
    width: 241px;
    margin-right: 20px;
    padding: 4px 7px;
  }

  &.markdown {
    min-width: 40px;
    // padding: 12px 15px;
    box-sizing: border-box;
    // white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    user-select: text;
    // font-size: 14px;
    border-radius: inherit;
  }
  p {
    margin: 0;
  }

  &-unsend {
    position: absolute;
    top: 50%;
    left: -30px;
    transform: translateY(-10px);

    img {
      width: 20px;
      height: 20px;
    }
  }
}
