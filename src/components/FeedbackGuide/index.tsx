/* eslint-disable @typescript-eslint/no-unused-expressions */
import React from 'react';
import ReactDOM from "react-dom";
import { isMobile } from '../../utils/canUse';
import { FeedbackGuideProps } from './interface';
import defaultDown from './images/default-down.svg';
import defaultUp from './images/default-up.svg';

export const FeedbackGuide = (props: FeedbackGuideProps) => {
  const { onOkBtn, onClose } = props;

  const classPrefix = isMobile ? "FeedbackGuideApp" : "FeedbackGuidePc"

  return (
    <>
      {ReactDOM.createPortal(
        <div className="FeedbackGuideWrap">
          <div className='FeedbackGuideWrap-mask' onClick={onClose}></div>
          <div className={classPrefix}>
            <div className={`${classPrefix}-head`}>
              <div className={`${classPrefix}-title`}>
                点击答案下方的
                <img src={defaultUp} alt="" className={`${classPrefix}-titleUpIcon`} />
                <img src={defaultDown} alt="" className={`${classPrefix}-titleDownIcon`} />
              </div>
              <div className={`${classPrefix}-title`}>
                评价下刚才的回答吧~
              </div>
            </div>

            <div className={`${classPrefix}-content`}>
              您的每一个反馈都是我成长的方向！
            </div>
            <div className={`${classPrefix}-footer`} onClick={onOkBtn}>
              去评价
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
};
