.FeedbackGuideWrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  box-sizing: border-box;

  *,
  :after,
  :before {
    box-sizing: border-box;
  }

  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 12;
  }

  .FeedbackGuideApp {
    position: relative;
    width: 280px;
    height: 216px;
    background: #FFFFFF;
    border-radius: 8px;
    z-index: 13;
    // padding: 24px 20px;

    &-head {
      width: 280px;
      height: 78px;
      background: linear-gradient(180deg, #CFE1F4 0%, #FFFFFF 100%);
      border-radius: 8px 8px 0px 0px;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
      padding: 24px 20px 0 20px;
    }

    &-title {
      font-size: 18px;
      line-height: 24px;

      :nth-child(2) {
        margin-bottom: 0;
      }
    }

    &-content {
      font-size: 12px;
      line-height: 24px;
    }

    &-footer {
      display: flex;
      flex-direction: column;
      align-items: center;
      // width: 100%;
      height: 44px;
      padding: 11px 0;
      background: #3E74F7;
      border-radius: 22px;
      cursor: pointer;
      margin-left: 20px;
      margin-right: 20px;

      font-size: 16px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 22px;
    }
  }

  .FeedbackGuidePc {
    position: relative;
    width: 360px;
    height: 206px;
    background: #FFFFFF;
    border-radius: 2px;
    z-index: 13;
    // padding: 24px 20px 30px 20px;

    &-head {
      width: 360px;
      height: 78px;
      padding: 24px 20px 0 20px;
      background: linear-gradient(180deg, #CFE1F4 0%, #FFFFFF 100%);
      border-radius: 2px 2px 0px 0px;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
    }

    &-footer {
      position: absolute;
      right: 20px;
      bottom: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 80px;
      height: 30px;
      padding: 5px 0;
      background: #108EE9;
      cursor: pointer;

      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 20px;
    }
  }

  .FeedbackGuideApp,
  .FeedbackGuidePc {

    &-title {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      font-size: 20px;
      font-weight: bold;
      color: #4A4A4A;
      line-height: 26px;
      background: linear-gradient(270deg, #48AEFF 0%, #325EC8 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &-titleUpIcon {
      width: 16px;
      height: 16px;
      margin-left: 11px;
    }

    &-titleDownIcon {
      width: 16px;
      height: 16px;
      margin-left: 6px;
    }

    &-content {
      display: inline-block;
      margin-top: 17px;
      margin-left: 20px;
      margin-bottom: 30px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 24px;
    }

  }

  @media (min-width: 1919px) {
    .FeedbackTipApp {
      width: 460px;
    }
  }

  @media (min-height: 1079px) {
    .FeedbackTipApp {
      height: 406px;
    }
  }

  /* 整个滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    /* 垂直滚动条宽度 */
    height: 6px;
    /* 水平滚动条高度 */
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: transparent;
    /* 轨道背景色 */
    // border-radius: 6px;
    /* 轨道圆角 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #E5E7EE;
    /* 滑块背景色 */
    border-radius: 3px;
    /* 滑块圆角 */
    border: 3px solid #f1f1f1;
    /* 滑块边框 */
    width: 6px;
    height: 45px;
    background: #E5E7EE;
    border-radius: 3px;
  }

  /* 滚动条滑块悬停效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
    /* 悬停时滑块背景色 */
  }
}
