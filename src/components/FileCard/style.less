.FileCard {
  // width: 241px;
  // padding: 12px 15px;
  box-sizing: border-box;
  width: 100%;
  max-width: @card-size-xl;
  padding: 8px;
  background: #f7f7f7;
  border-radius: 12px;
}

.FileCard-icon {
  position: relative;
  width: 31px;
  height: 38px;
  // height: 60px;
  margin-left: 15px;
  color: @gray-2;

  &[data-type='pdf'] {
    color: @red;
  }
  &[data-type*='doc'] {
    color: @blue;
  }
  &[data-type*='ppt'],
  &[data-type='key'] {
    color: @orange;
  }
  &[data-type*='xls'] {
    color: @green;
  }
  &[data-type='rar'],
  &[data-type='zip'] {
    color: @brand-1;
  }
  .Icon {
    font-size: 60px;
  }
}

.fileIcon {
  width: 31px;
  height: 38px;
}

.FileCard-name {
  margin-bottom: 4px;
  // width: 111px;
  width: 90%;
  height: 38px;
  font-size: 14px;
  color: #333;
  line-height: 19px;
}

.FileCard-ext {
  position: absolute;
  left: 20px;
  bottom: 15px;
  transform-origin: left bottom;
  transform: scale(0.5);
  max-width: 50px;
  font-size: @font-size-md;
  font-weight: 700;
  text-transform: uppercase;
}

.FileCard-meta {
  width: 90%;
  color: @gray-3;
  font-size: 12px;
  color: #999;
  line-height: 16px;

  & > a,
  & > span {
    margin-right: 10px;
  }
  a {
    color: @link-color;
    text-decoration: none;
  }
}
