.Composer {
  display: flex;
  padding: 12px;
  position: relative;
  background-color: @composer-compact-bg;
  border-radius: @composer-compact-border-radius;
  box-shadow: @composer-compact-box-shadow;
  border-width: @composer-compact-border-width;
  border-color: @composer-compact-border-color;

  // margin: 2px;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: -2px;
  //   right: -2px;
  //   bottom: -2px;
  //   left: -2px;
  //   border-radius: 12px;
  //   background: linear-gradient(90deg, #5c38e5, #0076ff);
  //   z-index: -1;
  // }

  // >div+div {
  //   margin-left: 9px;
  // }

  &-inputWrap {
    flex: 1;
    position: relative;
  }

  &-actions {
    cursor: pointer;
  }

  &-input {
    overflow-y: auto;
    max-height: @composer-compact-input-max-height;
    padding: 0;
    border: @composer-compact-input-border;
    border-radius: 0;
    background: @composer-compact-input-bg;
    // line-height: 20px;
    font-size: 15px;
    caret-color: #3e74f7;
    transition: @composer-compact-input-transition;
    color: @composer-compact-input-color;
  }

  &-col-layout {
    flex-direction: column;
  }

  &-inputTypeBtn {
    margin-right: 8px;
  }

  &-line {
    display: flex;
    flex: 1;
  }

  &-up-pad {
    padding-top: 26px;
  }

  &-SendIconWrap {
    position: relative;
    height: 24px;
  }

  &-SendIcon {
    width: 24px;
    height: 24px;
    position: relative;
  }

  /* iPhone XR型号在欢迎页键盘收起点击的时候，点击区域疑似偏移，放大点击区域 */
  &-SendIconClick {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    /* 透明但可点击 */
    pointer-events: auto;
    // background-color: pink;
  }

  &-StopAnswerIcon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid @composer-compact-send-color;
    display: flex;
    align-items: center;
    justify-content: center;

    &-inner {
      width: 8px;
      height: 8px;
      background: @composer-compact-send-color;
      border-radius: 2px;
    }
  }
}

.Composer-toggleBtn {
  transition: transform 0.2s;

  &.active .Icon {
    transform: rotate(45deg);
  }
}

.Composer-sendWrap {
  display: flex;
  justify-content: space-between;
  margin-left: 4px;
  align-items: center;

  .InternetSearch {
    height: 30px;
    background: @composer-compact-net-search-bg;
    border-radius: 15px;
    font-size: 14px;
    color: @composer-compact-net-search-color;
    line-height: 30px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border: 1px solid transparent;
    margin-right: 8px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 3px;
    }
  }

  .InternetSearch-active {
    background: @composer-compact-net-search-active-bg;
    color: @composer-compact-net-search-active-color;
    border: 1px solid @border-color;
  }

  &-selectedAgents {
    display: inline-flex;
  }
}
