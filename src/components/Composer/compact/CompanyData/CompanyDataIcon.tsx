import React from 'react';

export default function CompanyDataIcon() {
  return (
    <svg version="1.1" x="0px" y="0px" viewBox="0 0 16 16" xmlSpace="preserve" className="Composer-CompanyData-icon">
      <rect id="矩形_00000100347586473384276570000009844639221300192173_" x="6.076" y="1.712" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -0.2282 6.7219)" className="Composer-CompanyData-icon-stroke" width="3.848" height="3.848"/>
      <rect id="矩形备份-4" x="1.504" y="5.591" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -4.3099 4.6254)" className="Composer-CompanyData-icon-stroke" width="3.848" height="3.848"/>
      <line id="路径-9备份" className="Composer-CompanyData-icon-stroke" x1="3.428" y1="10.004" x2="3.428" y2="15"/>
      <rect id="矩形备份-6" x="10.504" y="5.591" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -1.6738 10.9893)" className="Composer-CompanyData-icon-stroke" width="3.848" height="3.848"/>
      <line id="路径-9备份-2" className="Composer-CompanyData-icon-stroke" x1="12.428" y1="10.004" x2="12.428" y2="15"/>
      <line id="路径-9" className="Composer-CompanyData-icon-stroke" x1="8" y1="5.984" x2="8" y2="15"/>
    </svg>
  );
}