.Composer-CompanyData {
  height: 30px;
  background: @composer-compact-net-search-bg;
  border-radius: 15px;
  font-size: 14px;
  color: @composer-compact-net-search-color;
  line-height: 30px;
  padding-left: 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border: none;
  position: relative;
  border: 1px solid transparent;

  &-icon-stroke {
    stroke: @composer-compact-net-search-color;
    fill: none;
  }

  &-label {
    padding-right: 5px;
    border-right: 1px solid #e0e0e0;
  }

  &-active {
    background: @composer-compact-net-search-active-bg;
    color: @composer-compact-net-search-active-color;
    border: 1px solid @border-color;

    .Composer-CompanyData-icon-stroke {
      stroke: @composer-compact-net-search-active-color;
      fill: none;
    }

    .Composer-CompanyData-label {
      border-right: 1px solid @border-color;
    }
  }

  &-Popover {
    border-radius: 8px;

    .WideComposer-tooltip-inner {
      padding: 15px 0;
      box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.3);
      border-radius: 8px;
      border: none;
    }
  }

  &-PopoverContent {
    &-header {
      padding: 0 15px;
      font-size: 11px;
      color: @gray-3;
      line-height: 18px;
      font-weight: bold;
    }

    &-item {
      margin: 0 15px;
      width: 180px;
      font-size: 14px;
      font-weight: 500;
      color: @gray-1;
      line-height: 20px;
      padding-top: 10px;
      padding-bottom: 10px;
      cursor: pointer;

      &-title {
        font-size: 14px;
        color: @gray-1;
        line-height: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
      }

      &-value {
        font-size: 11px;
        color: @gray-2;
        line-height: 18px;
      }

      &:nth-child(2) {
        border-bottom: 1px solid #e0e0e0;
      }
    }

    &-active {
      .Composer-CompanyData-PopoverContent-item-title {
        color: @composer-compact-net-search-active-color;
      }
    }

    &-disabled {
      cursor: default;
    }

    &-footer {
      color: @gray-3;
      line-height: 18px;
      font-size: 11px;
      padding: 10px 15px 0;
      border-top: 1px solid #e0e0e0;
    }
  }

  &-icon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
  }

  &-trigger {
    padding: 0 8px 0 5px;
    display: flex;
    align-items: center;
  }
  
  &-arrowIcon {
    width: 12px;
    height: 12px;
  }

  &-rotateIcon {
    transform: rotate(180deg);
  }

  &-selectedIcon {
    width: 14px;
    height: 14px;
  }
}