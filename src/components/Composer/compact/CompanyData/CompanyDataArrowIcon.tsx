import React from 'react';

type CompanyDataArrowIconProps = {
  className?: string;
};

export default function CompanyDataArrowIcon(props: CompanyDataArrowIconProps) {
  return (
    <svg
      version="1.1"
      x="0px"
      y="0px"
      viewBox="0 0 12 12"
      className={`Composer-CompanyData-arrowIcon ${props?.className}`}
    >
      <polyline id="Path-13" className="Composer-CompanyData-icon-stroke" points="9,4.5 6,7.5 3,4.5 "/>
    </svg>
  );
}