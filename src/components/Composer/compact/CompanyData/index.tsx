import React, { useState } from 'react';
import clsx from 'clsx';

import { LogPointConfigMap, ILogParams } from '../../../../LogPointConfigMap';
import { CompanyDataConfigProps } from '../../index';
import Popover from '../../wide/Popover';
import CompanyDataIcon from './CompanyDataIcon';
import CompanyDataArrowIcon from './CompanyDataArrowIcon';
import SelectedIcon from './images/selected.png';

// agent选择项
const options = [
  {
    value: 'yewen',
    label: '包含OA知识库、信用业务知识库、 专业交易知识库',
    title: '业务知识库',
  },
  {
    value: 'ruiping',
    label: '包含研究所生产资讯、省心研究院资讯、公司采购新闻及公告等',
    title: '公司资讯研报（建设中）',
  },
];

// 有睿评灰度则睿评的去掉建设中
const optionsRuipingGrey = [
  {
    value: 'yewen',
    label: '包含OA知识库、信用业务知识库、 专业交易知识库',
    title: '业务知识库',
  },
  {
    value: 'ruiping',
    label: '包含研究所生产资讯、省心研究院资讯、公司采购新闻及公告等',
    title: '公司资讯研报',
  },
]

export type OptionProps = {
  value: string;
  label: string;
  title: string;
};

export type CompanyDataProps = {
  selectedAgents: string[] | [];
  onCompanyDataSelectAgentsChange?: (value: string[]) => void;
  companyDataConfig: CompanyDataConfigProps;
  // log上报回调
  onReportLog?: (params: ILogParams | undefined) => void;
};

export const CompanyData: React.FC<CompanyDataProps> = (props) => {
  const {
    selectedAgents = [],
    companyDataConfig,
    onCompanyDataSelectAgentsChange,
    onReportLog,
  } = props;
  const [open, setOpen] = useState<boolean>(companyDataConfig?.defaultOpen || false);

  const disabled = !companyDataConfig?.hasGrey;
  const hasRuipingGrey = companyDataConfig?.hasRuipingGrey;
  const agentsOptions = hasRuipingGrey ? optionsRuipingGrey : options;

  const handleCompanyDataInnerClickLog = (newSelectedAgents: string[]) => {
    const value = JSON.stringify(newSelectedAgents);
    // 埋点上报
    const params = LogPointConfigMap.get('companyDataInnerClick');
    const newParams: any = {
      ...params,
      btn_title: {
        ...params?.btn_title,
        value,
      }
    };
    onReportLog?.(newParams);
  }

  const handlePopoverItemClick = (item: OptionProps) => {
    if (disabled) {
      return;
    }
    let newSelectedAgents = [];
    if (selectedAgents?.includes(item?.value)) {
      newSelectedAgents = selectedAgents?.filter(i => i !== item?.value);
    } else {
      newSelectedAgents = [...selectedAgents, item?.value];
    }
    handleCompanyDataInnerClickLog(newSelectedAgents);
    if (onCompanyDataSelectAgentsChange) {
      onCompanyDataSelectAgentsChange(newSelectedAgents);
    }
  };

  const handleOpenChange = (visible: boolean) => {
    if (visible) {
      // 埋点上报
      handleCompanyDataInnerClickLog(selectedAgents);
    }
    setOpen(visible)
  };

  const popoverContent = (
    <div className="Composer-CompanyData-PopoverContent">
      <div className="Composer-CompanyData-PopoverContent-header">已接入数据范围</div>
      {agentsOptions?.map((item: OptionProps) => {
        const selected = !disabled && selectedAgents?.includes(item?.value);
        return (
          <div
            key={item?.value}
            className={clsx(
              "Composer-CompanyData-PopoverContent-item",
              {
                "Composer-CompanyData-PopoverContent-active": selected,
                "Composer-CompanyData-PopoverContent-disabled": disabled,
              }
            )}
            onClick={() => handlePopoverItemClick(item)}
          >
            <div className="Composer-CompanyData-PopoverContent-item-title">
              {item?.title}
              {
                selected ? (
                  <img src={SelectedIcon} className="Composer-CompanyData-selectedIcon" />
                ) : null
              }
            </div>
            <div className="Composer-CompanyData-PopoverContent-item-value">
              {item?.label}
            </div>
          </div>
        )
      })}
      <div className='Composer-CompanyData-PopoverContent-footer'>更多公司数据，持续接入中…</div>
    </div>
  );

  const handleCompanyDataClickLog = (newSelectedAgents: string[]) => {
    // 埋点上报
    const params = LogPointConfigMap.get('companyDataClick');
    const label = newSelectedAgents?.length <= 0 ? '关闭内部数据' : '查看内部数据';
    const btnTitle = params?.btn_title;
    const newParams: any = {
      ...params,
      btn_title: {
        ...btnTitle,
        btn_label: `${btnTitle?.btn_label}${label}`,
        value: JSON.stringify(newSelectedAgents),
      }
    };
    onReportLog?.(newParams);
  };

  const handleCompanyDataClick = () => {
    const defaultSelectedAgents = companyDataConfig?.selectedAgents && companyDataConfig?.selectedAgents?.length > 0
      ? companyDataConfig?.selectedAgents : ['yewen', 'ruiping']
    const newSelectedAgents = selectedAgents?.length > 0
      ? [] : defaultSelectedAgents;
    handleCompanyDataClickLog(newSelectedAgents);
    if (onCompanyDataSelectAgentsChange) {
      onCompanyDataSelectAgentsChange(newSelectedAgents);
    }
  };

  const handleTriggerClick = (e: any) => {
    // 阻止事件冒泡
    e.stopPropagation();
  };

  return (
    <div
      className={clsx(
        'Composer-CompanyData',
        {
          // 'Composer-CompanyData-active': open || selectedAgents?.length > 0
          'Composer-CompanyData-active': selectedAgents?.length > 0
        },
      )}
      onClick={handleCompanyDataClick}
    >
      <CompanyDataIcon />
      <span className="Composer-CompanyData-label">公司数据</span>
      <Popover
        placement="topLeft"
        content={popoverContent}
        overlayClassName="Composer-CompanyData-Popover"
        onOpenChange={handleOpenChange}
        open={open}
        trigger="click"
        align={{
          offset: [-92, -10],
        }}
      >
        <div className="Composer-CompanyData-trigger" onClick={handleTriggerClick}>
          <CompanyDataArrowIcon
            className={open ? "Composer-CompanyData-rotateIcon" : ''}
          />
        </div>
      </Popover>
    </div>
  )
}