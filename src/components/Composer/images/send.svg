<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>send--alt--filled@2x</title>
    <defs>
        <linearGradient x1="48.2335125%" y1="123.94399%" x2="55.2714984%" y2="-33.456488%" id="linearGradient-1">
            <stop stop-color="#54B6FF" offset="0%"></stop>
            <stop stop-color="#0080FF" offset="46.2362543%"></stop>
            <stop stop-color="#CD8EED" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="APP-智能助手-生成中2" transform="translate(-321.000000, -718.000000)">
            <g id="编组-11" transform="translate(0.000000, 651.000000)">
                <g id="编组" transform="translate(15.000000, 54.000000)">
                    <g id="send--alt--filled" transform="translate(306.000000, 13.000000)">
                        <rect id="_Transparent_Rectangle_" x="0" y="0" width="24" height="24"></rect>
                        <path d="M20.7825,3.2175 C20.5758461,3.01181466 20.2687147,2.94453825 19.995,3.045 L3.495,9.045 C3.20365396,9.15550824 3.01098578,9.43464996 3.01098578,9.74625 C3.01098578,10.05785 3.20365396,10.3369918 3.495,10.4475 L9.9375,13.02 L14.6925,8.25 L15.75,9.3075 L10.9725,14.085 L13.5525,20.5275 C13.6662205,20.8130408 13.9426471,21.0002975 14.25,21.0000004 L14.25,21.0000004 C14.5602574,20.993638 14.8345555,20.7968589 14.94,20.505 L20.94,4.005 C21.0443046,3.73413812 20.9829589,3.42740918 20.7825,3.2175 Z" id="路径" fill="url(#linearGradient-1)"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>