import React, { useState, useRef, useEffect, useImperativeHandle, useCallback } from 'react';
import clsx from 'clsx';
import Select, { type SelectProps, Option as SelectOption } from './wide/Select';
import { Recorder, RecorderProps } from '../Recorder';
import { Toolbar, ToolbarItemProps } from '../Toolbar';
import { AccessoryWrap } from './AccessoryWrap';
import { InputProps } from '../Input';
// import { Popover } from '../Popover';
// import { ToolbarItem } from './ToolbarItem';
import { ComposerInput } from './ComposerInput';
import { SendButton } from './compact/SendButton';
import { SendButton as WideSendButton } from './wide/SendButton';
import { Action } from './Action';
import toggleClass from '../../utils/toggleClass';
import { isMobile } from '../../utils/canUse';
import NetSearchIcon from './NetSearchIcon';
import AttachmentIcon from './AttachmentIcon';
import { Icon } from '../Icon';
import Upload, { UploadFile, UploadProps } from './wide/Upload';
import Popover from './wide/Popover';
import { CompanyData } from './compact/CompanyData';
import { ILogParams } from '../../LogPointConfigMap';

export const CLASS_NAME_FOCUSING = 'S--focusing';

export * from './wide/Upload';

export type InputType = 'voice' | 'text';

export type ComposerProps = {
  // wideBreakpoint?: string;
  isWide?: boolean;
  text?: string;
  inputOptions?: InputProps;
  placeholder?: string;
  inputType?: InputType;
  onInputTypeChange?: (inputType: InputType) => void;
  recorder?: RecorderProps;
  onSend?: (type: string, content: string, payload?: object, transformedFiles?: UploadFile[]) => Promise<boolean>;
  onImageSend?: (file: File) => Promise<any>;
  onFocus?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  onChange?: (value: string, event: React.ChangeEvent<Element>) => void;
  onBlur?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  toolbar?: ToolbarItemProps[];
  onToolbarClick?: (item: ToolbarItemProps, event: React.MouseEvent) => void;
  onAccessoryToggle?: (isAccessoryOpen: boolean) => void;
  extraAction?: React.ReactNode;
  showStopAnswer?: boolean;
  onStopAnswer?: () => void;
  showInternetSearch?: boolean;
  enableInternetSearch?: boolean;
  toggleEnableInternetSearch?: (enable: boolean) => void;
  aboveNode?: React.ReactNode;
  belowNode?: React.ReactNode;

  llmConfig?: {
    selectProps?: Omit<SelectProps, 'onChange'>;
    llmOptions?: { value: string | number; label: string }[];
    onLLMSwitch?: (value: string) => void;
  };
  uploadConfig?: UploadProps;
  // todo
  showPictureUpload?: boolean;
  pictureConfig?: any;
  // 公司数据agent切换
  onCompanyDataSelectAgentsChange?: (value: string[]) => void;
  // 公司数据选择的agent
  selectedAgents?: string[];
  // 公司数据配置项
  companyDataConfig?: any;
  // log上报回调
  onReportLog?: (params: ILogParams | undefined) => void;
};

export type CompanyDataConfigProps = {
  hasGrey?: boolean;
  hasRuipingGrey?: boolean;
  defaultOpen?: boolean;
  selectedAgents?: string[];
};

export type ComposerConfigProps = {
  uploadConfig?: UploadProps;
  showInternetSearch?: boolean;
  companyDataConfig?: CompanyDataConfigProps;
}

export interface ComposerHandle {
  setText: (text: string) => void;
}

const ComposerC = React.forwardRef<ComposerHandle, ComposerProps>((props, ref) => {
  const {
    text: initialText = '',
    inputType: initialInputType = 'text',
    // wideBreakpoint,
    placeholder = '请输入您的问题',
    recorder = {},
    onInputTypeChange,
    onFocus,
    onBlur,
    onChange,
    onSend,
    onImageSend,
    onAccessoryToggle,
    toolbar = [],
    onToolbarClick,
    extraAction,
    inputOptions,
    showStopAnswer,
    onStopAnswer,
    aboveNode,
    belowNode,
    llmConfig,
    uploadConfig,
    showInternetSearch = false,
    enableInternetSearch = true,
    toggleEnableInternetSearch,
    isWide = false,
    onCompanyDataSelectAgentsChange,
    selectedAgents = [],
    companyDataConfig,
    onReportLog,
  } = props;

  const [text, setText] = useState(initialText);
  const [inputType, setInputType] = useState(initialInputType || 'text');
  const [isAccessoryOpen, setAccessoryOpen] = useState(false);
  const [accessoryContent, setAccessoryContent] = useState('');
  const inputRef = useRef<HTMLTextAreaElement>(null!);
  const focused = useRef(false);
  const blurTimer = useRef<any>();
  const popoverTarget = useRef<any>();
  const isMountRef = useRef(false);

  const showLLMSwitch = !!llmConfig;
  const showUploadAttachment = !!uploadConfig;
  const showCompanyData = !!companyDataConfig;
  const isShowExtraAction = showInternetSearch || showLLMSwitch || showCompanyData;

  const [fileList, setFileList] = useState<UploadFile[]>([]); //文件列表

  const prefixCls = 'WideComposer';

  // useEffect(() => {
  // const mq =
  //   wideBreakpoint && window.matchMedia
  //     ? window.matchMedia(`(min-width: ${wideBreakpoint})`)
  //     : false;
  // function handleMq(e: MediaQueryListEvent) {
  //   setWide(e.matches);
  // }
  // setWide(mq && mq.matches);
  // if (mq) {
  //   mq.addListener(handleMq);
  // }
  // return () => {
  //   if (mq) {
  //     mq.removeListener(handleMq);
  //   }
  // };
  // }, [wideBreakpoint]);

  const fileRenderPortal = useRef<HTMLDivElement>(null);

  useEffect(() => {
    toggleClass('S--wide', isWide);
    if (!isWide) {
      setAccessoryContent('');
    }
  }, [isWide]);

  useEffect(() => {
    if (isMountRef.current && onAccessoryToggle) {
      onAccessoryToggle(isAccessoryOpen);
    }
  }, [isAccessoryOpen, onAccessoryToggle]);

  useEffect(() => {
    isMountRef.current = true;
  }, []);

  useImperativeHandle(ref, () => ({
    setText,
  }));

  const handleInputTypeChange = useCallback(() => {
    const isVoice = inputType === 'voice';
    const nextType = isVoice ? 'text' : 'voice';
    setInputType(nextType);

    if (isVoice) {
      const input = inputRef.current;
      input.focus();
      // eslint-disable-next-line no-multi-assign
      input.selectionStart = input.selectionEnd = input.value.length;
    }
    if (onInputTypeChange) {
      onInputTypeChange(nextType);
    }
  }, [inputType, onInputTypeChange]);

  const handleInputFocus = useCallback(
    (e: React.FocusEvent<HTMLTextAreaElement>) => {
      clearTimeout(blurTimer.current);
      if (isMobile) {
        toggleClass(CLASS_NAME_FOCUSING, true);
      }

      focused.current = true;

      if (onFocus) {
        onFocus(e);
      }
    },
    [onFocus],
  );

  const handleInputBlur = useCallback(
    (e: React.FocusEvent<HTMLTextAreaElement>) => {
      blurTimer.current = setTimeout(() => {
        if (isMobile) {
          toggleClass(CLASS_NAME_FOCUSING, false);
        }
        focused.current = false;
      }, 0);

      if (onBlur) {
        onBlur(e);
      }
    },
    [onBlur],
  );

  const innerSend = useCallback(() => {
    if (onSend) {
      // 使用自定义转换处理文件列表
      const responseTransform = props.uploadConfig?.responseTransform;
      const transformedFiles = fileList.map((file) => {
        return {
          ...file,
          path: responseTransform?.(file.response, file) || file.response?.resultData?.Path,
        };
      });
      onSend('text', text, {}, transformedFiles).then((sendSuccess) => {
        // 发送成功才清空输入框
        if (sendSuccess) {
          setText('');
          setFileList([]);
        }
      });
    }
    if (focused.current) {
      inputRef.current.focus();
    }
  }, [onSend, text, fileList, props.uploadConfig?.responseTransform]);

  const handleInputKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (!e.shiftKey && e.keyCode === 13) {
        if (!showStopAnswer) {
          innerSend();
        }
        e.preventDefault();
      }
    },
    [innerSend, showStopAnswer],
  );

  const handleTextChange = useCallback(
    (value: string, e: React.ChangeEvent) => {
      setText(value);

      if (onChange) {
        onChange(value, e);
      }
    },
    [onChange],
  );

  const handleAccessoryToggle = useCallback(() => {
    setAccessoryOpen(!isAccessoryOpen);
  }, [isAccessoryOpen]);

  const handleAccessoryBlur = useCallback(() => {
    setTimeout(() => {
      setAccessoryOpen(false);
      setAccessoryContent('');
    });
  }, []);

  const handleToolbarClick = useCallback(
    (item: ToolbarItemProps, e: React.MouseEvent) => {
      if (onToolbarClick) {
        onToolbarClick(item, e);
      }
      if (item.render) {
        popoverTarget.current = e.currentTarget;
        setAccessoryContent(item.render);
      }
    },
    [onToolbarClick],
  );

  const onLlmChange = useCallback(
    (value: string) => {
      if (llmConfig?.onLLMSwitch) {
        llmConfig.onLLMSwitch(value);
      }
    },
    [llmConfig],
  );

  const isInputText = inputType === 'text';
  const inputTypeIcon = isInputText ? 'volume-circle' : 'keyboard-circle';
  const hasToolbar = toolbar.length > 0;

  const inputProps = {
    ...inputOptions,
    value: text,
    inputRef,
    placeholder: ` ${placeholder}`,//IOS下光标会覆盖文案，，所以在前面拼个空格，增加点间距
    onFocus: handleInputFocus,
    onBlur: handleInputBlur,
    onKeyDown: handleInputKeyDown,
    onChange: handleTextChange,
    onImageSend,
  };

  // 宽屏
  if (isWide) {
    return (
      <>
        {aboveNode}
        <div className="WideComposer" id="AiChatComposer">
          <div ref={fileRenderPortal}></div>
          <div className="WideComposer-inputWrap">
            <ComposerInput invisible={false} {...inputProps} />
          </div>
          <div className="WideComposer-actions">
            <div className="WideComposer-left">
              <div className="WideComposer-actions">
                {showLLMSwitch && (
                  <Select
                    placeholder="请选择模型"
                    suffixIcon={<Icon type={'chevron-down'} />}
                    {...llmConfig?.selectProps}
                    prefixCls={`${prefixCls}-select`}
                    onChange={onLlmChange}
                  >
                    {llmConfig?.llmOptions?.map((option) => (
                      <SelectOption key={option.value} value={option.value}>
                        {option.label}
                      </SelectOption>
                    ))}
                  </Select>
                )}
                {showInternetSearch && (
                  <div
                    className={`WideComposer-InternetSearch ${enableInternetSearch ? 'WideComposer-InternetSearch-active' : ''
                      }`}
                    onClick={() => toggleEnableInternetSearch?.(!enableInternetSearch)}
                  >
                    <NetSearchIcon />
                    联网搜索
                  </div>
                )}
              </div>
              {extraAction}
            </div>
            <div className="WideComposer-sendWrap">
              {showUploadAttachment && (
                <Upload
                  prefixCls={`${prefixCls}-attachments`}
                  {...uploadConfig}
                  fileRenderPortal={fileRenderPortal}
                  fileList={fileList}
                  onChange={({ fileList: newList }) => setFileList(newList)}
                >
                  {uploadConfig.popoverConfig ? (
                    <Popover prefixCls={`${prefixCls}-tooltip`} {...uploadConfig.popoverConfig}>
                      <span>
                        <AttachmentIcon />
                      </span>
                    </Popover>
                  ) : (
                    <AttachmentIcon />
                  )}
                </Upload>
              )}
              <WideSendButton
                onSendClick={(e: React.MouseEvent<HTMLDivElement>) => {
                  e.preventDefault();
                  e.stopPropagation();
                  innerSend();
                }}
                onStopClick={onStopAnswer}
                showStop={showStopAnswer}
                disabled={!text}
              />
            </div>
          </div>
        </div>
        {belowNode}
      </>
    );
  }

  // 窄屏
  return (
    <>
      {aboveNode}
      <div
        className={`Composer ${isShowExtraAction ? 'Composer-col-layout' : null}`}
        id="AiChatComposer"
      >
        <div className="Composer-line">
          {recorder.canRecord && (
            <Action
              className="Composer-inputTypeBtn"
              data-icon={inputTypeIcon}
              icon={inputTypeIcon}
              onClick={handleInputTypeChange}
              aria-label={isInputText ? '切换到语音输入' : '切换到键盘输入'}
            />
          )}
          <div className="Composer-inputWrap">
            <ComposerInput invisible={!isInputText} {...inputProps} />
            {!isInputText && <Recorder {...recorder} />}
          </div>
        </div>
        <div className={`Composer-sendWrap ${isShowExtraAction ? 'Composer-up-pad' : ''}`}>
          {hasToolbar && (
            <Action
              className={clsx('Composer-toggleBtn', {
                active: isAccessoryOpen,
              })}
              icon="plus-circle"
              onClick={handleAccessoryToggle}
              aria-label={isAccessoryOpen ? '关闭工具栏' : '展开工具栏'}
            />
          )}
          {/* {showLLMSwitch && (
            <div className="LLMSwitch">
              <span>GPT</span>
              <span>GPT</span>
            </div>
          )} */}
          <div className="Composer-sendWrap-selectedAgents">
            {showInternetSearch && (
              <div
                className={`InternetSearch ${enableInternetSearch ? 'InternetSearch-active' : ''}`}
                onClick={() => toggleEnableInternetSearch?.(!enableInternetSearch)}
              >
                <NetSearchIcon />
                联网搜索
              </div>
            )}
            {
              showCompanyData && (
                <CompanyData
                  onCompanyDataSelectAgentsChange={onCompanyDataSelectAgentsChange}
                  selectedAgents={selectedAgents}
                  companyDataConfig={companyDataConfig}
                  onReportLog={onReportLog}
                />
              )
            }
          </div>
          {extraAction}
          <SendButton
            onSendClick={(e: React.MouseEvent<HTMLDivElement>) => {
              e.preventDefault();
              e.stopPropagation();
              innerSend();
            }}
            onStopClick={onStopAnswer}
            showLoading={showStopAnswer}
            disabled={!text}
          />
        </div>
      </div>
      {belowNode}
      {isAccessoryOpen && (
        <AccessoryWrap onClickOutside={handleAccessoryBlur}>
          {accessoryContent || <Toolbar items={toolbar} onClick={handleToolbarClick} />}
        </AccessoryWrap>
      )}
    </>
  );
});

// 使用 memo 包装这个组件以避免不必要的渲染
export const Composer = React.memo(ComposerC);
