import React from 'react';
import ReactDOM, { flushSync } from 'react-dom';
import RcUpload from 'rc-upload';
import useMergedState from 'rc-util/lib/hooks/useMergedState';

import type {
  RcFile as OriRcFile,
  UploadProps as RcUploadProps,
  UploadRequestOption as RcCustomRequestOptions,
} from 'rc-upload/lib/interface';
import clsx from 'clsx';
import { file2Obj, getFileItem, removeFileItem, updateFileList } from './utils';
import AttachmentCard from './AttachmentCard';
import { PopoverProps } from './Popover';

export type UploadType = 'drag' | 'select';

export type UploadFileStatus = 'error' | 'success' | 'done' | 'uploading' | 'removed';

export interface RcFile extends OriRcFile {}

export interface CustomRequestOptions extends RcCustomRequestOptions {}

export type ItemRender<T = any> = (
  originNode: React.ReactElement,
  file: UploadFile,
  fileList: Array<UploadFile<T>>,
  actions: {
    download: () => void;
    preview: () => void;
    remove: () => void;
  },
) => React.ReactNode;

export interface UploadFile<T = any> {
  uid: string;
  size?: number;
  name: string;
  fileName?: string;
  lastModified?: number;
  lastModifiedDate?: Date;
  url?: string;
  status?: UploadFileStatus;
  percent?: number;
  thumbUrl?: string;
  crossOrigin?: React.ImgHTMLAttributes<HTMLImageElement>['crossOrigin'];
  originFileObj?: RcFile;
  response?: T;
  error?: any;
  linkProps?: any;
  type?: string;
  xhr?: T;
  preview?: string;
}
type BeforeUploadValueType = void | boolean | string | Blob | File;

export interface UploadChangeParam<T = UploadFile> {
  file: T;
  fileList: T[];
  event?: { percent: number };
}

export interface UploadProps<T = any> extends Pick<RcUploadProps, 'capture'> {
  name?: string;
  fileList?: Array<UploadFile<T>>;
  defaultFileList?: Array<UploadFile<T>>;
  action?: string | ((file: RcFile) => string) | ((file: RcFile) => PromiseLike<string>);
  directory?: boolean;
  data?:
    | Record<string, unknown>
    | ((file: UploadFile<T>) => Record<string, unknown> | Promise<Record<string, unknown>>);
  // method?: 'POST' | 'PUT' | 'PATCH' | 'post' | 'put' | 'patch';
  headers?: Record<string, string>;
  multiple?: boolean;
  accept?: string;
  beforeUpload?: (
    file: RcFile,
    FileList: RcFile[],
  ) => BeforeUploadValueType | Promise<BeforeUploadValueType>;
  onChange?: (info: UploadChangeParam<UploadFile<T>>) => void;
  className?: string;
  //   onDrop?: (event: React.DragEvent<HTMLDivElement>) => void;
  //   onDownload?: (file: UploadFile<T>) => void;
  onPreview?: (file: UploadFile<T>) => void;
  onRemove?: (file: UploadFile<T>) => void | boolean | Promise<void | boolean>;
  disabled?: boolean;
  prefixCls?: string;
  withCredentials?: boolean;
  customRequest?: (options: RcCustomRequestOptions) => void;
  responseTransform?: (response: T, file: UploadFile<T>) => any;
  // openFileDialogOnClick?: boolean;
  id?: string;
  iconRender?: (file: UploadFile<T>) => React.ReactNode;
  //   locale?: UploadLocale;
  //   previewFile?: PreviewFileHandler;
  //   isImageUrl?: (file: UploadFile) => boolean;
  //   progress?: UploadListProgressProps;
  //   itemRender?: ItemRender<T>;
  /** Config max count of `fileList`. Will replace current one when `maxCount` is 1 */
  maxCount?: number;
  popoverConfig?: {
    content?: PopoverProps['content'];
    className?: string;
    overlayClassName?: string;
    placement?: PopoverProps['placement'];
    autoAdjustOverflow?: boolean;
    getPopupContainer?: PopoverProps['getPopupContainer'];
    zIndex?: number;
    mouseEnterDelay?: number;
    mouseLeaveDelay?: number;
    trigger?: PopoverProps['trigger'];
  };
  children?: React.ReactNode;
  fileRenderPortal: React.RefObject<any>; // 上传文件列表的渲染容器
  padUrlPrefix?: (uploadPath: string)=> string; // 如果后端未返回url，需要拼接前缀的情况下使用
}

const Upload: React.FC<UploadProps> = (props) => {
  const {
    fileList,
    defaultFileList,
    onRemove,
    onPreview,
    onChange,
    // onDownload,
    // onDrop,
    // previewFile,
    // isImageUrl,
    // itemRender,
    disabled,
    iconRender,
    prefixCls = 'WideComposer-attachments',
    className,
    maxCount,
    data = {},
    multiple = false,
    action = '',
    accept = '',
    fileRenderPortal,
  } = props;

  const uploadButtonCls = clsx({
    [`${prefixCls}-disabled`]: disabled,
  });

  const [mergedFileList, setMergedFileList] = useMergedState(defaultFileList || [], {
    value: fileList,
    postState: (list) => list ?? [],
  });

  const upload = React.useRef<RcUpload>(null);
  const [isPassBeforeLoad,setIsPassBeforeLoad] = React.useState(true);

  const onInternalChange = (
    file: UploadFile,
    changedFileList: UploadFile[],
    event?: { percent: number },
  ) => {
    if(!isPassBeforeLoad) return;
    let cloneList = [...changedFileList];
    // Cut to match count
    if (maxCount === 1) {
      cloneList = cloneList.slice(-1);
    } else if (maxCount) {
      cloneList = cloneList.slice(0, maxCount);
    }
    // Prevent React18 auto batch since input[upload] trigger process at same time
    // which makes fileList closure problem
    flushSync(() => {
      setMergedFileList(cloneList);
    });
    const changeInfo: UploadChangeParam<UploadFile> = {
      file: file as UploadFile,
      fileList: cloneList,
    };
    if (event) {
      changeInfo.event = event;
    }
    flushSync(() => {
      onChange?.(changeInfo);
    });
  };

  const onBatchStart: RcUploadProps['onBatchStart'] = (batchFileInfoList) => {
    // Skip file which marked as `LIST_IGNORE`, these file will not add to file list
    const filteredFileInfoList = batchFileInfoList; /* .filter(
      (info) => !(info.file as any)[LIST_IGNORE],
    ); */

    // Nothing to do since no file need upload
    if (!filteredFileInfoList.length) {
      return;
    }

    const objectFileList = filteredFileInfoList.map((info) => file2Obj(info.file as RcFile));

    // Concat new files with prev files
    let newFileList = [...mergedFileList];

    objectFileList.forEach((fileObj) => {
      // Replace file if exist
      newFileList = updateFileList(fileObj, newFileList);
    });

    objectFileList.forEach((fileObj, index) => {
      // Repeat trigger `onChange` event for compatible
      let triggerFileObj: UploadFile = fileObj;

      if (!filteredFileInfoList[index].parsedFile) {
        // `beforeUpload` return false
        const { originFileObj } = fileObj;
        let clone;

        try {
          clone = new File([originFileObj], originFileObj.name, {
            type: originFileObj.type,
          }) as any as UploadFile;
        } catch (e) {
          clone = new Blob([originFileObj], {
            type: originFileObj.type,
          }) as any as UploadFile;
          clone.name = originFileObj.name;
          clone.lastModifiedDate = new Date();
          clone.lastModified = new Date().getTime();
        }

        clone.uid = fileObj.uid;
        triggerFileObj = clone;
      } else {
        // Inject `uploading` status
        fileObj.status = 'uploading';
      }

      onInternalChange(triggerFileObj, newFileList);
    });
  };

  const onSuccess = (response: any, file: RcFile, xhr: any) => {
    try {
      if (typeof response === 'string') {
        response = JSON.parse(response);
      }
    } catch (e) {
      /* do nothing */
    }

    // removed
    if (!getFileItem(file, mergedFileList)) {
      console.log('file removed', file);

      return;
    }

    const targetItem = file2Obj(file);
    targetItem.status = 'done';
    targetItem.percent = 100;
    targetItem.response = response;
    targetItem.xhr = xhr;

    const nextFileList = updateFileList(targetItem, mergedFileList);

    onInternalChange(targetItem, nextFileList);
  };

  const onProgress = (e: { percent: number }, file: RcFile) => {
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }

    const targetItem = file2Obj(file);
    targetItem.status = 'uploading';
    targetItem.percent = e.percent;

    const nextFileList = updateFileList(targetItem, mergedFileList);

    onInternalChange(targetItem, nextFileList, e);
  };

  const onError = (error: Error, response: any, file: RcFile) => {
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }

    const targetItem = file2Obj(file);
    targetItem.error = error;
    targetItem.response = response;
    targetItem.status = 'error';

    const nextFileList = updateFileList(targetItem, mergedFileList);

    onInternalChange(targetItem, nextFileList);
  };

  const handleRemove = (file: UploadFile) => {
    let currentFile: UploadFile;
    Promise.resolve(typeof onRemove === 'function' ? onRemove(file) : onRemove).then((ret) => {
      // Prevent removing file
      if (ret === false) {
        return;
      }

      const removedFileList = removeFileItem(file, mergedFileList);

      if (removedFileList) {
        currentFile = { ...file, status: 'removed' };
        mergedFileList?.forEach((item) => {
          const matchKey = currentFile.uid !== undefined ? 'uid' : 'name';
          if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {
            item.status = 'removed';
          }
        });
        upload.current?.abort(currentFile as RcFile);

        setIsPassBeforeLoad(true)
        onInternalChange(currentFile, removedFileList);
      }
    });
  };

  const renderUploadList = () =>
    fileRenderPortal.current &&
    ReactDOM.createPortal(
      mergedFileList?.length ? (
        <div className={`${prefixCls}-container`}>
          {mergedFileList?.map((file) => (
            <AttachmentCard
              key={file.uid}
              prefixCls={`${prefixCls}`}
              file={file}
              items={mergedFileList}
              iconRender={iconRender}
              onRemove={handleRemove}
              onPreview={onPreview}
            />
          ))}
        </div>
      ) : null,
      fileRenderPortal.current,
    );

  const mergedBeforeUpload = async (file: RcFile, fileLists: RcFile[]) => {
    const { beforeUpload } = props;

    let parsedFile: File | Blob | string = file;
    let beforeUploadError = '';
    if (beforeUpload) {
      try{
        const result = await beforeUpload(file, fileLists);
        if (result === false) {
          beforeUploadError = '上传文件不符合要求! ';
        }

        if (typeof result === 'object' && result) {
          parsedFile = result as File;
        }

      } catch(err){
        beforeUploadError = err as string;
      }
    
      if(beforeUploadError){
        alert(beforeUploadError)
        setIsPassBeforeLoad(false)
        return false;
      }

    }

    if(fileList && fileList.length > 0){
      if(maxCount!== undefined && fileList?.length >= maxCount){
        alert("最多支持上传10个文件！")
        setIsPassBeforeLoad(false)
        return false;
      }
  
       for(const fileItem of fileList){
        if(fileItem.name === file.name && fileItem.lastModified === file.lastModified){
          alert("文件已在上传列表中！")
          setIsPassBeforeLoad(false)
          return false;
        }
       }
    }
    
    setIsPassBeforeLoad(true)

    return parsedFile as RcFile;
  };

  const rcUploadProps = {
    onBatchStart,
    onError,
    onProgress,
    onSuccess,
    ...props,
    data,
    multiple,
    action,
    accept,
    supportServerRender: false,
    prefixCls,
    disabled,
    beforeUpload: mergedBeforeUpload,
    onChange: undefined,
  } as RcUploadProps;

  delete rcUploadProps.className;
  delete rcUploadProps.style;

  return (
    <span className={className}>
      <div className={uploadButtonCls}>
        <RcUpload {...rcUploadProps} ref={upload} />
      </div>
      {renderUploadList()}
    </span>
  );
};

export default Upload;
