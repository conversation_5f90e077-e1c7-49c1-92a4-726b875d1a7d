import React from 'react';
import type { TooltipProps as RcTooltipProps } from 'rc-tooltip/lib/Tooltip';
import RcTooltip from 'rc-tooltip';

type TooltipPlacement =
  | 'top'
  | 'left'
  | 'right'
  | 'bottom'
  | 'topLeft'
  | 'topRight'
  | 'bottomLeft'
  | 'bottomRight'
  | 'leftTop'
  | 'leftBottom'
  | 'rightTop'
  | 'rightBottom';

export type PopoverProps = {
  prefixCls?: string;
  content?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  placement?: TooltipPlacement;
  openClassName?: string;
  arrowPointAtCenter?: boolean;
  autoAdjustOverflow?: boolean;
  getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
  open?: RcTooltipProps['visible'];
  defaultOpen?: RcTooltipProps['defaultVisible'];
  onOpenChange?: RcTooltipProps['onVisibleChange'];
  afterOpenChange?: RcTooltipProps['afterVisibleChange'];
  mouseEnterDelay?: number;
  mouseLeaveDelay?: number;
  trigger?: RcTooltipProps['trigger'];
  overlayStyle?: React.CSSProperties;
  overlayClassName?: string;
  zIndex?: number;
  children: React.ReactElement;
  align?: RcTooltipProps['align'];
};

const Popover: React.FC<PopoverProps> = (props) => {
  const {
    prefixCls = 'WideComposer-tooltip',
    content,
    placement = 'topRight',
    trigger = 'hover',
    mouseEnterDelay = 0.3,
    mouseLeaveDelay = 0.3,
    // overlayStyle = {},
    getPopupContainer,
    open,
    onOpenChange,
    children,
    style,
    ...otherProps
  } = props;

  const overlay = (
    <div className={`${prefixCls}-inner-content`} onClick={(evt) => evt.stopPropagation()}>
      {content}
    </div>
  );

  return (
    <RcTooltip
      prefixCls={prefixCls}
      {...otherProps}
      placement={placement}
      trigger={trigger}
      mouseEnterDelay={mouseEnterDelay}
      mouseLeaveDelay={mouseLeaveDelay}
      overlay={overlay}
      visible={open}
      onVisibleChange={onOpenChange}
      arrowContent={<span className={`${prefixCls}-arrow-content`} />}
      motion={{
        motionName: 'zoom-big-fast',
        motionDeadline: 1000,
      }}
    >
      {children}
    </RcTooltip>
  );
};

export default Popover;
