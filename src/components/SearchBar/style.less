@placeholderColor: #ccc;

.searchHeader {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  height: 30px;
  box-sizing: border-box;

  .searchBox {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 30px;
    margin-left: 10px;
    padding: 0 20px 0 20px;

    border-radius: 12px;
    box-shadow: 0px 3px 12px 0px rgba(50, 50, 50, 0.2);
    background-color: #f5f5f5;
    box-sizing: border-box;

    .searchIcon {
      flex: 0 0 auto;
      width: 15px;
      height: 15px;
      margin-right: 2px;
    }

    .searchInput {
      flex: 1 1 auto;
      height: 30px;
    }

    .input {
      width: 100%;
      font-size: 15px;
      color: @placeholderColor;
      height: 30px;
      line-height: 30px;
      border: none;
      background-color: transparent;
      -webkit-appearance: none;

      &::placeholder {
        font-size: 15px;
        color: @placeholderColor;
      }
      &::-webkit-placeholder {
        font-size: 15px;
        color: @placeholderColor;
      }
      &:-ms-placeholder {
        font-size: 15px;
        color: @placeholderColor;
      }
    }
    .input:focus{
      border: none;
      outline: none;
    }

    .deleteIcon {
      flex: 0 0 auto;
      width: 15px;
      height: 15px;
      // background-image: url('./images/del.png');
      // background-size: cover;
    }
  }

  .cancel {
    font-size: 28px;
    color: #fff;
    flex: 0 0 auto;
    height: 60px;
    line-height: 60px;
    padding-left: 20px;
  }
}
