/** 搜索导航 */
import React, { useState, useRef } from 'react';
import DeleteIcon from './images/del.png';
import SearchIcon from './images/search.png';

interface SearchBarProps {
  /** 默认搜索关键字 */
  keyword?: string;
  /**  */
  searchPlaceholder?: string;
  /** 输入框变化的时候 */
  onInput: (value: string) => void;
  /** 搜索回调 */
  onSearch: (keyword: string) => void;
  /** 点击【取消】回调 */
  onCancel?: () => void;
  /** 点击输入框【清除】回调 */
  onClear: () => void;
}

const SearchBar: React.FC<SearchBarProps> = (props) => {
  const { keyword, searchPlaceholder } = props;

  // 中文输入法
  const [inComposition, setInComposition] = useState(false);
  const inputEleRef = useRef<HTMLInputElement>(null);

  // 初始化时input至于focus状态
  // useEffect(() => {
  //   debugger;
  //   // 页面销毁失去焦点
  //   const ele = inputEleRef.current;
  //   // ele?.focus();

  //   return () => {
  //     ele?.blur();
  //   };
  // }, []);

  const handleInputChange = (e: any) => {
    const val = e.target.value;
    props.onInput(val);

    if (!inComposition) {
      // 非拼音直接触发请求
      props.onSearch(val);
    }
  };

  const handleInputCompositionStart = () => {
    setInComposition(true);
  };

  const handleInputCompositionEnd = (e: any) => {
    setInComposition(false);
    props.onSearch(e.target.value);
  };

  // 提交事件
  const handleSubmit = (e: any) => {
    e.preventDefault();
    inputEleRef?.current?.blur();
  };

  const handleClear = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    inputEleRef?.current?.focus();

    props.onClear();
  };

  return (
    <div className="searchHeader">
      <div className="searchBox">
        <img className="searchIcon" src={SearchIcon} />
        <div className="searchInput">
          <form action="#" onSubmit={handleSubmit}>
            <input
              type="search"
              ref={inputEleRef}
              className="input"
              placeholder={searchPlaceholder ?? "请输入搜索内容"}
              value={keyword}
              onChange={handleInputChange}
              maxLength={50}
              onCompositionStart={handleInputCompositionStart}
              onCompositionEnd={handleInputCompositionEnd}
            />
          </form>
        </div>
        {
          !keyword
            ? null
            : (<img src={DeleteIcon} className="deleteIcon" onClick={handleClear} />)
        }
      </div>
    </div>
  );
};

export default SearchBar;
