import { MessageProps } from '../components/Message';

// 解析思维链，获取思考内容和正文
export function getThinkContent(msgText: string) {
  const startIndex = msgText?.indexOf('<think>');
  const endIndex = msgText?.indexOf('</think>');
  let content = '';
  // 找到了开始标记则截取思考内容
  const thinkContent = startIndex > -1 ? msgText?.slice(startIndex + '<think>'.length, endIndex) : '';
  if (endIndex > -1) {
    // 表示思考结束，使用结束标记后面的内容为正文
    content = msgText?.slice(endIndex + '</think>'.length);
  }
  if (startIndex === -1 && endIndex === -1) {
    // 表示没有找到思考开始与思考结束标记，则所有内容均为正文
    content = msgText;
  }
  return {
    content,
    thinkContent,
    isThinking: startIndex > -1 && endIndex === -1, // 有思考开始，没有思考结束表示正在思考中
  }
}

// 格式一下魔方卡片内容
export function formatLowcodeContent(content: MessageProps['content']) {
  const lowCodeDomin = window.isLowCodeDev ? 'http://168.61.127.80/app/' : 'http://lowcode.fe.htsc/app/';

  return {
    url: lowCodeDomin + content?.cardBizType + '/editor',
    data: content,
  };
}

// 时间格式转换
const REGEX_FORMAT = /YYYY|MM|DD|HH|mm|ss/g;
type DateFormats = {
  [p: string]: string;
};
const padStart = (n: number) => (n <= 9 ? '0' : '') + n;
export function getDateFormat(d: number | string | Date, formatString = 'YYYY-MM-DD HH:mm:ss') {
  const date = new Date(d);

  const dates: DateFormats = {
    YYYY: date.getFullYear() + '',
    MM: padStart(date.getMonth() + 1),
    DD: padStart(date.getDate()),
    HH: padStart(date.getHours()),
    mm: padStart(date.getMinutes()),
    ss: padStart(date.getSeconds()),
  }

  return formatString?.replace(REGEX_FORMAT, (match) => dates[match]);
}
