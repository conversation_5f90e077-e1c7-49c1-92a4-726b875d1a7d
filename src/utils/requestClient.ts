import { request as h5request } from '@ht/h5-utils';
// import { queryToString } from './utils';

const { request } = h5request;

const DEFAULT_TIMEOUT = 15000;

export interface RequestOptions {
  action: string;
  method: string;
  url: string;
  headers: object;
  payload?: any;
  timeout: number;
  type: 'http' | 'tcp';
  responseValidator?: (res: any) => boolean | Promise<boolean>;
  requestTransfer?: (params: any) => Promise<any>;
  responseTransfer?: (params: any) => Promise<any>
}

export function queryToString(query: Record<string, unknown>) {
  return Object.keys(query)
    .map(
      (key) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(query[key] as any)}`
    )
    .join('&');
}

/**
 * Requests a URL, returning a promise
 *
 * @param  {string} url       完整的 业务url 地址，如 /mcrm/api/v1/groovy/mobile/mobileMenu
 * @param  {object} [options] 除了 axios 的 options 之外，支持设置站内请求的特定参数。
 *    站内参数：
 *      action: 网关号
 *      path: 携带在 /reqxml?path=xxx 中的路径.
 *      nativeParams: 其它需要带在路径中的参数
 *      MS__REQUEST__PAYLOAD: 真实请求发送的请求参数
 *
 * @return {object}           The response data
 */
export async function postRequest(baseUrl: string, options: RequestOptions) {
  let prefix = baseUrl ? baseUrl : '';
  let payload = options?.payload;
  const useHttp = (options?.type ?? 'http') === 'http';
  if (options?.requestTransfer) {
    payload = await options?.requestTransfer(payload);
  }
  const finalUrl = options?.method === 'GET' ? `${prefix + options?.url}?${queryToString(payload)}` : prefix + options?.url;
  const finalHeaders = {
    'Content-Type': 'application/json',
    ...(options?.headers || {})
  };
  let reqData: object;
  //聊他body部分需要包一层MS__REQUEST__PAYLOAD
  if (payload?.appId === 'aorta') {
    reqData = {
      action: options?.action,
      path: prefix + options?.url,
      ...finalHeaders,
      MS__REQUEST__PAYLOAD: JSON.stringify(payload)
    };
  } else {
    reqData = {
      action: options?.action,
      path: prefix + options?.url,
      ...finalHeaders,
      ...(options?.method === 'POST' ? { data: payload } : { params: payload }),
    };
  }

  let reqConfig: any = {
    timeout: options?.timeout || DEFAULT_TIMEOUT,
    forceHttpRequest: useHttp,
    responseValidator: options?.responseValidator,
  };

  // 发送http请求：不走ms请求地址取url；走ms请求地址取path
  // usehttp的请求返回response的data
  // 站内默认走tcp请求，如果配置了usehttp才走http
  if (!options?.action || useHttp) {
    reqData = {};
    reqConfig = {
      ...reqConfig,
      ...options,
      data: payload,
      url: finalUrl
    };
    console.log('1 reqData=', reqData);
    console.log('1 reqConfig=', reqConfig);
    let res = await request(reqData, reqConfig);
    console.log('1 res=', res);
    if (options?.responseTransfer) {
      res = await options?.responseTransfer(res);
    }
    return res;
  }

  console.log('2 reqData=', reqData);
  console.log('2 reqConfig=', reqConfig);
  let res = await request(reqData, reqConfig);
  console.log('2 res=', res);
  if (options?.responseTransfer) {
    res = await options?.responseTransfer(res);
  }
  return res;
}
