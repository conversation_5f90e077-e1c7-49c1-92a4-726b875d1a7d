@theme: default;

@ai-kit-prefix: ai-kit;

@global-style: true;

// Color Palette
@brand-0:#3e74f7;
@brand-1: #255ff2;
@brand-2: #78a7ff;
@brand-3: #f0f7ff;
@brand-4:#f7faff;
@brand-5: #cae3ff;
@brand-6: #cfe1f4;
@black: #000;
@white: #fff;
@background-1:#f7f7f7;
@background-1: #cae3ff;
@gray-0: #111;
@gray-1: #333;
@gray-2: #666;
@gray-3: #999;
@gray-4: #ccc;
@gray-5: #ddd;
@gray-6: #eee;
@gray-7: #f5f5f5;
@gray-8: #f8f8f8;

@blue: #255ff2;
@gray-dark: #333;
@green: #62d957;
@orange: #f70;
@red: #ff3634;
@yellow: #ffc233;
@yellow-light: #fff9db;
@transparent: transparent;


@light-1: @gray-6;
@light-2: @gray-7;
@highlight-1: @brand-1;
@highlight-2: @brand-2;
@link-color: @blue;

// safe
@safe-top: 0px;
@safe-bottom: 0px;

// Box Shadow
@shadow-1: 0 3px 4px 0 rgba(0, 0, 0, 0.04);
@shadow-2: 0 4px 8px 0 rgba(0, 0, 0, 0.08);
@shadow-3: 0 6px 10px 0 rgba(0, 0, 0, 0.08);

// Gutter
@gutter: 12px;

// Body
@body-bg: @white;
@body-color: @gray-1;

// Links
@link-color: @blue;
@link-decoration: none;
// @link-hover-color: darken(@link-color, 15%);
@link-hover-decoration: underline;

// Typography
@font-family-sans-serif: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
  Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
@font-family-base: @font-family-sans-serif;

@font-size-root: 16px;
@font-size-base: 16px;
@font-size-small: 14px;
@font-size-large: 14px;

@font-size-xxs: @font-size-base * (10px / @font-size-root);
@font-size-xs: @font-size-base * (12px / @font-size-root);
@font-size-sm: @font-size-base * (14px / @font-size-root);
@font-size-md: @font-size-base* (16px / @font-size-root);
@font-size-lg: @font-size-base * (18px / @font-size-root);

// @font-weight-lighter: lighter;
// @font-weight-light: 300;
@font-weight-normal: 400;
@font-weight-bold: 500;
// @font-weight-bolder: bolder;

@font-weight-base: @font-weight-normal;

@line-height-base: 1.5;
@line-height-lg: 1.75;
@line-height-sm: 1.25;

// Border
@border-width: 1px;
// @border-color: @gray-4;
@border-radius-sm: 4px; // 0.25rem
@border-radius-md: 12px; // 0.75rem
@border-radius-lg: 20px; // 1.25rem
@border-color: rgba(62, 116, 247, 0.7);

// Avatar
@avatar-size: 36px;
@avatar-size-sm: 24px;
@avatar-size-lg: 40px;

@avatar-square-border-radius: 4px;

// Backdrop
@backdrop-bg: rgba(0, 0, 0, 0.7);

// Button
@btn-padding: 5px 12px;
@btn-border-radius: 999px;
@btn-bg: @white;
@btn-font-family: inherit;
@btn-font-size: @font-size-sm;
@btn-font-weight: @font-weight-normal;
@btn-line-height: @line-height-base;

@btn-hover-bg: rgba(0, 0, 0, 0.04);
@btn-active-bg: rgba(0, 0, 0, 0.08);

@btn-padding-sm: 4px 12px;
// @btn-border-radius-sm: 15px;
@btn-font-size-sm: @font-size-sm;

@btn-padding-lg: 7px 12px;
// @btn-border-radius-lg: 50px;
@btn-font-size-lg: @font-size-md;

@btn-block-spacing-y: 9px;

@btn-border-width: @border-width;
@btn-border-color: @gray-5;

@btn-transition: 0.15s ease-in-out;

@btn-primary-border-color: transparent;
@btn-primary-color: @white;
@btn-primary-bg: linear-gradient(90deg, @brand-2 0%, @brand-1 98%);
@btn-primary-hover-bg: @btn-primary-bg right/200%;
@btn-primary-active-bg: @btn-primary-bg right/400%;

// Composer
@composer-compact-input-max-height: 132px;
@composer-compact-input-min-height: 36px;
@composer-compact-input-padding: 8px 12px;
@composer-compact-input-border: none;
@composer-compact-input-bg: @white;
@composer-compact-input-border-radius: 12px;
@composer-compact-input-caret-color: @brand-2;
@composer-compact-input-transition: border-color 0.15s ease-in-out;
@composer-compact-input-color: #333;

@composer-compact-bg: #fff;
@composer-compact-border-radius: 12px;
@composer-compact-border-color: @gray-5;
@composer-compact-border-width: 0;
@composer-compact-box-shadow: 0px 3px 12px 0px rgba(50, 50, 50, 0.2);
@composer-compact-net-search-bg: #f4f6f9;
@composer-compact-net-search-color: #666;
@composer-compact-net-search-active-bg: rgba(62, 116, 247, 0.1);
@composer-compact-net-search-active-color: @brand-0;
@composer-compact-send-color: @brand-0;

@composer-wide-border-radius: 2px;
@composer-wide-border-color: #dee8f5;
@composer-wide-border-width: 1px;
@composer-wide-box-shadow: none;
@composer-wide-net-search-height: 24px;
@composer-wide-net-search-border: 1px solid #dee8f5;
@composer-wide-net-search-border-radius: 2px;
@composer-wide-net-search-color: #7c8db5;
@composer-wide-net-search-active-color: #007aff;
@composer-wide-llm-height: 24px;
@composer-wide-llm-bg: white;
@composer-wide-llm-color: #7c8db5;
@composer-wide-llm-border:1px solid #dee8f5;
@composer-wide-llm-border-hover: #007aff;
@composer-wide-llm-border-active: #007aff;
@composer-wide-llm-option-color: #25396f;
@composer-wide-llm-option-selected-color: #007aff;
@composer-wide-llm-option-selected-bg: #f6f9ff;
@composer-wide-send-bg: #007aff;
@composer-wide-send-size: 30px;
@composer-wide-send-disable-bg: rgba(166, 166, 166, 0.1);
@composer-wide-send-disable-border: 1px solid #a6a6a6;

// Input
@input-width: 100%;
@input-min-height: 20px;
@input-margin: 0;
@input-padding: 5px @gutter;
@input-border: 1px solid @gray-6;
@input-border-radius: 12px;
@input-bg: @white;
@input-font-family: inherit;
@input-font-size: @font-size-sm;
@input-line-height: 20px;
@input-color: @gray-1;
@input-resize: none;

// List
@list-bg: @white;
@list-border-width: @border-width;
@list-border-color: @gray-7;
@list-border-radius: 2px;

@list-item-padding: 10px @gutter;
@list-item-color: @gray-1;
@list-item-font-size: 15px;
@list-item-line-height: 1.6;
@list-item-icon-color: @gray-3;

@list-item-hover-bg: @btn-hover-bg;

@list-item-active-bg: @gray-6;

// Navbar
@navbar-height: 44px;
@navbar-padding: 0 @gutter;
@navbar-bg: @transparent;
@navbar-border-color: @gray-5;

@navbar-color: @gray-1;
@navbar-font-size: @font-size-lg;
@navbar-logo-height: @navbar-height - 8px;

// Notice
@notice-padding: @gutter;
@notice-border-radius: 12px;
@notice-bg: @white;
@notice-icon-color: @brand-1;

@notice-content-color: @gray-1;
@notice-content-font-size: 13px;

// Popover
@popover-border-radius: 6px;
@popover-bg: @white;
@popover-box-shadow: @modal-box-shadow;

// RateActions
@rate-btn-bg: @white;

// Bubble
@bubble-max-width: 800px;
@bubble-left-padding-left: 15px;
@bubble-left-padding-right: 15px;
@bubble-left-padding-top: 12px;
@bubble-left-padding-bottom: 12px;

@bubble-right-padding-left: 15px;
@bubble-right-padding-right: 15px;
@bubble-right-padding-top: 12px;
@bubble-right-padding-bottom: 12px;

@bubble-left-bg: @brand-4;
@bubble-left-color: '';
@bubble-left-border-radius: 12px;

@bubble-right-bg: @brand-5;
@bubble-right-color: '';
@bubble-right-border-radius: 12px;

@bubble-left-gutter: 48px;
@bubble-right-gutter: 40px;

@bubble-text-padding: 12px;

@bubble-img-max-width: 200px;
@bubble-img-max-height: 200px;

@bubble-typing-padding: 8px 16px;
@bubble-text-font-size: @font-size-md;
@bubble-think-text-font-size: @font-size-sm;

// Card
@card-padding: 12px;
@card-title-padding: @card-padding @card-padding 6px;
@card-title-font-size: @font-size-md;
@card-title-font-weight: @font-weight-bold;
@card-subtitle-font-size: @font-size-xxs;
@card-subtitle-color: @gray-3;
@card-border-width: @border-width;
@card-border-radius: 12px;
// @card-border-color: ;
@card-bg: @white;

@card-text-color: @gray-dark;

@card-size-xl: 300px;
@card-size-lg: 160px;
@card-size-md: 120px;
@card-size-sm: 104px;
@card-size-xs: 80px;

@card-fluid-width: calc(100% - 48px);
@card-max-width: 680px;
@card-min-width: 260px;

@card-btn-padding: 10px;
@card-btn-line-height: 1.5;
@card-btn-spacing-x: @gutter;
@card-btn-spacing-y: 0;
@card-btn-border-color: @gray-6;

@card-column-btn-bg: @white;
@card-column-btn-color: @gray-3;

@card-column-btn-primary-color: @brand-1;

@card-column-btn-hover-bg: @gray-7;
@card-column-btn-active-bg: @gray-7;

@card-column-btn-disabled-color: @gray-4;

// Carousel
@carousel-dots-bottom: 8px;

@carousel-dot-width: 8px;
@carousel-dot-height: @carousel-dot-width;
@carousel-dot-margin: 0 4px;
@carousel-dot-padding: 0;
@carousel-dot-border: 0;
@carousel-dot-border-radius: 50%;
@carousel-dot-bg: @gray-4;
@carousel-dot-transition: 0.3s;

// Icon
@icon-size-lg: 30px;

// IconButton
@icon-button-border: 0;
@icon-button-border-radius: 3px;
@icon-button-bg: transparent;
@icon-button-color: @gray-2;
@icon-button-size: 18px;

@icon-button-primary-color: @brand-2;

@icon-button-lg-border-radius: 6px;
@icon-button-lg-size: 24px;

@icon-button-disabled-border-color: @gray-6;
@icon-button-disabled-color: @gray-6;

// Modals
@modal-width: 320px;
@modal-border-radius: 12px;

@modal-bg: @white;
@modal-box-shadow: @shadow-3;

@modal-header-padding: @gutter;

@modal-title-margin: 0;
@modal-title-color: @gray-1;
@modal-title-font-size: @font-size-lg;
@modal-title-font-weight: @font-weight-bold;

@modal-close-padding: 0;
@modal-close-color: @gray-1;

@modal-footer-x-padding: @gutter;
@modal-btn-x-spacing: @gutter;

@modal-btn-y-padding: 12px;
@modal-btn-y-border-width: 1px;
@modal-btn-y-border-color: @gray-6;
@modal-btn-y-bg: @white;
@modal-btn-y-color: @gray-2;
@modal-btn-y-primary-color: @brand-1;

@modal-fade-transform: translate(0, -50px);
@modal-show-transform: none;
@modal-opacity: 0;
@modal-show-opacity: 1;
@modal-transition: transform 0.3s ease-out, opacity 0.15s linear;

// Popup
@popup-border-radius: 27px 27px 0 0;
@popup-bg: @white;

@popup-max-height: 70vh;
@popup-wide-width: 480px;

@popup-header-padding: 15px 40px;

@popup-title-margin: @modal-title-margin;
@popup-title-color: @modal-title-color;
@popup-title-font-size: @modal-title-font-size;

@popup-close-color: @modal-close-color;

@popup-footer-padding: 9px @gutter;
@popup-btn-x-spacing: 9px;

// SendConfirm
@send-confirm-dialog-width: 480px;
@send-confirm-dialog-margin: 20px;
@send-confirm-inner-height: 320px;

// Progress bars
@progress-height: 2px;
@progress-bg: @gray-5;
@progress-border-radius: 100px;
@progress-bar-bg: @blue;
@progress-bar-transition: width 0.6s ease;

@progress-bar-bg-success: @green;
@progress-bar-bg-error: @red;

// QuickReplies
@quick-replies-padding: 9px @gutter;
@quick-replies-bg: @gray-7;

@quick-reply-color: @gray-1;
@quick-reply-font-size: @font-size-sm;

@quick-reply-hover-bg: @gray-6;

@quick-reply-dot-top: 0;
@quick-reply-dot-right: 0;
@quick-reply-dot-size: 8px;
@quick-reply-dot-bg: @red;

// ScrollView
@scroll-view-spacing-x: 6px;

// Tabs
@tabs-nav-link-padding: 4px 12px;
@tabs-nav-link-border: 0;
@tabs-nav-link-border-radius: 20px;
@tabs-nav-link-bg: transparent;
@tabs-nav-link-color: @gray-2;
@tabs-nav-link-font-size: @font-size-sm;
@tabs-nav-link-transition: 0.3s;

@tabs-nav-link-hover-color: @gray-1;

@tabs-nav-link-active-color: @tabs-nav-link-hover-color;

@tabs-nav-pointer-height: 3px;
@tabs-nav-pointer-bg: @btn-primary-bg;
@tabs-nav-pointer-border-radius: 2px;
@tabs-nav-pointer-transition: 0.3s;

// Tag
@tag-margin: 0 4px 0 0;
@tag-padding: 0 6px;
@tag-border-radius: 4px;
@tag-color: @brand-1;
@tag-font-size: 12px;

// Price
@price-font-size: @font-size-sm;

// Goods
@goods-img-width: 72px;
@goods-img-height: @goods-img-width;
@goods-img-border-radius: 12px;

@goods-padding: @gutter;
@goods-gap: 9px;
@goods-desc-color: @gray-3;
@goods-meta-color: @gray-3;
@goods-meta-font-size: @font-size-xxs;
@goods-border-width: 1px;
@goods-border-color: @gray-7;
@goods-count-color: @gray-3;
@goods-count-font-size: @font-size-xs;
@goods-unit-font-size: @font-size-xxs;
@goods-buy-btn-bg: @brand-1;
@goods-buy-btn-color: #fff;
@goods-buy-btn-padding: 2px;
@goods-detail-btn-min-width: 48px;
@goods-detail-btn-padding: 0 10px;
@goods-detail-btn-border-radius: 10px;
@goods-detail-btn-font-size: @font-size-xxs;
@goods-detail-btn-line-height: 18px;
@goods-status-color: @highlight-2;

// MessageList
@message-list-padding: 20px;

@message-spacer-y: 12px;

// Toast
@toast-content-padding: 18px 24px;
@toast-content-border-radius: 12px;
@toast-content-bg: rgba(0, 0, 0, 0.5);
@toast-typing-content-bg: #cfe2ff;

@toast-message-margin: 0;
@toast-message-color: @white;
@toast-typing-message-color: @brand-0;
@toast-message-font-size: 16px;

// Z-index
@zindex-backdrop: 100;
@zindex-modal: 100;
@zindex-popup: @zindex-modal;
@zindex-popover: 1030;
@zindex-toast: 200;
@zindex-tooltip: 200;
@zindex-recorder-toast: 100;

@zindex-navbar: 10;
@zindex-footer: 10;
@zindex-quick-replies: 110;
@zindex-rate-actions: 10;

@zindex-step-dot: 2;

// AiChat
@chat-bg: linear-gradient(180deg, @brand-6 0%, @white 50%, @white 100%);
@chat-color: @gray-1;

// bubble-think
@bubble-think-bg: transparent;
@bubble-think-color: #888;

// navBar
@navbar-title-font-size: @font-size-lg;
@navbar-title-color: @gray-1;
@navbar-box-shadow: transparent;

// guidePage
@guide-page-bg: @transparent;
@guide-page-title-color: @gray-1;
@guide-page-subtitle-color: @gray-2;
@guide-page-risk-tip-color: @gray-3;

// quickReply
@quick-reply-bg: @white;
@quick-reply-font-color: @gray-1;

// historyConversation
@history-conversation-bg: @transparent;
@history-conversation-active-bg: rgba(62, 116, 247, 0.1);
@history-conversation-active-color: #3E74F7;

// feedbackTip
@feedback-tip-title-bg: linear-gradient(180deg, #CFE1F4 0%, #FFFFFF 100%);
@feedback-tip-titleText-bg: linear-gradient(270deg, #48AEFF 0%, #325EC8 100%);
@feedback-tip-titleText-font-color: transparent;
@feedback-tip-titleText-font-size: 20px;
