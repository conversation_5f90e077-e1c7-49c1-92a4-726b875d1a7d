export const navConfig = [
  {
    title: '对话组件',
    list: [
      { name: '对话容器', code: 'ai-chat' },
      { name: '公共对话容器', code: 'common-chat' },
      { name: '投行宽屏对话容器', code: 'wide-ai-chat' },
      { name: 'pc宽窄屏对话容器', code: 'fsp-ai-chat' },
      { name: '左推', code: 'push-div' },
      { name: '引导页', code: 'guide-page' },
      { name: '对话容器', code: 'chat' },
      { name: '气泡', code: 'bubble' },
      { name: '卡片', code: 'card' },
      { name: '文件卡片', code: 'file-card' },
      { name: '文本卡片', code: 'mark-down' },
      { name: '消息状态', code: 'message-status' },
      { name: '系统消息', code: 'system-message' },
      { name: '输入中', code: 'typing' },
      { name: '消息列表', code: 'message-container' },
      { name: '消息', code: 'message' },
      { name: '输入框', code: 'composer' },
      { name: '水印', code: 'water-mark' },
      { name: '思维链', code: 'think-content' },
      { name: '参考资料', code: 'references' },
      { name: '虚拟列表', code: 'virtual-list' },
    ],
  },
  {
    title: '基础组件',
    list: [
      { name: '按钮', code: 'button' },
      { name: '布局', code: 'flex' },
      { name: '图标', code: 'icon' },
      { name: '图片', code: 'image' },
    ],
  },
  {
    title: '表单组件',
    list: [
      { name: '复选框', code: 'checkbox' },
      { name: '表单', code: 'form' },
      { name: '单选框', code: 'radio' },
      { name: '点赞点踩', code: 'rate-actions' },
      { name: '输入框', code: 'input' },
      { name: '搜索框', code: 'search' },
      { name: '选择器', code: 'select' },
    ],
  },
  {
    title: '展示组件',
    list: [
      { name: '头像', code: 'avatar' },
      { name: '图片轮播', code: 'carousel' },
      { name: '分割线', code: 'divider' },
      { name: '空状态', code: 'empty' },
      { name: '列表', code: 'list' },
      { name: '导航', code: 'navbar' },
      { name: '公告', code: 'notice' },
      { name: '进度条', code: 'progress' },
      { name: '富文本', code: 'rich-text' },
      { name: 'markdown', code: 'mark-down' },
      { name: '滚动容器', code: 'scroll-view' },
      { name: '步骤条', code: 'stepper' },
      { name: '标签页', code: 'tabs' },
      { name: '标签', code: 'tag' },
      { name: '文本', code: 'text' },
      { name: '时间', code: 'time' },
      { name: '视频', code: 'video' },
      { name: '媒体对象（废弃）', code: 'media-object' },
    ],
  },
  {
    title: '反馈组件',
    list: [
      { name: '无限滚动', code: 'infinite-scroll' },
      { name: '加载', code: 'Loading' },
      { name: '确认框', code: 'confirm' },
      { name: '模态框', code: 'modal' },
      { name: '上拉弹窗', code: 'popup' },
      { name: '下拉刷新', code: 'pull-to-refresh' },
      { name: '轻提示', code: 'toast' },
    ],
  },
  {
    title: '业务组件',
    list: [
      { name: '商品卡片', code: 'goods' },
      { name: '价格', code: 'price' },
    ],
  },
  {
    title: '其它组件',
    list: [
      { name: '组件加载器', code: 'component-provider' },
      { name: '错误边界', code: 'error-boundary' },
      { name: '传送门', code: 'portal' },
      { name: '视觉上隐藏', code: 'visually-hidden' },
    ],
  },
  {
    title: 'Hooks',
    list: [
      { name: '创建会话', code: 'use-create-conversation' },
      { name: '发送消息', code: 'use-send-message' },
      { name: '停止消息', code: 'use-stop-message' },
      { name: '获取会话列表', code: 'use-get-conversation-list' },
      { name: '删除会话', code: 'use-delete-conversation' },
      { name: '获取消息列表', code: 'use-get-message-list' },
      { name: '反馈评价', code: 'use-feedback' },
      { name: '敏感词校验', code: 'use-sensitive' },
    ],
  },
];
