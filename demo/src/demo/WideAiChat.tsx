import React, { useCallback } from 'react';
// import  {API_PREFIX} from '@lowcode/lc-render';
import LCRender from '@lowcode/lc-render';
import { DemoPage } from '../components';
import { message, Button } from '@ht/ioned';
import {
  // MessageProps,
  WideAiChat,
  // useMessages,
  ComposerCustomRequestOptions,
} from '../../../src/index';
import WideComposer from '../components/WideComposer';
import CustomWelcome from '../components/CustomWelcome';
import './WideAiChat.less';
import { AiChatHandle } from '../../../src/components/AiChat/interface';
import LogoPng from './images/deepseekChat.png';

//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;

export default () => {
  // 消息列表
  // const { messages, appendMsg, updateMsg, prependMsgs, setTyping, getTyping, getMessages } = useMessages(initialMessages);
  // const { quickReplies } = useQuickReplies(defaultQuickReplies);
  const msgRef = React.useRef(null);
  const ref = React.useRef<AiChatHandle>(null);

  // window.appendMsg = appendMsg;
  // window.updateMsg = updateMsg;
  // window.prependMsgs = prependMsgs;
  // window.setTyping = setTyping;
  // window.getTyping = getTyping;
  // window.getMessages = getMessages;
  // window.msgRef = msgRef;

  const config = {
    //租户id：表示当前系统
    appId: 'aorta',

    //用户id：代表当前系统唯一用户id
    userId: '002332',

    //场景id
    sceneId: 'scene',

    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      host: 'AROTA',
    },

    //接口请求 method默认post
    requests: {

      /**
      * 基础URL
      */
      baseUrl: '',

      //初始引导接口
      init: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',
        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },
        // requestTransfer: (input: object) => {
        //   return new Promise((resolve, reject) => {
        //     try {
        //       const parsedInput = {
        //         ...input,
        //         customerInput1: '123',
        //       }
        //       resolve(parsedInput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        // responseTransfer: (output: object) => {
        //   return new Promise((resolve, reject) => {
        //     const parsedOutput = {
        //       ...output,
        //       customeroutput1: '123',
        //     }
        //     try {
        //       resolve(parsedOutput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        requestTransfer: (input: object) => {
          const parsedInput = {
            ...input,
            customerInput1: '123',
          }
          return parsedInput;
        },
        responseTransfer: (output: object) => {
          const parsedOutput = {
            ...output,
            customeroutput1: '123',
          }
          return parsedOutput;
        },
      },

      //问答接口
      send: {
        url: 'http://aorta.uat.saas.htsc/aorta/ai-bag/workflow/main-v1',
        // url: 'http://10.102.77.187:8000/api/june/chat/stream',
        stream: true,
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //查询历史接口
      history: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        // pageSize: 6,
      },

      //快捷问题接口
      // quickReply: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryQuickReplies',
      //   headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      // },

      //点赞点踩接口
      score: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //停止生成接口
      // stop: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/stopGeneratingAnswerAortaAI',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },

      //关联问题接口
      related: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryRelatedQuestions',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },

      // 历史会话列表
      historyConversation: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
        // type: 'tcp',
        // paramsKey: 'MS__REQUEST__PAYLOAD',
        // action: '53421',
      },
      // 阅读风险提示
      riskRead: {
        url: '/mcrm/api/groovynoauth/chatBar/updateUserBehaviorByType',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // 查询是否阅读了风险提示
      queryRiskRead: {
        url: '/mcrm/api/groovynoauth/chatBar/queryUserBehaviorByType',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // 敏感词校验
      // sensitive: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 点踩反馈标签列表
      feedbackTagList: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryAllAiTag',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },

    },
    bridge: {// 原生bridge
      // 打开新页面
      openWebPage: useCallback((url: any) => {
        console.log('打开新页面', url)
      }, []),
      // 调用原生方法打开页面预览文件支持word\pdf
      // openFileViews: (url: any) => {
      //   console.log('预览文件', url)
      // },
      // 复制文本的方法
      copyText: useCallback((text: string) => {
        console.log('复制文本', text);
      }, []),
    },
    // pushPosition: 'left',
    // pushPercent: 50,
    // showInternetSearch: true,
    /**
     * 加载时是否默认展示新会话（为false则直接进入对话中页面）
     */
    // showNewConversation: false,
    waterMark: {
      show: true,
      text: `宋娟       010497`,
    },
    // robot: {
    //   logo: LogoPng
    // }
  }
  const navbarProps = {

    // open?: boolean,//是否展示navbar，默认true

    // 返回按钮设置项
    // showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
    // returnButtonIcon?: string;//返回按钮图标路径
    // onReturnButtonClick?: ()=>void;//点击返回按钮响应处理函数

    // 标题区域设置
    // showLogo?: boolean,//是否展示logo,默认为true
    // logoIcon?: string, // logo图标的地址
    // title?: string; //头部标题文案，展示于logo右侧,默认为空字符串
    // logoAndTitlePosition?:'left' | 'center';//标题区域的位置：pc端默认靠左边，移动端默认居中

    // 历史会话按钮设置项
    // showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
    // historyButtonIcon?: string,//历史会话按钮图标路径
    // historyButtonPosition?: 'left' | 'right' ,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
    // onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数

    //新建会话按钮设置项
    // showNewButton?: boolean;//是否显示新建会话按钮，默认为true
    // newButtonIcon?: string,//新建会话按钮图标路径
    // onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

    // 关闭按钮设置项
    // showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
    // closeButtonIcon?: string;//关闭按钮图标路径
    // onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数
    // wrapstyle: {
    //   paddingTop: '20px',
    // }
    showLogo: false,
    title: '',
    logo: LogoPng,
    showHistoryButton: false,
    showNewButton: false,
    showCollapseToWindowButton: true, // 是否展示折叠到窗口按钮
    onCollapseToWindowButtonClick: () => {
      console.log('点击折叠到窗口按钮');
    }
  }

  // const renderWelcome = () => {
  //   return (
  //     <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: '100%' }}>这是欢迎页</div>
  //   )
  // }
  // const renderQuickReplies = () => {
  //   return (
  //     <div style={{ marginLeft: 20, marginBottom: 10 }}>11111</div>
  //   )
  // }

  const onReportLog = useCallback((params: any) => {
    // log(params); // 需要init
    console.log(params);
  }, []);

  const RenderHistoryConversationFooter = () => {
    return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的底部自定义模块</div>
  }

  const RenderHistoryConversationBrand = () => {
    return (
      <div
        style={{
          boxSizing: 'border-box',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          height: '40px',
          paddingTop: '9px',
          marginLeft: '20px',
          marginRight: '20px',
          color: '#fff',
          background: '#3E74F7',
          borderRadius: '2px',
          fontSize: '14px',
        }}
      >
        + 开启新对话
      </div>
    )
  }

  const renderCardContent = useCallback((data) => {
    console.warn('renderCardContent', data);
    return <div>睿评卡片{data?.[0]?.type}-{data?.[0]?.content?.fileTitle}</div>
  }, []);



  const renderBrand = () => {
    const handleNewConversation = () => {
      ref.current?.chatContext?.handleNewConversation?.();
      console.log('创建新会话');
    }
    return <div className="quickNav">
      <Button
        onClick={handleNewConversation}
        className="new_session_btn"
      >
        <div className="new_session_btn_wrap">
          <img src="http://lighten-renderer.sit.saas.htsc/ione-lighten-renderer/static/addchat.09d0d5dd.svg"></img>
          <span>开始新会话</span>
        </div>
      </Button>
    </div>;
  };

  return (
    <DemoPage>
      <div
        className="wide-screen-demo"
        style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }}
        id="chat-container"
      >
        <WideAiChat
          navbar={navbarProps}
          messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          // initialMessages={initialMessages}
          config={config}
          onReportLog={onReportLog}
          showPushHistory={false}
          // quickReplies={[{
          //   title: 'string',//页面展示的快捷问题
          //   content: 'realString',//点击后实际发送的问题
          //   url: '',//如果配置，点击后跳转链接
          //   isHighlight: true,
          //   isNew: true,
          //   img: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg'
          // }]}

          historyConversation={{
            // pushPosition?: 'left' | 'right';   //推的方向，左推还是右推
            // pushPercent?: number; //推多宽，50 表示半屏，100表示全屏，默认75
            title: '问TA', //历史会话列表标题
            logo: LogoPng,
            // logo: string,  //历史会话列表图标
            // renderTitle?:  function // 支持自定义标题区域
            // showSearchArea?: boolean//是否展示搜索区域，默认为true
            // searchPlaceholder?: string, //搜索placeholder设置，默认为“请输入搜索关键字”
            renderBrand: RenderHistoryConversationBrand, //支持传入自定义brand区域
            // renderFooter: RenderHistoryConversationFooter, //支持传入自定义底部区域
            showSearch: false,
          }}

          welcome={{
            // open?: boolean //是否展示欢迎页，默认true
            // logo?: string //logo图标的地址
            // itle?: string //欢迎页主标题，默认："智能助手为您服务~",
            // subtitle: string //欢迎页副标题，默认："作为您的智能助手，我可以为您答疑解惑",
            // riskTip: string //欢迎页底部风险提示，默认："内容由AI大模型生成，请谨慎识别"
            title: 'Hi～我是 问TA',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            // showsubTitle: false,
            logo: '',
            openMessages: [{ type: 'list', content: '宁德时代的近期走势' }, { type: 'list', content: '如何做好黄金投资？' }, { type: 'list', content: '今日金价？' }],
            navbar: {
              onCloseButtonClick: () => {
                console.log('关闭全屏弹窗');
              },
              // 配置和上面的navbarProps相同，优先在欢迎页生效
              title: '',
              showHistoryButton: false,
              showNewButton: false,
              showCollapseToWindowButton: true, // 是否展示折叠到窗口按钮
              onCollapseToWindowButtonClick: () => {
                console.log('点击折叠到窗口按钮');
              }
            },
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}
          composerConfig={{
            showInternetSearch: true,
            companyDataConfig: {
              hasGrey: true,
              hasRuipingGrey: true,
              // 默认选中传这个
              selectedAgents: ['yewen', 'ruiping']
            }
          }}
          showFeedbackModal={true}
          showToken={false}
          // feedbackModalConfig={{
          //   title: 'string',
          //   inputPlaceholder: 'string',
          //   showLabels: true,
          // }}
          renderCardContent={renderCardContent}
        />
      </div>
    </DemoPage>
  );
};
