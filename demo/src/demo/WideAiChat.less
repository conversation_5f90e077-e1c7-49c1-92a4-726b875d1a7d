.wide-screen-demo {
  .WideAiChatLayout {
    .WideAiChatContent {

      .Message-avatar-img {
        height: 30px;
        width: 30px;

        > img {
          height: 30px;
          width: 30px;
        }
      }
    }
  }
  .feedback {
    display: flex;
    height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #7c8db5;
    line-height: 20px;
    margin-top: 15px;
    margin-bottom: 16px;
    cursor: pointer;
    padding: 0px 30px;
    > img {
      margin-right: 14px;
    }
  }
  .quickNav {
    flex: 180px 0 0;
    padding-top: 30px;
    padding-left: 30px;
    padding-right: 30px;
    .new_session_btn {
      width: 100%;
      .new_session_btn_wrap {
        display: flex;
        justify-content: center;
        align-items: center;
        > img {
          margin-right: 10px;
        }
      }
    }
  }
}
