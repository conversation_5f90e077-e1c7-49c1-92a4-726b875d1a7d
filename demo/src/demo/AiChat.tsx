/* eslint-disable import/no-extraneous-dependencies */
import React, { useCallback } from 'react';
// import { log } from '@ht/xlog'; // 需要init
// import  {API_PREFIX} from '@lowcode/lc-render';
import LCRender from '@lowcode/lc-render';
import { DemoPage } from '../components';
import AiChat, {
  // MessageProps,
  // MessageWithoutId,
  // useMessages,
} from '../../../src';
import LogoPng from './images/deepseekChat.png';

//需要注入魔方渲染组件全部变量提供给组件内部引用
window.LCRender = LCRender;

// 魔方卡片数据示例
// let data = {

//   "cardBizType": "447e0542ca8f79b1cbbc",
//   "cardCategory": "lowcode-card",
//   "context": {
//     "tradingCode": "000001"
//   },
//   "conversationId": "ec80fa0c2c6e44869fce1373d44097ff",
//   "createTime": 1731651751569,
//   "lowCodeDetailMeta": {
//     "appId": "bd908367baee85cf3e7f"
//   },
//   "messageId": "efaec0798f2a4006a1373bc22bb93f05",
//   "snapshotId": "b2e82c3271ca4b08a1da5fde6af00443",
//   "view": {
//     "annReturnTot": 7.85722786,
//     "companyQuality": {
//       "managerName": "基金管理人", "rankPercent": 69.0
//     },
//     "exchangeCode": "OF",
//     "existing": true,
//     "htTypeCodeii": "002002",
//     "htTypeNameii": "灵活配置基金",
//     "opinionDate": 1726761600000,
//     "secuabbr": "华夏成长混合A",
//     "secucode": 53110,
//     "simpleEvaluateResults": [{
//       "conclusion": "历史收益能力一般", "rankPercent": 34.0
//     },
//     {
//       "conclusion": "风险控制能力一般", "rankPercent": 41.0
//     },
//     {
//       "conclusion": "择券配置能力较差", "rankPercent": 14.0
//     },
//     {
//       "conclusion": "投资独立性优秀", "rankPercent": 100.0
//     }
//     ],
//     "totalScoreScript": {
//       "highlightKeyWords": ["33.42", "较差"], "lowlightKeyWords": [], "script": "本基金近1年综合得分为33.42，超过12%的同类基金，总体表现较差"
//     },
//     "tradingCode": "000001"
//   }
// };


// const initialMessages: MessageWithoutId[] = [
//   {
//     type: 'text',
//     content: { text: 'Hi，我是你的专属智能助理小蜜，有问题请随时找我哦~' },
//     // user: { avatar: '//gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg', name: '小小蜜' },
//     createdAt: Date.now(),
//     hasTime: true,
//   },
//   {
//     type: 'richtext',
//     content: {
//       // text: '<h2>链接</h2><p>请点击以下链接访问外部网站：</p><a href="https://www.example.com">示例链接</a> <h2>图片</h2><p>这是一张示例图片：</p><img src="https://s3.cn-north-1.amazonaws.com.cn/s3-000045-cfglpic/3c792586-3d29-4ea9-af30-1c9363f2b62d_%E8%B6%85%E7%BA%A7ETF%E4%BA%A7%E5%93%81%E4%BB%8B%E7%BB%8D%E8%AF%BE%E7%A8%8B%E5%B0%81%E9%9D%A2.jpg" alt="示例图片" style="width: 300px; height: 300px;"><h2>PDF文件</h2><p>请点击以下链接下载PDF文件：</p><a href="../assets/test.pdf">示例PDF文件</a>',
//       text: '<h1>欢迎来到富文本世界</h1><p>这是一个包含多种标签的富文本示例内容。</p><h2>标题2</h2><p>这里是标题2的内容。</p><h3>标题3</h3><p>这里是标题3的内容。</p><a href="https://www.example.com">这是一个链接</a><p>这是一个包含图片的段落。</p><img src="http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg" alt="示例图片">'
//       // text: '<p>文本文本文本</p><p><a href="http://www.baidu.com" rel="noopener noreferrer" target="_blank">链接链接链接</a></p><p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAG4AAABVCAIAAAAnhEbCAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAEXRFWHRTb2Z0d2FyZQBTbmlwYXN0ZV0Xzt0AAApfSURBVHic3Vx7bBzFGf9md+986wclFiQUSuLgi3mE4hoQLYGqIXZSqShQCqJtiKD8Uak0DUG0SA1qQ1pFKJSKtErVF02JIpUmDQRCLVClSm2JEwghqkoSEscJVE1ArfKwHdtn393uTv+Y29l57d3e3e56zz9Z1nl2dueb337zvWbOCGMMMxFkXohtAQAAhBDGGLktCCHav/QJsTdVATQjqZQnJXDHsikD1cSmVsM9CQerjwghJS8IoZKSMj/0Um3jzkAqAQDx6kaJA0ZhSSP7A3XwCDOSSk/jKvFCO5AP9fAIAEY9NycWAUnBGAts1oMZQiXrZ2RSlFdJI8tmnZgJC5z6GfKjjEmQ6pZw0fBUUh4pN35sCreUOoekktDoVFJSqFeWPbXfLRAqj9DoVIJP/CgzhKX2cHmEhqay5Ddqujd0HqGhqQwex5SSxTDi8DJo7GAoePxY7S01oIG1MiDi4REai8rgwSBbAaKNkfIIDUQlTU6C94+TR2gUKqtNTgTaYuARGoLK2pSr/vpjtUg6lX48KvWU1CbIhUjjHiUSvSHB5tdCRYdASS7ZvYF4eYSEUwn8JozHEb2q2ufCsZNIkPQFLusdl0dLK30a9SLpVAKUdlPpTlYp/2P2uTw2w9hXqBmJW+C+Zs4VlLvkVjSmiz4WydJKr/4ov2BJ43DCtCBZVAK7kOVjAbzqJUETWSSISi7KkVrknvXUK6NAgqgEN4RkK94ym6WWePPrIEhWvRIzHxBlVt5ftWycz2MNIdNU8ogvXJj63VYAMB9/NGKRPUTrwa1DR/IvbKvnCdrcKzNrVgGvevn+NyYeedT49PUXvf6K8i7n9Ecjty4GgPZTQ/WMXhWi1Urn44/zO3fV8wSjp9tcs0rQzYSsaAFxLPD08i+Zq77Ftjjnh613D6b6liDN11g754fHVjxEPvuu9CQhDiq19nZ94bXe35adW/lwce9bqLU1882H/e5CZ86QDxXsZjKAMZ4Gt5N7+hnCI8o05f/4J7mDviBr3Hwj24JVW9vJAamkxE3l1PadU8+/AAB4fHziyaeUfTIPrWSpJAWh+OuPAUF4xLFSiXHuF7+e+slzAJBe2ms+8RgpSVB2in/9W27jT1FLc/qeu4RbSwfIeR7xxETxrf3qoVzjIHfQZl2sX3N1/bMpDeTyCPFRaVkTa9flt+/UrrjcuPnGwu5+/frrzMe+Q9nMv/xq7tlNyDTbtm0xbuo"></p>'
//      },
//   },
//   {
//     type: 'markdown',
//     content : {
//       text: `### 基础语法

// 1.**标题**

// # 标题 1
// ## 标题 2
// ### 标题 3
// #### 标题 4
// ##### 标题 5
// ###### 标题 6

// 2.**段落和换行**

// 段落之间留空行
// 可以在行尾加两个空格来强制换行。

// 3.**列表**

// - 无序列表

// - 项目 1
// - 项目 2
//   - 子项目 2.1
//   - 子项目 2.2
// - 项目 3

// - 有序列表

// 1. 项目 1
// 2. 项目 2
//    1. 子项目 2.1
//    2. 子项目 2.2
// 3. 项目 3

// 4.**引用**

// > 这是引用的文本。

// 5.**粗体和斜体**

// **粗体文本**
// *斜体文本*

// 6.**链接**

// [链接文本](https://tongyi.aliyun.com/)

// 自动识别连接： https://chatbot.console.aliyun.com/ChatSDK
// 链接截断验证：https://chatbot.console.aliyun.com/ChatSDK?test=测试，结束

// 7.**图片**

// ![替代文本](https://dashscope-cn-beijing.oss-cn-beijing.aliyuncs.com/code-interpreter/temp_files/code-output-20240131-120401.943328.png?OSSAccessKeyId=LTAI5tKHB4j1Cmo6kRtd5Ac8&Expires=1706704441&Signature=WWyDLwits154vO%2FaU3BFtkBc5fg%3D)


// 这是一个图文混排的例子
// ![替代文本](https://gw.alicdn.com/imgextra/i1/O1CN01TinEt01x1vHLqHZGz_!!6000000006384-2-tps-1280-960.png)


// 8.**PDF文件**

// Prompt的类别有哪些？
// ![如何写好Prompt.pdf](https://files.alicdn.com/tpsservice/f6648a7e019575dfa4b708f0c635b4f2.pdf)

// 9.**水平线**

// ---

// 10.**代码**

// - 行内代码

// 这是 \`行内代码\` 的例子。

// - 代码块

// \`\`\`javascript
// function example() {
//   console.log("Hello, World!");
// }
// \`\`\`

// 11.**任务列表**

// - [x] 已完成任务
// - [ ] 未完成任务

// 12.**表情符号**

// :smile: :heart: :rocket:

// 13.**注释**

// <!-- 这是注释 -->

// 14.**定义型列表**

// 苹果
// : 一种水果，有很多种品种。

// 桔子
// : 另一种水果，橙色的。

// 15.**LaTeX 公式**

// $E=mc^2$

// 16.**下标和上标**

// H~2~O，X^2^

// 17.**自动链接**

// <https://www.example.com>

// 18.**内部链接**

// [跳到文档底部](#文档底部)

// 19.**代码注释**

// \`代码注释\`

// 20.**支持HTML**

// <details>
//   <summary>点击展开</summary>
//   这是一个可以展开的内容。
// </details>

// 21.**定义标题 ID**

// ### 标题 {#custom-id}

// 22.**表格**

// | 姓名   | 年龄 | 职业         |
// |--------|------|--------------|
// | 张三   | 25   | 工程师       |
// | 李四   | 30   | 设计师       |

// 23. **at**

// 在Markdown中使用@符号来表示"at"的意思。
// 注意Markdown组件需要透传一个mentionList属性，该属性是一个数组，包含所有需要@的人或团队的名称。

// hello @tongyi-ui

// @OKR设定专家 @3D头像设计师 @🙈齐天大圣 @资深作家

// 24. **识别html链接**

// <a href="tel:0516-96777" target="_self">0516-96777</a>`
//     },
//   },
//   //魔方卡片
//   {
//     type: 'card',
//     content: {
//       url: 'http://lowcode.fe.htsc/app/447e0542ca8f79b1cbbc/editor',
//       data: data
//     },
//   },
//   {
//     type: 'image',
//     content: {
//       picUrl: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg',
//     },
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是一个word文件这是一个word文件.docx",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/外籍股权激励账户开立.docx",
//       "size": 102400,
//     }
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是第二个word文件.docx",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E5%A4%96%E7%B1%8D%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E8%B4%A6%E6%88%B7%E5%BC%80%E7%AB%8B.docx",
//       "size": 102400,
//     }
//   },

//   {
//     type: 'file',
//     content: {
//       "name": "这是一个PDF文件信诚新双盈分级债券型证券投资基金更新招募说明书摘要.pdf",
//       "url": "https://aorta.htzq.com.cn/pdf_finchina/FUND/2020/2020-5/2020-05-28/1922768.pdf",
//       "size": 102400,
//     }
//   },
//   {
//     type: 'file',
//     content: {
//       "name": "这是一个PDF文件这是Tailor服务记录.pdf",
//       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E4%BB%80%E4%B9%88%E6%98%AF%E6%9C%8D%E5%8A%A1%E8%AE%B0%E5%BD%95.pdf",
//       "size": 102400,
//     }
//   },

// ];


export default () => {
  // 消息列表
  // const { messages, appendMsg, updateMsg, prependMsgs, setTyping, getTyping, getMessages } = useMessages(initialMessages);
  // const { quickReplies } = useQuickReplies(defaultQuickReplies);
  const msgRef = React.useRef(null);

  // window.appendMsg = appendMsg;
  // window.updateMsg = updateMsg;
  // window.prependMsgs = prependMsgs;
  // window.setTyping = setTyping;
  // window.getTyping = getTyping;
  // window.getMessages = getMessages;
  // window.msgRef = msgRef;

  const config = {
    //租户id：表示当前系统
    appId: 'aorta',

    //用户id：代表当前系统唯一用户id
    userId: '002332',

    //场景id
    sceneId: 'scene',

    isDev: 'isDev',

    //魔方卡片配置，移动端需要传
    lowCode: {
      //rootValue，,用于app适配
      // rootValue: 37.5,
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
      // requestHandlerType: 'aortaReq',
      host: 'AROTA',
    },

    //接口请求 method默认post
    requests: {

      /**
      * 基础URL
      */
      baseUrl: '',

      //初始引导接口
      init: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',
        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },
        // requestTransfer: (input: object) => {
        //   return new Promise((resolve, reject) => {
        //     try {
        //       const parsedInput = {
        //         ...input,
        //         customerInput1: '123',
        //       }
        //       resolve(parsedInput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        // responseTransfer: (output: object) => {
        //   return new Promise((resolve, reject) => {
        //     const parsedOutput = {
        //       ...output,
        //       customeroutput1: '123',
        //     }
        //     try {
        //       resolve(parsedOutput);
        //     } catch (error) {
        //       reject(error);
        //     }
        //   });
        // },
        requestTransfer: (input: object) => {
          const parsedInput = {
            ...input,
            customerInput1: '123',
          }
          return parsedInput;
        },
        responseTransfer: (output: object) => {
          const parsedOutput = {
            ...output,
            customeroutput1: '123',
          }
          return parsedOutput;
        },
      },

      //问答接口
      send: {
        url: 'http://aorta.uat.saas.htsc/aorta/ai-bag/workflow/main-v1',
        // url: 'http://10.102.77.187:8000/api/june/chat/stream',
        stream: true,
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //查询历史接口
      history: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryMessagesInConversation',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        // pageSize: 6,
      },

      //快捷问题接口
      // quickReply: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryQuickReplies',
      //   headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      // },

      //点赞点踩接口
      score: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/likeOrDislikeAMessage',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
      },

      //停止生成接口
      // stop: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentCmd/stopGeneratingAnswerAortaAI',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },

      //关联问题接口
      related: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/TecAIAgentQuery/queryRelatedQuestions',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },

      // 历史会话列表
      historyConversation: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryHistoryConversations',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
        // type: 'tcp',
        // paramsKey: 'MS__REQUEST__PAYLOAD',
        // action: '53421',
      },
      // 阅读风险提示
      riskRead: {
        url: '/mcrm/api/groovynoauth/chatBar/updateUserBehaviorByType',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // 查询是否阅读了风险提示
      queryRiskRead: {
        url: '/mcrm/api/groovynoauth/chatBar/queryUserBehaviorByType',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },
      // 敏感词校验
      // sensitive: {
      //   url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/checkSensitiveContent',
      //   headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      // },
      // 点踩反馈标签列表
      feedbackTagList: {
        url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryAllAiTag',
        headers: { empid: '002332', 'iv-user': '002332', token: 'token', 'deviceId': 'deviceId' },
      },

    },
    bridge: {// 原生bridge
      // 打开新页面
      openWebPage: useCallback((url: any) => {
        console.log('打开新页面', url)
      }, []),
      // 调用原生方法打开页面预览文件支持word\pdf
      // openFileViews: (url: any) => {
      //   console.log('预览文件', url)
      // },
      // 复制文本的方法
      copyText: useCallback((text: string) => {
        console.log('复制文本', text);
      }, []),
    },
    // pushPosition: 'left',
    // pushPercent: 50,
    // showInternetSearch: true,
    /**
     * 加载时是否默认展示新会话（为false则直接进入对话中页面）
     */
    // showNewConversation: false,
    waterMark: {
      show: true,
      text: `宋娟       010497`,
    },
    // robot: {
    //   logo: LogoPng
    // }
  }

  const navbarProps = {

    // open?: boolean,//是否展示navbar，默认true  

    // 返回按钮设置项
    // showReturnButton?: boolean,//是否展示返回按钮,默认pc不展示，app展示
    // returnButtonIcon?: string;//返回按钮图标路径
    // onReturnButtonClick?: ()=>void;//点击返回按钮响应处理函数

    // 标题区域设置
    // showLogo?: boolean,//是否展示logo,默认为true            
    // logoIcon?: string, // logo图标的地址 
    // title?: string; //头部标题文案，展示于logo右侧,默认为空字符串
    // logoAndTitlePosition?:'left' | 'center';//标题区域的位置：pc端默认靠左边，移动端默认居中

    // 历史会话按钮设置项
    // showHistoryButton?: boolean,//是否展示历史会话按钮，默认为true
    // historyButtonIcon?: string,//历史会话按钮图标路径
    // historyButtonPosition?: 'left' | 'right' ,//'left'-放左边（位于返回按钮右边），'right'-放在右边，默认在右边，如果配了left，则默认从左边推出，关联历史对话配置的pushPosition
    // onHistoryButtonClick?: () => void;//点击历史会话按钮响应处理函数

    //新建会话按钮设置项
    // showNewButton?: boolean;//是否显示新建会话按钮，默认为true
    // newButtonIcon?: string,//新建会话按钮图标路径               
    // onNewButtonClick?: () => void; //点击新建会话按钮响应处理函数

    // 关闭按钮设置项
    // showCloseButton?: boolean;//是否显示关闭按钮，pc端默认true，移动端默认false
    // closeButtonIcon?: string;//关闭按钮图标路径
    // onCloseButtonClick?: () => void;//关闭按钮点击响应处理函数 
    // wrapstyle: {
    //   paddingTop: '20px',
    // }
    logo: LogoPng,
  }

  // const renderWelcome = () => {
  //   return (
  //     <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', height: '100%' }}>这是欢迎页</div>
  //   )
  // }
  // const renderQuickReplies = () => {
  //   return (
  //     <div style={{ marginLeft: 20, marginBottom: 10 }}>11111</div>
  //   )
  // }

  const onReportLog = useCallback((params: any) => {
    // log(params); // 需要init
    console.log(params);
  }, []);

  const RenderHistoryConversationFooter = () => {
    return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的底部自定义模块</div>
  }

  const RenderHistoryConversationBrand = () => {
    return <div style={{ height: '30px', marginLeft: '6px', marginTop: '5px', border: '1px solid grey', color: '#333' }}>历史会话列表的品牌区域自定义模块</div>
  }

  // const initialMessages = [
  //   {
  //     type: 'image',
  //     content: {
  //       picUrl: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg',
  //     },
  //   },
  //   {
  //     type: 'file',
  //     content: {
  //       "name": "这是一个word文件这是一个word文件.docx",
  //       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/外籍股权激励账户开立.docx",
  //       "size": 102400,
  //     }
  //   },
  //   {
  //     type: 'file',
  //     content: {
  //       "name": "这是第二个word文件.docx",
  //       "url": "https://c.zhangle.com/airobottest/iwen/material/docs/%E5%A4%96%E7%B1%8D%E8%82%A1%E6%9D%83%E6%BF%80%E5%8A%B1%E8%B4%A6%E6%88%B7%E5%BC%80%E7%AB%8B.docx",
  //       "size": 102400,
  //     }
  //   },

  //   {
  //     type: 'file',
  //     content: {
  //       "name": "这是一个PDF文件信诚新双盈分级债券型证券投资基金更新招募说明书摘要.pdf",
  //       "url": "https://aorta.htzq.com.cn/pdf_finchina/FUND/2020/2020-5/2020-05-28/1922768.pdf",
  //       "size": 102400,
  //     }
  //   },
  // ];
  const renderCardContent = useCallback((data) => {
    console.warn('renderCardContent', data);
    return <div>睿评卡片{data?.[0]?.type}-{data?.[0]?.content?.fileTitle}</div>
  }, []);

  return (
    <DemoPage>
      <div style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }} id="chat-container">
        <AiChat
          navbar={navbarProps}
          messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          // initialMessages={initialMessages}
          config={config}
          onReportLog={onReportLog}
          // quickReplies={[{
          //   title: 'string',//页面展示的快捷问题
          //   content: 'realString',//点击后实际发送的问题
          //   url: '',//如果配置，点击后跳转链接
          //   isHighlight: true,
          //   isNew: true,
          //   img: 'http://gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg'
          // }]}

          historyConversation={{
            // pushPosition?: 'left' | 'right';   //推的方向，左推还是右推              
            // pushPercent?: number; //推多宽，50 表示半屏，100表示全屏，默认75
            title: '历史会话列表', //历史会话列表标题
            logo: LogoPng,
            // logo: string,  //历史会话列表图标
            // renderTitle?:  function // 支持自定义标题区域   
            // showSearchArea?: boolean//是否展示搜索区域，默认为true
            // searchPlaceholder?: string, //搜索placeholder设置，默认为“请输入搜索关键字”
            renderBrand: RenderHistoryConversationBrand, //支持传入自定义brand区域
            renderFooter: RenderHistoryConversationFooter, //支持传入自定义底部区域  
            showSearch: false,
          }}

          welcome={{
            // open?: boolean //是否展示欢迎页，默认true
            // logo?: string //logo图标的地址
            // itle?: string //欢迎页主标题，默认："智能助手为您服务~",
            // subtitle: string //欢迎页副标题，默认："作为您的智能助手，我可以为您答疑解惑",
            // riskTip: string //欢迎页底部风险提示，默认："内容由AI大模型生成，请谨慎识别"
            title: 'Hi～我是 问TA',
            subtitle: '作为您的智能助手，可以为您答疑解惑',
            // showsubTitle: false,
            logo: '',
            openMessages: [{type:'list', content: '宁德时代的近期走势'},{type:'list', content: '如何做好黄金投资？'},{type:'list', content: '今日金价？'}],
            navbar: {
              // 配置和上面的navbarProps相同，优先在欢迎页生效
              title: '欢迎页标题',
              // wrapstyle: {
              //   paddingTop: '20px',
              // },
            },
            // renderNavbar: () => {
            //   return (
            //     <div>自定义——欢迎页navbar</div>
            //   )
            // }
          }}
          composerConfig={{
            showInternetSearch: true,
            companyDataConfig: {
              hasRuipingGrey: true,
              hasGrey: true,
              // 默认选中传这个
              selectedAgents: ['yewen', 'ruiping']
            }
          }}
          showFeedbackModal={true}
          showToken={false}
        // feedbackModalConfig={{
        //   title: 'string',
        //   inputPlaceholder: 'string',
        //   showLabels: true,
          // }}
          renderCardContent={renderCardContent}
        />
      </div>
    </DemoPage>
  );
};
