import React from 'react';
import { DemoPage, DemoSection } from '../components';
import { ThinkContent } from '../../../src';

const testThinkText = `### 这是思考内容，支持markdown，也可以传普通文本

1.**标题**

# 标题 1
## 标题 2
### 标题 3
#### 标题 4
##### 标题 5
###### 标题 6`;

const thinkLinks = [
  {
    title: '问题分析',
    isThinking: false,
    thinkContent: '嗯，我理解用户的问题包含多个方面。首先用户是希望知道股票期权账号的规则，我应该优先根据用户提供的知识库查询答案。同时用户希望就期权市场的最新资讯以及中美贸易战对期权市场的影响进行点评分析，针对这点，我应该进行使用用户提供的资讯内容进行相关资料查询并总结。'
  },
  {
    title: '业务知识查询',
    isThinking: true,
    thinkContent: '用户问的问题包含股票期权账号的开户流程，以及开户要求两部分，我需要对此分别进行回答。首先，我需要回忆一下开通股票期权账户的条件。记得之前了解过，国内券商通常要求投资者有融资融券资格，并且需要一定的交易经验，比如半年以上。此外，可能还需要通过知识测试，模拟交易经验，以及资金门槛。'
  },
  {
    title: '资讯查询',
    isThinking: false,
    thinkContent: '客户问到期权市场相关资讯信息'
  },
  {
    title: 'DS大模型查询',
    isThinking: false,
    thinkContent: testThinkText,
  }
];
export default () => (
  <DemoPage>
    <DemoSection title="思维链-思考完成，允许折叠">
      <ThinkContent thinkLinks={thinkLinks} />
    </DemoSection>
    <DemoSection title="思维链-思考中，不允许折叠">
      <ThinkContent thinkLinks={thinkLinks}  isThinking />
    </DemoSection>
  </DemoPage>
);
