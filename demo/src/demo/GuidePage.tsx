import React from 'react';
// import { DemoPage } from '../components';
import {
  GuidePage,
  MessageProps,
  useMessages,
} from '../../../src';

type MessageWithoutId = Omit<MessageProps, '_id'>;


const initialMessages: MessageWithoutId[] = [
  // {
  //   type: 'system',
  //   content: { text: '88VIP专属智能客服小蜜 为您服务' },
  // },
  {
    type: 'text',
    content: { text: 'Hi，我是你的专属智能助理小蜜，有问题请随时找我哦~' },
    // user: { avatar: '//gw.alicdn.com/tfs/TB1DYHLwMHqK1RjSZFEXXcGMXXa-56-62.svg', name: '小小蜜' },
    createdAt: Date.now(),
    hasTime: true,
  },
  // {
  //   type: 'guess-you',
  // },
  // {
  //   type: 'markdown',
  //   content: {
  //     text:'# This is a header\n\nAnd this is a paragraph;'
  //     // text: 'Markdown 表格示例如下：\n|左对齐|右对齐|居中对齐|\n|:---|----:|:----:|\n|单元格|单元格|单元格|'
  //   },
  // },
  // {
  //   type: 'lowcode',
  //   content: {
  //     url:'http://lowcode.fe.htsc/app/447e0542ca8f79b1cbbc/editor',
  //     data:data
  //   },
  // },
  // {
  //   type: 'image',
  //   content: {
  //     picUrl: '//img.alicdn.com/tfs/TB1p_nirYr1gK0jSZR0XXbP8XXa-300-300.png',
  //   },
  // },
];



export default () => {
  // 消息列表
  const { messages, appendMsg, updateMsg, setTyping } = useMessages(initialMessages);
  // const { quickReplies, replace } = useQuickReplies(defaultQuickReplies);
  const msgRef = React.useRef(null);

  window.appendMsg = appendMsg;
  window.updateMsg = updateMsg;
  window.setTyping = setTyping;
  window.msgRef = msgRef;

  const  config = {
    //租户id：表示当前系统
    appId :'aorta',

    //用户id：代表当前系统唯一用户id
    userId:'empid',

    //场景id
    sceneId:'scene',

    //接口请求 method默认post
    requests: {

    /**
    * 基础URL
    */
    baseUrl: 'http://mock.htsc',

    //初始引导接口
    init: { url:'xxx'},

    //问答接口
    send: { url:'/goapi/FSP/getAnswer', stream:false, headers:{empid:'002332',token:'token','deviceId':'deviceId'}},

    // //查询历史接口
    // history:  { url:'xxx' }

    // //快捷问题接口
    // quickreply :  { url:'xxx' }

    // //点赞点踩接口
    // score:  { url:'xxx' }

  }
}

  return (
    // <DemoPage>
    //   <div style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }}>
    <div style={{ height: '100%' }}>
        <GuidePage
          navbar={{
            type: 'pc',
            onClose: () => {
              console.log('这里写关闭当前页面的方法')
            }
          }}
          messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          wideBreakpoint="600px"
          messages={messages}
          config={config}
        />
      </div>
    // </DemoPage>
  );
};
