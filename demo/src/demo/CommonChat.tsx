import React, { useEffect } from 'react';
// import  {API_PREFIX} from '@lowcode/lc-render';
import { DemoPage } from '../components';
// import  '../dist/chat-sdk.js';    
// import '../dist/chat-sdk.css';
import AiChat, {
  // MessageProps,
  // useQuickReplies,
} from '../../../src';

// type MessageWithoutId = Omit<MessageProps, '_id'>;

// const initialMessages: MessageWithoutId[] = [];

export default () => {
  // 消息列表
  // const { quickReplies } = useQuickReplies(defaultQuickReplies);
  // const msgRef = React.useRef(null);

  const config = {
    //租户id：表示当前系统
    appId: 'aorta',

    //用户id：代表当前系统唯一用户id
    userId:'022650',

    //场景id
    sceneId: '0',

    // 标识业务平台
    source: 'chat-component',

    //魔方卡片配置，移动端需要传
    lowCode:{
      //rootValue，,用于app适配
      // rootValue: 37.5,   
      // 移动端需要重传该字段表示走站内tcp请求，不同app传入不同枚举值
      // requestMobile: 'AortaApp'
    },

    //接口请求 method默认post
    requests: {

      /**
      * 基础URL
      */
      baseUrl: '',

      //初始引导接口
      init: {
        url: '/api/groovy/ai/adapter/initialGuide',
        platform: 'custom',
      },

      //问答接口
      send: {
        url: '/api/groovy/ai/adapter/chat',
        platform: 'custom',
      },

      //查询历史接口
      history: {
        url: '/api/groovy/ai/adapter/historyRecord',
        pageSize: 3,
        platform: 'custom',
      },

      //快捷问题接口
      quickReply: {
        url: '/api/groovy/ai/adapter/quickNavigation',
        platform: 'custom',
      },

      //点赞点踩接口
      score: {
        url: '/api/groovy/ai/adapter/feedBack',
        headers: { empid: '002332', token: 'token', 'iv-user': '002332', 'deviceId': 'deviceId' },
        platform: 'custom',
      },
    },
    bridge: {
      openWebPage: (url: any) => {
        console.log('打开新页面', url)
      }
    }
  }
  //测试chatsdk
  useEffect(()=>{  
  //   const bot = new window.ChatSDK({
  //     container: document.getElementById('chat-container'), // 指定容器
  //     config: {
  //         config : config,
  //     },            
  //     });
  //   bot.run();
  })

  return (
    <DemoPage>
      <div style={{ height: 'calc(100vh - 48px)', marginTop: '-12px' }} id="chat-container">
        <AiChat
          navbar={{
            onClose: () => {
              console.log('这里写关闭当前页面的方法')
            }
          }}
          // messagesRef={msgRef}
          // recorder={{ canRecord: true }}
          // wideBreakpoint="600px"
          // initialMessages={messages}
          config={config}
          // quickReplies={quickReplies}
        />
      </div>
    </DemoPage>
  );
};
