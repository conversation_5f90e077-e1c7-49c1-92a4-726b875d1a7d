{"name": "@aorta/pwm-chatui", "version": "0.0.3-beta.4", "description": "The React library for Chatbot UI", "main": "lib/index.js", "module": "es/index.js", "browser": "dist/index.js", "style": "dist/index.css", "typings": "lib/index.d.ts", "files": ["dist", "es", "lib"], "scripts": {"clean": "rimraf dist es lib", "prebuild": "npm run clean", "prefix": "cross-env NODE_ENV=production postcss dist/index.css -o dist/index.css", "copy:less": "copyfiles -u 1 src/**/*.less src/**/**/*.less es", "js:cjs": "cross-env BABEL_ENV=cjs babel src -d lib --extensions '.ts,.tsx'", "js:esm": "cross-env BABEL_ENV=esm babel src -d es --extensions '.ts,.tsx'", "build:types": "tsc -p tsconfig.build.json", "build": "npm run js:cjs && npm run js:esm && npm run build:types", "build:css": "lessc src/styles/index.less dist/index.css && npm run prefix && npm run copy:less", "build:umd": "cross-env BABEL_ENV=umd rollup -c && npm run build:css", "prepublishOnly": "npm run build && npm run build:umd", "prepare": "husky install && node ./.husky/prepare.js"}, "dependencies": {"@babel/runtime": "^7.18.3", "@babel/runtime-corejs3": "^7.18.3", "@ht/h5-utils": "4.2.2", "@tanstack/react-virtual": "^3.13.9", "@types/react-syntax-highlighter": "^15.5.13", "clsx": "^1.1.1", "copy-to-clipboard": "^3.3.3", "core-js": "^3.23.1", "dompurify": "^2.3.8", "github-markdown-css": "^5.8.0", "intersection-observer": "^0.12.2", "moment": "^2.30.1", "rc-select": "^14.16.6", "rc-tooltip": "^6.4.0", "rc-upload": "^4.8.1", "rc-util": "^5.44.4", "react-image-lightbox": "^5.1.4", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.6.1", "rehype-external-links": "^3.0.0", "rehype-katex": "^6.0.2", "rehype-raw": "^6.0.0", "remark-gemoji": "^8.0.0", "remark-gfm": "^3.0.1", "remark-math": "^6.0.0"}, "resolutions": {"@types/react": "^17.0.45", "string-width": "4.2.3"}, "devDependencies": {"@babel/cli": "^7.17.10", "@babel/core": "^7.18.5", "@babel/plugin-proposal-class-properties": "^7.17.12", "@babel/plugin-transform-runtime": "^7.18.5", "@babel/preset-env": "^7.18.2", "@babel/preset-react": "^7.17.12", "@babel/preset-typescript": "^7.17.12", "@commitlint/cli": "^17.0.2", "@commitlint/config-conventional": "^17.0.2", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-url": "^8.0.2", "@types/dompurify": "^2.3.3", "@types/react": "^17.0.45", "@types/react-dom": "^17.0.17", "@types/resize-observer-browser": "^0.1.7", "@typescript-eslint/eslint-plugin": "^5.28.0", "@typescript-eslint/parser": "^5.28.0", "autoprefixer": "^10.4.7", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "cssnano": "^5.1.11", "eslint": "^8.17.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-compat": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "7.0.4", "less": "^4.1.3", "postcss": "^8.4.14", "postcss-cli": "^9.1.0", "postcss-pxtorem": "^6.0.0", "react": "17.0.2", "react-dom": "17.0.2", "rollup": "^2.75.6", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.7.3"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "browserslist": [">0.2%", "Android >= 4.4", "not dead", "not op_mini all", "chrome >= 42", "ios >= 9", "ie >= 11"], "keywords": ["react", "react-component", "chat", "chat-ui"], "homepage": "http://web.npm.htsc/package/@aorta/pwm-chatui", "bugs": {"url": "http://gitlab.htzq.htsc.com.cn/pwm/pwm-chat/-/issues"}, "repository": {"type": "git", "url": "http://gitlab.htzq.htsc.com.cn/pwm/pwm-chat"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}